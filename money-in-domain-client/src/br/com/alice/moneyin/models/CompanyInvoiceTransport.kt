package br.com.alice.moneyin.models

import br.com.alice.common.BeneficiaryType
import br.com.alice.data.layer.models.CancellationReason
import br.com.alice.data.layer.models.InvoiceBreakdownItem
import br.com.alice.data.layer.models.InvoiceItem
import br.com.alice.data.layer.models.InvoiceStatus
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class CompanyInvoiceResponse(
    val id: UUID,
    val externalId: String?,
    val status: CompanyInvoiceStatus,
    val billingAccountablePartyId: UUID,
    val referenceDate: LocalDate,
    val validityPeriod: CompanyInvoiceValidityPeriod,
    val multipleValidityPeriods: List<CompanyInvoiceValidityPeriod> = emptyList(),
    val companySubContractIds: List<UUID>,
    val companyIds: List<UUID>,
    val type: CompanyInvoiceType,
    val model: CompanyInvoiceModel,
    val totalAmount: BigDecimal,
    val discount: BigDecimal,
    val addition: BigDecimal,
    val dueDate: LocalDate,
    val quantityMemberInvoices: Int,
    val memberInvoiceIds: List<UUID>,
    val relationships: List<CompanyInvoiceRelationship>,
    val globalItems: List<InvoiceItem>,
    val installments: Int,
    val totalInstallments: Int,
    val version: Int,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
    val payments: List<CompanyInvoicePayment> = emptyList(),
    val memberInvoiceDetails: List<MemberInvoiceDetail> = emptyList(),
) {
    val isMemberInvoiceGroup get() = this.model.isMemberInvoiceGroup

    val isPreActivationPayment get() = this.model.isPreActivationPayment

    val isLiquidation get() = this.model.isLiquidation
}

data class MemberInvoiceDetail(
    val invoiceId: UUID?,
    val memberInvoiceId: UUID,
    val beneficiary: MemberInvoiceBeneficiaryDetail,
    val amount: BigDecimal,
    val status: InvoiceStatus,
    val canceledReason: CancellationReason? = null,
    val referenceDate: LocalDate,
    val dueDate: LocalDateTime,
    val paidAt: LocalDateTime? = null,
    val invoiceBreakdownItems: List<InvoiceBreakdownItem>? = null,
)

data class MemberInvoiceBeneficiaryDetail(
    val memberId: UUID,
    val firstName: String,
    val lastName: String,
    val nationalId: String,
    val nationalIdHolder: String? = null,
    val type: BeneficiaryType,
    val productTitle: String,
    val productDisplayName: String? = null,
    val isMemberActive: Boolean,
)

data class CompanyInvoicePayment(
    val id: UUID,
    val invoiceId: UUID? = null,
    val amount: BigDecimal,
    val status: CompanyInvoicePaymentStatus,
    val method: CompanyInvoicePaymentMethod,
    val externalId: String? = null,
    val reason: CompanyInvoicePaymentReason? = null,
    val pix: PixDetail? = null,
    val bankSlip: BankSlipDetail?,
    val creditCard: CreditCardDetail?,
    val approvedAt: LocalDateTime? = null,
    val canceledReason: CompanyInvoicePaymentCancellation? = null,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
)

data class PixDetail(
    val paymentUrl: String? = null,
    val dueDate: LocalDateTime? = null,
    val paymentCode: String? = null,
    val externalId: String? = null,
)

data class BankSlipDetail(
    val dueDate: LocalDateTime,
    val barcode: String? = null,
    val paymentUrl: String? = null,
    val externalId: String? = null,
)

data class CreditCardDetail(
    val paymentUrl: String,
)

data class CompanyInvoiceValidityPeriod(
    val start: LocalDate,
    val end: LocalDate,
)

data class CompanyInvoiceRelationship(
    val invoiceId: UUID,
    val model: CompanyInvoiceModel,
)

enum class CompanyInvoiceType { FIRST_PAYMENT, RECURRENT, LIQUIDATION }

enum class CompanyInvoiceModel {
    MEMBER_INVOICE_GROUP, PRE_ACTIVATION_PAYMENT, INVOICE_LIQUIDATION;

    val isLiquidation get() = INVOICE_LIQUIDATION == this

    val isMemberInvoiceGroup get() = MEMBER_INVOICE_GROUP == this

    val isPreActivationPayment get() = PRE_ACTIVATION_PAYMENT == this
}

enum class CompanyInvoiceStatus(val description: String) {
    PROCESSING("Member invoice group criada"),
    WAITING_PAYMENT("Aguardando pagamento"),
    PROCESSED("Invoices e pagamentos gerados"),
    PAID("Member Invoice Group pago"),
    PARTIALLY_PAID("Member Invoice Group parcialmente pago"),
    CANCELED("Member invoice group cancelado"),
    CANCELED_BY_LIQUIDATION("Baixa por liquidação");
}

enum class CompanyInvoicePaymentStatus {
    PENDING, APPROVED, CANCELED, EXPIRED;
}

enum class CompanyInvoicePaymentMethod {
    BOLETO,
    SIMPLE_CREDIT_CARD,
    PIX,
    BOLEPIX;
}

enum class CompanyInvoicePaymentCancellation {
    INVALID, PAYMENT_PROCESSOR_CANCELED, OVERDUE, CANCELED_BY_REISSUE, CANCELED_BY_LIQUIDATION, SCHEDULED_CANCEL
}

enum class CompanyInvoicePaymentReason {
    REGULAR_PAYMENT,
    FIRST_PAYMENT,
    LIQUIDATION,
    PRE_ACTIVATION_PAYMENT
}
