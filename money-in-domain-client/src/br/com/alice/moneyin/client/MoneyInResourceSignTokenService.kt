package br.com.alice.moneyin.client

import br.com.alice.common.UUIDv7
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface MoneyInResourceSignTokenService : Service {
    override val namespace get() = "money_in"
    override val serviceName get() = "money_in_resource_sign_token"

    suspend fun createSignTokenForMoneyInBff(
        invoicePaymentId: UUID
    ): Result<UUIDv7, Throwable>

    suspend fun getSignTokenForMoneyInBff(
        invoicePaymentId: UUID
    ): Result<UUIDv7, Throwable>

    suspend fun getSignTokensForMoneyInBff(
        invoicePaymentIds: List<UUID>
    ): Result<List<Pair<UUID, UUIDv7>>, Throwable>

    suspend fun isSignTokenValidForMoneyInBff(
        invoicePaymentId: UUID,
        signUuid: UUIDv7
    ): Result<Boolean, Throwable>

    suspend fun softDeleteSignToken(
        resourceSignTokenId: UUID
    ): Result<Boolean, Throwable>
}
