package br.com.alice.moneyin.client

import br.com.alice.common.PaymentMethod
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.InvoicePaymentOrigin
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.PreActivationPayment
import br.com.alice.data.layer.models.PreActivationPaymentStatus
import br.com.alice.data.layer.models.PreActivationPaymentType
import com.github.kittinunf.result.Result
import java.time.LocalDate
import java.util.UUID

@RemoteService
interface PreActivationPaymentService : Service {
    override val namespace get() = "money_in"
    override val serviceName get() = "pre_activation_payment"

    suspend fun get(id: UUID): Result<PreActivationPayment, Throwable>

    suspend fun generateForB2B(
        companyId: UUID,
        companySubContractId: UUID,
        members: List<Member>,
        billingAccountablePartyId: UUID,
        referenceDate: LocalDate,
        dueDate: LocalDate,
        paymentMethod: PaymentMethod = PaymentMethod.BOLEPIX,
        paymentOrigin: InvoicePaymentOrigin = InvoicePaymentOrigin.UNDEFINED,
    ): Result<PreActivationPayment, Throwable>

    suspend fun markAsPaid(
        preActivationPayment: PreActivationPayment,
        invoicePayment: InvoicePayment
    ): Result<PreActivationPayment, Throwable>

    suspend fun cancelById(id: UUID): Result<PreActivationPayment, Throwable>

    suspend fun getBySubcontractId(
        subContractId: UUID,
        options: FindOptions = FindOptions()
    ): Result<List<PreActivationPayment>, Throwable>

    suspend fun listByCompanyId(companyId: UUID,  options: FindOptions = FindOptions()): Result<List<PreActivationPayment>, Throwable>

    suspend fun listByIds(preActivationPaymentIds: List<UUID>): Result<List<PreActivationPayment>, Throwable>

    suspend fun update(preActivationPayment: PreActivationPayment): Result<PreActivationPayment, Throwable>

    suspend fun getByBillingAccountablePartyId(billingAccountablePartyId: UUID): Result<List<PreActivationPayment>, Throwable>

    data class FindOptions(
        val status: List<PreActivationPaymentStatus>? = null,
        val type: List<PreActivationPaymentType>? = null,
    )
}
