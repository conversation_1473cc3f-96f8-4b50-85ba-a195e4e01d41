package br.com.alice.moneyin.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.moneyin.models.CompanyInvoiceModel
import br.com.alice.moneyin.models.CompanyInvoiceResponse
import br.com.alice.moneyin.models.CompanyInvoiceStatus
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface CompanyInvoiceService : Service {
    override val namespace get() = "money_in"
    override val serviceName get() = "company_invoice"

    suspend fun listByCompanyId(
        companyId: UUID,
        findOptions: FindOptions = FindOptions(),
    ): Result<List<CompanyInvoiceResponse>, Throwable>

    data class FindOptions(
        val withPayments: Boolean = false,
        val withMemberInvoiceDetails: Boolean = false,
        val status: List<CompanyInvoiceStatus> = emptyList(),
        val models: List<CompanyInvoiceModel> = emptyList()
    )

    suspend fun listByBillingAccountablePartyIdAndModelsWithPayments(
        billingAccountablePartyId: UUID,
        models: List<CompanyInvoiceModel> = emptyList(),
    ): Result<List<CompanyInvoiceResponse>, Throwable>

    suspend fun listBySubContractIdAndModelsWithPayments(
        subContractId: UUID,
        models: List<CompanyInvoiceModel> = emptyList(),
    ): Result<List<CompanyInvoiceResponse>, Throwable>

    suspend fun listByIdsAndModel(
        payload: ListByIdsAndModelPayload
    ): Result<List<CompanyInvoiceResponse>, Throwable>

    data class ListByIdsAndModelPayload(
        val invoiceIds: List<Pair<UUID, CompanyInvoiceModel>>,
        val withPayments: Boolean = false,
        val withMemberInvoiceDetails: Boolean = false,
    )

    suspend fun getByIdAndModel(payload: GetByIdAndModelPayload): Result<CompanyInvoiceResponse, Throwable>

    data class GetByIdAndModelPayload(
        val invoiceId: UUID,
        val model: CompanyInvoiceModel,
        val withPayments: Boolean = false,
        val withMemberInvoiceDetails: Boolean = false,
    )

}
