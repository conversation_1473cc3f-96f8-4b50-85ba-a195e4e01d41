package br.com.alice.moneyin.converters

import br.com.alice.common.PaymentMethod
import br.com.alice.common.core.extensions.atBeginningOfTheMonth
import br.com.alice.common.core.extensions.atEndOfTheMonth
import br.com.alice.common.core.extensions.money
import br.com.alice.data.layer.models.BolepixPaymentDetail
import br.com.alice.data.layer.models.BoletoPaymentDetail
import br.com.alice.data.layer.models.CancellationReason
import br.com.alice.data.layer.models.InvoiceLiquidation
import br.com.alice.data.layer.models.InvoiceLiquidationStatus
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.InvoicePaymentStatus
import br.com.alice.data.layer.models.MemberInvoice
import br.com.alice.data.layer.models.MemberInvoiceGroup
import br.com.alice.data.layer.models.MemberInvoiceGroupStatus
import br.com.alice.data.layer.models.PaymentDetail
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.data.layer.models.PixPaymentDetail
import br.com.alice.data.layer.models.PreActivationPayment
import br.com.alice.data.layer.models.PreActivationPaymentStatus
import br.com.alice.data.layer.models.SimpleCreditCardPaymentDetail
import br.com.alice.moneyin.models.BankSlipDetail
import br.com.alice.moneyin.models.CompanyInvoiceModel
import br.com.alice.moneyin.models.CompanyInvoicePayment
import br.com.alice.moneyin.models.CompanyInvoicePaymentCancellation
import br.com.alice.moneyin.models.CompanyInvoicePaymentMethod
import br.com.alice.moneyin.models.CompanyInvoicePaymentReason
import br.com.alice.moneyin.models.CompanyInvoicePaymentStatus
import br.com.alice.moneyin.models.CompanyInvoiceRelationship
import br.com.alice.moneyin.models.CompanyInvoiceResponse
import br.com.alice.moneyin.models.CompanyInvoiceStatus
import br.com.alice.moneyin.models.CompanyInvoiceType
import br.com.alice.moneyin.models.CompanyInvoiceValidityPeriod
import br.com.alice.moneyin.models.CreditCardDetail
import br.com.alice.moneyin.models.MemberInvoiceBeneficiaryDetail
import br.com.alice.moneyin.models.MemberInvoiceDetail
import br.com.alice.moneyin.models.PixDetail

object CompanyInvoiceConverter {
    fun MemberInvoiceGroup.toCompanyInvoiceResponse() = CompanyInvoiceResponse(
        id = this.id,
        externalId = this.externalId,
        status = this.status.toCompanyInvoiceStatus(),
        referenceDate = this.referenceDate,
        billingAccountablePartyId = this.billingAccountablePartyId,
        validityPeriod = CompanyInvoiceValidityPeriod(
            start = this.referenceDate.atBeginningOfTheMonth(),
            end = this.referenceDate.atEndOfTheMonth()
        ),
        companySubContractIds = listOf(this.companySubcontractId!!),
        companyIds = listOf(this.companyId!!),
        type = if (this.type?.isFirstPayment() == true) CompanyInvoiceType.FIRST_PAYMENT else CompanyInvoiceType.RECURRENT,
        model = CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
        totalAmount = this.totalAmount ?: 0.money,
        discount = 0.money,
        addition = 0.money,
        dueDate = this.dueDate,
        quantityMemberInvoices = this.quantityMemberInvoices ?: this.memberInvoiceIds.size,
        memberInvoiceIds = this.memberInvoiceIds,
        relationships = this.getRelationships(),
        globalItems = this.globalItems ?: emptyList(),
        installments = 0,
        totalInstallments = 0,
        version = this.version,
        createdAt = this.createdAt,
        updatedAt = this.updatedAt,
    )

    private fun MemberInvoiceGroup.getRelationships() = this.preActivationPaymentId?.let {
        listOf(
            CompanyInvoiceRelationship(
                it,
                CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT,
            )
        )
    } ?: this.invoiceLiquidationIds?.let { ids ->
        ids.map {
            CompanyInvoiceRelationship(
                it,
                CompanyInvoiceModel.INVOICE_LIQUIDATION
            )
        }
    } ?: emptyList()

    private fun MemberInvoiceGroupStatus.toCompanyInvoiceStatus() = when (this) {
        MemberInvoiceGroupStatus.PARTIALLY_PAID -> CompanyInvoiceStatus.PARTIALLY_PAID
        MemberInvoiceGroupStatus.PAID -> CompanyInvoiceStatus.PAID
        MemberInvoiceGroupStatus.WAITING_PAYMENT -> CompanyInvoiceStatus.WAITING_PAYMENT
        MemberInvoiceGroupStatus.PROCESSED -> CompanyInvoiceStatus.PROCESSED
        MemberInvoiceGroupStatus.PROCESSING -> CompanyInvoiceStatus.PROCESSING
        MemberInvoiceGroupStatus.CANCELED -> CompanyInvoiceStatus.CANCELED
        MemberInvoiceGroupStatus.CANCELED_BY_LIQUIDATION -> CompanyInvoiceStatus.CANCELED_BY_LIQUIDATION
    }

    fun InvoiceLiquidation.toCompanyInvoiceResponse(memberInvoiceGroups: List<MemberInvoiceGroup>) =
        CompanyInvoiceResponse(
            id = this.id,
            externalId = this.externalId,
            status = this.status.toCompanyInvoiceStatus(),
            referenceDate = this.dueDate,
            billingAccountablePartyId = this.billingAccountablePartyId,
            validityPeriod = CompanyInvoiceValidityPeriod(
                start = this.dueDate.atBeginningOfTheMonth(),
                end = this.dueDate.atEndOfTheMonth()
            ),
            multipleValidityPeriods = memberInvoiceGroups.filter { it.id in this.memberInvoiceGroupIds }
                .takeIf { it.size > 1 }
                ?.map {
                    CompanyInvoiceValidityPeriod(
                        start = it.referenceDate.atBeginningOfTheMonth(),
                        end = it.referenceDate.atEndOfTheMonth()
                    )
                }
                ?: emptyList(),
            companySubContractIds = this.subcontractIds,
            companyIds = this.companyIds,
            type = CompanyInvoiceType.LIQUIDATION,
            model = CompanyInvoiceModel.INVOICE_LIQUIDATION,
            totalAmount = this.amount,
            discount = this.discount,
            addition = this.addition,
            dueDate = this.dueDate,
            quantityMemberInvoices = 0,
            memberInvoiceIds = emptyList(),
            relationships = this.memberInvoiceGroupIds.map {
                CompanyInvoiceRelationship(
                    it,
                    CompanyInvoiceModel.MEMBER_INVOICE_GROUP
                )
            },
            installments = this.installment,
            totalInstallments = this.totalInstallments,
            globalItems = emptyList(),
            version = this.version,
            createdAt = this.createdAt,
            updatedAt = this.updatedAt,
        )

    private fun InvoiceLiquidationStatus.toCompanyInvoiceStatus() = when (this) {
        InvoiceLiquidationStatus.PARTIALLY_PAID -> CompanyInvoiceStatus.PARTIALLY_PAID
        InvoiceLiquidationStatus.PAID -> CompanyInvoiceStatus.PAID
        InvoiceLiquidationStatus.WAITING_PAYMENT -> CompanyInvoiceStatus.WAITING_PAYMENT
        InvoiceLiquidationStatus.PROCESSED -> CompanyInvoiceStatus.PROCESSED
        InvoiceLiquidationStatus.PROCESSING -> CompanyInvoiceStatus.PROCESSING

        InvoiceLiquidationStatus.CANCELED -> CompanyInvoiceStatus.CANCELED
    }

    fun PreActivationPayment.toCompanyInvoiceResponse() =
        CompanyInvoiceResponse(
            id = this.id,
            externalId = this.externalId,
            status = this.status.toCompanyInvoiceStatus(),
            referenceDate = this.referenceDate,
            billingAccountablePartyId = this.billingAccountablePartyId,
            validityPeriod = CompanyInvoiceValidityPeriod(
                start = this.referenceDate.atBeginningOfTheMonth(),
                end = this.referenceDate.atEndOfTheMonth()
            ),
            companySubContractIds = listOf(this.companySubContractId!!),
            companyIds = listOf(this.companyId!!),
            type = CompanyInvoiceType.FIRST_PAYMENT,
            model = CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT,
            totalAmount = this.totalAmount,
            discount = 0.money,
            addition = 0.money,
            dueDate = this.dueDate,
            quantityMemberInvoices = this.memberInvoiceIds.size,
            memberInvoiceIds = this.memberInvoiceIds,
            relationships = this.memberInvoiceGroupId?.let {
                listOf(
                    CompanyInvoiceRelationship(
                        it,
                        CompanyInvoiceModel.MEMBER_INVOICE_GROUP
                    )
                )
            } ?: emptyList(),
            globalItems = this.globalItems,
            installments = 0,
            totalInstallments = 0,
            version = this.version,
            createdAt = this.createdAt,
            updatedAt = this.updatedAt,
        )

    private fun PreActivationPaymentStatus.toCompanyInvoiceStatus() = when (this) {
        PreActivationPaymentStatus.PAID ->
            CompanyInvoiceStatus.PAID

        PreActivationPaymentStatus.PROCESSED -> CompanyInvoiceStatus.PROCESSED
        PreActivationPaymentStatus.PROCESSING -> CompanyInvoiceStatus.PROCESSING

        PreActivationPaymentStatus.CANCELED -> CompanyInvoiceStatus.CANCELED
    }

    fun InvoicePayment.toCompanyInvoicePayment() = CompanyInvoicePayment(
        id = this.id,
        invoiceId = this.invoiceGroupId ?: this.invoiceLiquidationId ?: this.preActivationPaymentId,
        amount = this.amount,
        status = this.status.toCompanyInvoicePaymentStatus(),
        method = this.method.toCompanyInvoicePaymentMethod(),
        externalId = this.externalId,
        reason = this.reason?.toCompanyInvoicePaymentReason(),
        pix = this.paymentDetail?.toPix(),
        bankSlip = this.paymentDetail?.toBankSlip(),
        creditCard = this.paymentDetail?.toCreditCard(),
        approvedAt = this.approvedAt,
        canceledReason = this.canceledReason?.toCompanyInvoicePaymentCancellation(),
        createdAt = this.createdAt,
        updatedAt = this.updatedAt,
    )

    private fun InvoicePaymentStatus.toCompanyInvoicePaymentStatus() = when (this) {
        InvoicePaymentStatus.APPROVED -> CompanyInvoicePaymentStatus.APPROVED
        InvoicePaymentStatus.DECLINED,
        InvoicePaymentStatus.FAILED,
        InvoicePaymentStatus.CANCELED -> CompanyInvoicePaymentStatus.CANCELED

        InvoicePaymentStatus.PENDING -> CompanyInvoicePaymentStatus.PENDING
        InvoicePaymentStatus.EXPIRED -> CompanyInvoicePaymentStatus.EXPIRED
    }

    private fun PaymentMethod.toCompanyInvoicePaymentMethod() = when (this) {
        PaymentMethod.BOLEPIX -> CompanyInvoicePaymentMethod.BOLEPIX
        PaymentMethod.PIX -> CompanyInvoicePaymentMethod.PIX
        PaymentMethod.SIMPLE_CREDIT_CARD -> CompanyInvoicePaymentMethod.SIMPLE_CREDIT_CARD
        PaymentMethod.BOLETO -> CompanyInvoicePaymentMethod.BOLETO
    }

    private fun PaymentReason.toCompanyInvoicePaymentReason() = when (this) {
        PaymentReason.B2B_PRE_ACTIVATION_PAYMENT,
        PaymentReason.B2C_PRE_ACTIVATION_PAYMENT -> CompanyInvoicePaymentReason.PRE_ACTIVATION_PAYMENT

        PaymentReason.B2C_LIQUIDATION,
        PaymentReason.B2B_LIQUIDATION -> CompanyInvoicePaymentReason.LIQUIDATION

        PaymentReason.B2B_FIRST_PAYMENT,
        PaymentReason.FIRST_PAYMENT -> CompanyInvoicePaymentReason.FIRST_PAYMENT

        PaymentReason.OVERDUE_PAYMENT,
        PaymentReason.REGULAR_PAYMENT,
        PaymentReason.B2B_REGULAR_PAYMENT -> CompanyInvoicePaymentReason.REGULAR_PAYMENT
    }

    private fun PaymentDetail.toPix() = if (method == PaymentMethod.PIX) {
        this as PixPaymentDetail

        PixDetail(
            paymentUrl = this.paymentUrl,
            dueDate = this.dueDate,
            paymentCode = this.paymentCode,
            externalId = this.externalId
        )
    } else if (method == PaymentMethod.BOLEPIX) {
        this as BolepixPaymentDetail

        PixDetail(
            paymentUrl = this.pixPaymentUrl,
            dueDate = this.dueDate,
            paymentCode = this.paymentCodePix,
            externalId = this.externalId
        )
    } else null

    private fun PaymentDetail.toBankSlip() = if (method == PaymentMethod.BOLETO) {
        this as BoletoPaymentDetail

        BankSlipDetail(
            paymentUrl = this.paymentUrl,
            dueDate = this.dueDate,
            barcode = this.barcode,
            externalId = this.externalId
        )
    } else if (method == PaymentMethod.BOLEPIX) {
        this as BolepixPaymentDetail

        BankSlipDetail(
            paymentUrl = this.paymentUrl,
            dueDate = this.dueDate!!,
            barcode = this.barcodeBoleto,
            externalId = this.externalId
        )
    } else null

    private fun PaymentDetail.toCreditCard() = if (method == PaymentMethod.SIMPLE_CREDIT_CARD) {
        this as SimpleCreditCardPaymentDetail

        CreditCardDetail(
            paymentUrl = this.paymentUrl,
        )
    } else null

    private fun CancellationReason.toCompanyInvoicePaymentCancellation() = when (this) {
        CancellationReason.INVALID -> CompanyInvoicePaymentCancellation.INVALID
        CancellationReason.PAYMENT_PROCESSOR_CANCELED -> CompanyInvoicePaymentCancellation.PAYMENT_PROCESSOR_CANCELED
        CancellationReason.SCHEDULED_CANCEL -> CompanyInvoicePaymentCancellation.PAYMENT_PROCESSOR_CANCELED
        CancellationReason.OVERDUE -> CompanyInvoicePaymentCancellation.OVERDUE
        CancellationReason.CANCELED_BY_REISSUE -> CompanyInvoicePaymentCancellation.CANCELED_BY_REISSUE
        CancellationReason.CANCELED_BY_LIQUIDATION -> CompanyInvoicePaymentCancellation.CANCELED_BY_LIQUIDATION
    }

    fun MemberInvoice.toMemberInvoiceDetail(memberInvoiceBeneficiary: MemberInvoiceBeneficiaryDetail) =
        MemberInvoiceDetail(
            invoiceId = this.memberInvoiceGroupId ?: this.preActivationPaymentId,
            memberInvoiceId = this.id,
            beneficiary = memberInvoiceBeneficiary,
            amount = this.totalAmount,
            status = this.status,
            canceledReason = this.canceledReason,
            referenceDate = this.referenceDate,
            dueDate = this.dueDate,
            paidAt = this.paidAt,
            invoiceBreakdownItems = this.invoiceBreakdown?.let { InvoiceBreakdownConverter.convert(it) },
        )
}
