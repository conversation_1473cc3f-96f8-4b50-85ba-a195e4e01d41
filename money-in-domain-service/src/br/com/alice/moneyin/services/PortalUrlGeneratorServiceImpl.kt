package br.com.alice.moneyin.services

import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.moneyin.ServiceConfig
import br.com.alice.moneyin.client.MoneyInResourceSignTokenService
import br.com.alice.moneyin.client.PortalUrlGeneratorService
import br.com.alice.moneyin.models.InvoicePaymentWithPortalUrl
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.lift
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID

class PortalUrlGeneratorServiceImpl(
    private val moneyInResourceSignTokenService: MoneyInResourceSignTokenService,
) : PortalUrlGeneratorService {
    companion object {
        const val INVOICE_PAYMENT_IDS_CHUNK_SIZE = 300
    }

    override suspend fun mountPortalUrl(invoicePaymentId: UUID): Result<String, Throwable> =
        moneyInResourceSignTokenService.getSignTokenForMoneyInBff(invoicePaymentId)
            .map { signToken ->
                "${ServiceConfig.Payment.invoiceUrl()}/${invoicePaymentId}?token=${signToken}"
            }

    override suspend fun mountPortalUrlInBatch(invoicePaymentIds: List<UUID>) =
        moneyInResourceSignTokenService.getSignTokensForMoneyInBff(invoicePaymentIds)
            .mapEach { (invoicePaymentId, signToken) ->
                invoicePaymentId to "${ServiceConfig.Payment.invoiceUrl()}/${invoicePaymentId}?token=${signToken}"
            }

    override suspend fun mountPortalUrlForInvoicePayments(
        invoicePayments: List<InvoicePayment>
    ): Result<List<InvoicePaymentWithPortalUrl>, Throwable> =
        invoicePayments.pmap { invoicePayment ->
            mountPortalUrl(
                invoicePaymentId = invoicePayment.id
            ).map { portalUrl ->
                InvoicePaymentWithPortalUrl(
                    invoicePaymentId = invoicePayment.id,
                    portalUrl = portalUrl
                )
            }.coFoldNotFound {
                logger.info("Not found Sign Token for invoice payment: ${invoicePayment.id}")

                InvoicePaymentWithPortalUrl(
                    invoicePaymentId = invoicePayment.id,
                    portalUrl = null
                ).success()
            }
        }.lift()

    override suspend fun mountPortalUrlForInvoicePaymentIds(
        invoicePaymentIds: List<UUID>
    ): Result<List<InvoicePaymentWithPortalUrl>, Throwable> = coResultOf {
        invoicePaymentIds.chunked(INVOICE_PAYMENT_IDS_CHUNK_SIZE).map { chunkedIds ->
            mountPortalUrlInBatch(chunkedIds).map { portalUrls ->
                chunkedIds.map { id ->
                    val portalUrl = portalUrls.find { it.first == id }?.second

                    if (portalUrl == null) {
                        logger.error("Error generating portal URL for invoice payment: $id")
                    }

                    InvoicePaymentWithPortalUrl(
                        invoicePaymentId = id,
                        portalUrl = portalUrl
                    )
                }
            }.get()
        }.flatten()
    }
}

