package br.com.alice.moneyin.services

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.common.BeneficiaryType
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.coroutine.pmapConcurrent
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapFirst
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InvoiceLiquidation
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberInvoice
import br.com.alice.data.layer.models.MemberInvoiceGroup
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PreActivationPayment
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.moneyin.client.CompanyInvoiceService
import br.com.alice.moneyin.client.InvoiceLiquidationService
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.client.PreActivationPaymentService
import br.com.alice.moneyin.converters.CompanyInvoiceConverter.toCompanyInvoicePayment
import br.com.alice.moneyin.converters.CompanyInvoiceConverter.toCompanyInvoiceResponse
import br.com.alice.moneyin.converters.CompanyInvoiceConverter.toMemberInvoiceDetail
import br.com.alice.moneyin.models.CompanyInvoiceModel
import br.com.alice.moneyin.models.CompanyInvoicePayment
import br.com.alice.moneyin.models.CompanyInvoiceResponse
import br.com.alice.moneyin.models.MemberInvoiceBeneficiaryDetail
import br.com.alice.moneyin.models.MemberInvoiceDetail
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class CompanyInvoiceServiceImpl(
    private val memberInvoiceGroupService: MemberInvoiceGroupService,
    private val invoiceLiquidationService: InvoiceLiquidationService,
    private val preActivationPaymentService: PreActivationPaymentService,
    private val invoicePaymentService: InvoicePaymentService,
    private val invoicesService: InvoicesService,
    private val personService: PersonService,
    private val memberService: MemberService,
    private val beneficiaryService: BeneficiaryService,
    private val productService: ProductService,
) : CompanyInvoiceService {
    companion object {
        const val CHUNK_GROUP_SIZE = 500
    }

    override suspend fun getByIdAndModel(payload: CompanyInvoiceService.GetByIdAndModelPayload) =
        listByIdsAndModel(
            CompanyInvoiceService.ListByIdsAndModelPayload(
                listOf(payload.invoiceId to payload.model),
                withPayments = payload.withPayments,
                withMemberInvoiceDetails = payload.withMemberInvoiceDetails,
            )
        ).map {
            it.firstOrNull()
                ?: throw NotFoundException("CompanyInvoice from id ${payload.invoiceId} and model ${payload.model} is not found")
        }


    override suspend fun listByCompanyId(
        companyId: UUID,
        findOptions: CompanyInvoiceService.FindOptions
    ): Result<List<CompanyInvoiceResponse>, Throwable> = coResultOf {
        val invoices = fetchInvoicesByCompanyId(companyId, findOptions)

        fetchPaymentAndMemberInvoiceDetails(invoices, findOptions)
    }

    override suspend fun listByBillingAccountablePartyIdAndModelsWithPayments(
        billingAccountablePartyId: UUID,
        models: List<CompanyInvoiceModel>,
    ): Result<List<CompanyInvoiceResponse>, Throwable> = coResultOf {
        val findOptions = CompanyInvoiceService.FindOptions(
            withPayments = true,
            withMemberInvoiceDetails = false,
            models = models,
        )

        val invoices = fetchInvoicesByBillingAccountablePartyId(
            billingAccountablePartyId,
            findOptions,
        )

        fetchPaymentAndMemberInvoiceDetails(invoices, findOptions)
    }

    override suspend fun listBySubContractIdAndModelsWithPayments(
        subContractId: UUID,
        models: List<CompanyInvoiceModel>,
    ): Result<List<CompanyInvoiceResponse>, Throwable> = coResultOf {
        val findOptions = CompanyInvoiceService.FindOptions(
            withPayments = true,
            withMemberInvoiceDetails = false,
            models = models,
        )

        val invoices = fetchInvoicesBySubContractId(subContractId, findOptions)

        fetchPaymentAndMemberInvoiceDetails(invoices, findOptions)
    }

    override suspend fun listByIdsAndModel(
        payload: CompanyInvoiceService.ListByIdsAndModelPayload
    ): Result<List<CompanyInvoiceResponse>, Throwable> = coResultOf<List<CompanyInvoiceResponse>, Throwable> {
        val findOptions = CompanyInvoiceService.FindOptions(
            withPayments = payload.withPayments,
            withMemberInvoiceDetails = payload.withMemberInvoiceDetails,
            models = payload.invoiceIds.map { it.second }.distinct()
        )

        val invoices = fetchByIdsAndModel(payload.invoiceIds, findOptions)
        logger.info(
            "CompanyInvoiceServiceImpl::listByIdsAndModel",
            "ids" to payload.invoiceIds,
            "findOptions" to findOptions
        )

        fetchPaymentAndMemberInvoiceDetails(invoices, findOptions)
    }.then { logger.info("ListByIdsAndModel - result") }
        .thenError { logger.error("Something is wrong", "ex" to it.message, it) }

    private suspend fun fetchPaymentAndMemberInvoiceDetails(
        invoices: List<CompanyInvoiceResponse>,
        findOptions: CompanyInvoiceService.FindOptions
    ) = coroutineScope {
        logger.info("CompanyInvoiceServiceImpl::fetchPaymentAndMemberInvoiceDetails")

        val paymentsDef = async { fetchPayments(invoices, findOptions) }
        val memberInvoiceDetailsDef = async { fetchMemberInvoiceDetail(invoices, findOptions) }

        val payments = paymentsDef.await()
        val memberInvoiceDetails = memberInvoiceDetailsDef.await()

        invoices
            .let {
                if (findOptions.withPayments) it.bindPayments(payments) else it
            }
            .let {
                if (findOptions.withMemberInvoiceDetails) it.bindMemberInvoiceDetails(memberInvoiceDetails) else it
            }
    }

    private suspend fun fetchByIdsAndModel(
        invoiceIds: List<Pair<UUID, CompanyInvoiceModel>>,
        findOptions: CompanyInvoiceService.FindOptions
    ) = coroutineScope {
        val memberInvoiceGroupsDef = async {
            if (findOptions.shouldUseModel(CompanyInvoiceModel.MEMBER_INVOICE_GROUP)) {
                val ids =
                    invoiceIds.filter { it.second.isMemberInvoiceGroup }
                        .map { it.first }

                memberInvoiceGroupService.getByIds(
                    ids,
                ).get()
            } else emptyList()
        }

        val invoiceLiquidationsDef = async {
            if (findOptions.shouldUseModel(CompanyInvoiceModel.INVOICE_LIQUIDATION)) {
                val ids =
                    invoiceIds.filter { it.second.isLiquidation }
                        .map { it.first }

                invoiceLiquidationService.listByIds(ids).get()
            } else emptyList()
        }

        val preActivationPaymentsDef = async {
            if (findOptions.shouldUseModel(CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT) && isPreActivationEnabled()) {
                val ids =
                    invoiceIds.filter { it.second.isPreActivationPayment }
                        .map { it.first }

                preActivationPaymentService.listByIds(ids).get()
            } else emptyList()
        }

        val memberInvoiceGroups = memberInvoiceGroupsDef.await()
        val invoiceLiquidations = invoiceLiquidationsDef.await()
        val preActivationPayments = preActivationPaymentsDef.await()

        groupInvoices(memberInvoiceGroups, preActivationPayments, invoiceLiquidations)
    }

    private fun List<CompanyInvoiceResponse>.bindPayments(payments: List<CompanyInvoicePayment>) = map { invoice ->
        invoice.copy(payments = payments.filter { it.invoiceId == invoice.id })
    }

    private fun List<CompanyInvoiceResponse>.bindMemberInvoiceDetails(memberInvoiceDetails: List<MemberInvoiceDetail>) =
        map { invoice ->
            invoice.copy(memberInvoiceDetails = memberInvoiceDetails.filter { it.invoiceId == invoice.id })
        }

    private fun List<CompanyInvoiceResponse>.toIdForModel(model: CompanyInvoiceModel) =
        filter { it.model == model }.map { it.id }

    private fun CompanyInvoiceService.FindOptions.shouldUseModel(model: CompanyInvoiceModel) =
        models.isEmpty() || models.contains(model)

    private suspend fun fetchPayments(
        invoices: List<CompanyInvoiceResponse>,
        findOptions: CompanyInvoiceService.FindOptions
    ) = coroutineScope {

        if (!findOptions.withPayments) {
            return@coroutineScope emptyList()
        }

        val memberInvoiceGroupInvoicePaymentsDef = async {
            if (findOptions.shouldUseModel(CompanyInvoiceModel.MEMBER_INVOICE_GROUP)) {
                val ids = invoices.toIdForModel(CompanyInvoiceModel.MEMBER_INVOICE_GROUP)

                invoicePaymentService.getByInvoiceGroupIds(ids, true).get()
            } else emptyList()
        }

        val invoiceLiquidationInvoicePaymentsDef = async {
            if (findOptions.shouldUseModel(CompanyInvoiceModel.INVOICE_LIQUIDATION)) {
                val ids = invoices.toIdForModel(CompanyInvoiceModel.INVOICE_LIQUIDATION)
                invoicePaymentService.getByInvoiceLiquidationIds(ids, true).get()
            } else emptyList()
        }

        val preActivationPaymentInvoicePaymentsDef = async {
            if (findOptions.shouldUseModel(CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT) && isPreActivationEnabled()) {
                val ids = invoices.toIdForModel(CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT)
                invoicePaymentService.getByInvoicePreActivationPayment(ids, true)
                    .get()
            } else emptyList()
        }

        val memberInvoiceGroupInvoicePayments = memberInvoiceGroupInvoicePaymentsDef.await()
        val invoiceLiquidationInvoicePayments = invoiceLiquidationInvoicePaymentsDef.await()
        val preActivationPaymentInvoicePayments = preActivationPaymentInvoicePaymentsDef.await()

        val invoicePayments = memberInvoiceGroupInvoicePayments
            .plus(invoiceLiquidationInvoicePayments)
            .plus(preActivationPaymentInvoicePayments)
            .map { it.toCompanyInvoicePayment() }

        invoicePayments.sortedByDescending { it.createdAt }
    }

    private fun groupInvoices(
        memberInvoiceGroups: List<MemberInvoiceGroup>,
        preActivationPayments: List<PreActivationPayment>,
        invoiceLiquidations: List<InvoiceLiquidation>
    ): List<CompanyInvoiceResponse> {
        val invoiceLiquidationResponses =
            invoiceLiquidations.map { it.toCompanyInvoiceResponse(memberInvoiceGroups) }

        val invoiceResponses = memberInvoiceGroups.map { it.toCompanyInvoiceResponse() }

        val preActivationPaymentResponses = preActivationPayments.map { it.toCompanyInvoiceResponse() }

        val allInvoiceResponses =
            invoiceResponses
                .plus(invoiceLiquidationResponses)
                .plus(preActivationPaymentResponses)

        return allInvoiceResponses.sortedByDescending { it.referenceDate }
    }

    private suspend fun fetchInvoicesBySubContractId(
        subContractId: UUID,
        findOptions: CompanyInvoiceService.FindOptions
    ) =
        coroutineScope {
            val memberInvoiceGroupsDef = async {
                if (findOptions.shouldUseModel(CompanyInvoiceModel.MEMBER_INVOICE_GROUP))
                    memberInvoiceGroupService.getBySubcontractId(subContractId).get()
                else emptyList()
            }

            val invoiceLiquidationsDef = async {
                if (findOptions.shouldUseModel(CompanyInvoiceModel.INVOICE_LIQUIDATION)) {
                    invoiceLiquidationService.getBySubContractId(subContractId).get()
                } else emptyList()
            }

            val preActivationPaymentsDef = async {
                if (findOptions.shouldUseModel(CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT) && isPreActivationEnabled()) {
                    preActivationPaymentService.getBySubcontractId(subContractId).get()
                } else emptyList()
            }

            val memberInvoiceGroups = memberInvoiceGroupsDef.await()
            val invoiceLiquidations = invoiceLiquidationsDef.await()
            val preActivationPayments = preActivationPaymentsDef.await()

            groupInvoices(memberInvoiceGroups, preActivationPayments, invoiceLiquidations)
        }

    private suspend fun fetchInvoicesByBillingAccountablePartyId(
        billingAccountablePartyId: UUID,
        findOptions: CompanyInvoiceService.FindOptions
    ) =
        coroutineScope {
            val memberInvoiceGroupsDef = async {
                if (findOptions.shouldUseModel(CompanyInvoiceModel.MEMBER_INVOICE_GROUP))
                    memberInvoiceGroupService.getByBillingAccountablePartyId(
                        billingAccountablePartyId,
                    ).get()
                else emptyList()
            }

            val invoiceLiquidationsDef = async {
                if (findOptions.shouldUseModel(CompanyInvoiceModel.INVOICE_LIQUIDATION)) {
                    invoiceLiquidationService.getByBillingAccountablePartyId(billingAccountablePartyId).get()
                } else emptyList()
            }

            val preActivationPaymentsDef = async {
                if (findOptions.shouldUseModel(CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT) && isPreActivationEnabled()) {
                    preActivationPaymentService.getByBillingAccountablePartyId(billingAccountablePartyId).get()
                } else emptyList()
            }

            val memberInvoiceGroups = memberInvoiceGroupsDef.await()
            val invoiceLiquidations = invoiceLiquidationsDef.await()
            val preActivationPayments = preActivationPaymentsDef.await()

            groupInvoices(memberInvoiceGroups, preActivationPayments, invoiceLiquidations)
        }

    private suspend fun fetchInvoicesByCompanyId(companyId: UUID, findOptions: CompanyInvoiceService.FindOptions) =
        coroutineScope {
            val memberInvoiceGroupsDef = async {
                if (findOptions.shouldUseModel(CompanyInvoiceModel.MEMBER_INVOICE_GROUP))
                    memberInvoiceGroupService.getByCompanyId(
                        companyId,
                    ).get()
                else emptyList()
            }

            val invoiceLiquidationsDef = async {
                if (findOptions.shouldUseModel(CompanyInvoiceModel.INVOICE_LIQUIDATION)) {
                    invoiceLiquidationService.listByCompanyId(companyId).get()
                } else emptyList()
            }

            val preActivationPaymentsDef = async {
                if (findOptions.shouldUseModel(CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT) && isPreActivationEnabled()) {
                    preActivationPaymentService.listByCompanyId(companyId).get()
                } else emptyList()
            }

            val memberInvoiceGroups = memberInvoiceGroupsDef.await()
            val invoiceLiquidations = invoiceLiquidationsDef.await()
            val preActivationPayments = preActivationPaymentsDef.await()

            groupInvoices(memberInvoiceGroups, preActivationPayments, invoiceLiquidations)
        }

    private suspend fun fetchMemberInvoiceDetail(
        invoices: List<CompanyInvoiceResponse>,
        findOptions: CompanyInvoiceService.FindOptions
    ) = coroutineScope {
        if (!findOptions.withMemberInvoiceDetails) {
            return@coroutineScope emptyList()
        }

        val memberInvoices = fetchMemberInvoices(invoices, findOptions)

        val uniqueMemberIds = memberInvoices.map { it.memberId }.distinct()

        logger.info("Find for members", "size" to uniqueMemberIds.chunked(CHUNK_GROUP_SIZE).size)
        val members = uniqueMemberIds.chunked(CHUNK_GROUP_SIZE)
            .pmapConcurrent(semaphorePermits()) {
                logger.info("MemberService.findByIds", "size" to it.size)
                memberService.findByIds(it)
                    .then { logger.info("MemberService.findByIds - success result") }
                    .thenError { logger.error("MemberService.findByIds - failure result", "ex" to it.message, it) }
                    .get()
            }.flatten().also { logger.info("MemberService.findByIds - final result", "size" to it.size) }

        val memberInvoiceBeneficiaryDetails = fetchMemberBeneficiaryDetails(members)

        memberInvoices.map { it.toMemberInvoiceDetail(memberInvoiceBeneficiaryDetails.getValue(it.memberId)) }
    }

    private fun semaphorePermits() = FeatureService.get(FeatureNamespace.MONEY_IN, "semaphore_permits", 3)

    private suspend fun fetchMemberBeneficiaryDetails(members: List<Member>) = coroutineScope {
        logger.info(
            "Find for people",
            "size" to members.map { it.personId.toString() }.distinct().chunked(CHUNK_GROUP_SIZE).size
        )

        val peopleDeferred = async {
            val personIds = members.map { it.personId.toString() }

            personIds.chunked(CHUNK_GROUP_SIZE).pmapConcurrent(semaphorePermits()) {
                logger.info("PersonService.findByIds", "size" to it.size)
                personService.findByIds(it)
                    .then { logger.info("PersonService.findByIds - success result") }
                    .thenError { logger.error("PersonService.findByIds - failure result", "ex" to it.message, it) }
                    .get()
            }
                .flatten().also { logger.info("PersonService.findByIds - final result", "size" to it.size) }
        }

        logger.info(
            "Find for beneficiaries",
            "size" to members.map { it.id }
                .chunked(CHUNK_GROUP_SIZE).size
        )

        val beneficiariesDeferred = async {
            val memberIds = members.map { it.id }
            memberIds.chunked(CHUNK_GROUP_SIZE)
                .pmapConcurrent(semaphorePermits()) {
                    logger.info("BeneficiaryService.findByMemberIds", "size" to it.size)
                    beneficiaryService.findByMemberIds(it)
                        .then { logger.info("BeneficiaryService.findByMemberIds - success result") }
                        .thenError {
                            logger.error(
                                "BeneficiaryService.findByMemberIds - failure result",
                                "ex" to it.message,
                                it
                            )
                        }
                        .get()
                }.flatten().also { logger.info("BeneficiaryService.findByMemberIds - final result", "size" to it.size) }
        }

        logger.info(
            "Find for products",
            "size" to members.map { it.selectedProduct.id }.distinct().size
        )
        val productsDeferred = async {
            productService.findByIds(
                members.map { it.selectedProduct.id }.distinct(),
                ProductService.FindOptions(
                    withPriceListing = false,
                    withBundles = false
                )
            ).get()
        }

        val peopleById = peopleDeferred.await().associateBy { it.id }
        val beneficiariesByMemberId = beneficiariesDeferred.await().associateBy { it.memberId }
        val beneficiariesById = beneficiariesByMemberId.values.associateBy { it.id }
        val productsById = productsDeferred.await().associateBy { it.id }

        members.pmap { member ->
            val person = peopleById.getValue(member.personId)
            val beneficiary = beneficiariesByMemberId[member.id]
            val product = productsById.getValue(member.selectedProduct.id)

            MemberInvoiceBeneficiaryDetail(
                memberId = member.id,
                firstName = person.firstName,
                lastName = person.lastName,
                isMemberActive = member.active,
                nationalId = person.nationalId,
                nationalIdHolder = beneficiary?.getNationalIdBeneficiaryEmployee(peopleById, beneficiariesById),
                type = beneficiary?.type ?: BeneficiaryType.UNDEFINED,
                productTitle = product.title,
                productDisplayName = product.salesProductName,
            )
        }.associateBy { it.memberId }
    }

    private suspend fun fetchMemberInvoicesPaginatedUsingMethod(
        ids: List<UUID>,
        paginatedMethod: suspend (ids: List<UUID>, withPayment: Boolean, offset: Int, limit: Int) -> Result<List<MemberInvoice>, Throwable>
    ): List<MemberInvoice> {
        val memberInvoices = mutableListOf<MemberInvoice>()
        var offset = 0
        var page = 0

        do {
            val fetched =
                paginatedMethod(
                    ids,
                    false,
                    offset,
                    CHUNK_GROUP_SIZE,
                )
                    .then {
                        logger.info(
                            "CompanyInvoiceServiceImpl::fetchMemberInvoices",
                            "result_found_size" to it.size,
                            "ids" to ids,
                            "page" to page
                        )
                    }.then { memberInvoices.addAll(it) }
                    .get()

            offset += CHUNK_GROUP_SIZE
            page += page
        } while (fetched.size == CHUNK_GROUP_SIZE)

        return memberInvoices
    }

    private suspend fun fetchMemberInvoices(
        invoices: List<CompanyInvoiceResponse>,
        findOptions: CompanyInvoiceService.FindOptions,
    ) = coroutineScope {
        logger.info(
            "CompanyInvoiceServiceImpl::fetchMemberInvoices - find for memberInvoices",
            "findOptions" to findOptions,
            "invoices" to invoices.map {
                mapOf(
                    "id" to it.id,
                    "model" to it.model,
                    "is_pre_activation_payment" to it.isPreActivationPayment,
                    "is_member_invoice_group" to it.isMemberInvoiceGroup,
                )
            })

        val invoicesFromMemberInvoiceGroupDef = async {
            if (findOptions.shouldUseModel(CompanyInvoiceModel.MEMBER_INVOICE_GROUP)) {
                val memberInvoiceGroupIds = invoices.filter { it.isMemberInvoiceGroup }.map { it.id }
                logger.info(
                    "CompanyInvoiceServiceImpl::fetchMemberInvoices - should find using MEMBER_INVOICE_GROUP",
                    "ids" to memberInvoiceGroupIds,
                )


                fetchMemberInvoicesPaginatedUsingMethod(
                    memberInvoiceGroupIds,
                    invoicesService::listByMemberInvoiceGroupIdsPaginated
                )
            } else emptyList()
        }

        val invoicesFromPreActivationPaymentDef = async {
            if (findOptions.shouldUseModel(CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT) && isPreActivationEnabled()) {
                val preActivationPaymentIds = invoices.filter { it.isPreActivationPayment }.map { it.id }
                logger.info(
                    "CompanyInvoiceServiceImpl::fetchMemberInvoices - should find using PRE_ACTIVATION_PAYMENT",
                    "ids" to preActivationPaymentIds
                )

                fetchMemberInvoicesPaginatedUsingMethod(
                    preActivationPaymentIds,
                    invoicesService::listByPreActivationPaymentIdsPaginated
                )
            } else emptyList()
        }

        invoicesFromMemberInvoiceGroupDef.await() + invoicesFromPreActivationPaymentDef.await()
    }

    private suspend fun Beneficiary.getNationalIdBeneficiaryEmployee(
        peopleById: Map<PersonId, Person>,
        beneficiariesById: Map<UUID, Beneficiary>
    ): String? {
        return if (this.type == BeneficiaryType.DEPENDENT && this.parentBeneficiary != null) {
            try {
                val parent = beneficiariesById.getValue(this.parentBeneficiary!!)
                peopleById.getValue(parent.personId).nationalId
            } catch (e: NoSuchElementException) {
                this.parentBeneficiary!!.let { beneficiaryService.get(it) }.map {
                    personService.get(it.personId).get().nationalId
                }.getOrNullIfNotFound()
            }
        } else {
            null
        }
    }

    private fun isPreActivationEnabled() =
        FeatureService.get(FeatureNamespace.MONEY_IN, "enable_pre_activation_payment", false)
}
