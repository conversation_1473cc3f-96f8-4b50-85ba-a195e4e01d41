package br.com.alice.moneyin.services

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InvoicePaymentStatus
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberInvoice
import br.com.alice.data.layer.models.Person
import br.com.alice.moneyin.client.CompanyInvoiceService
import br.com.alice.moneyin.client.InvoiceLiquidationService
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.client.PreActivationPaymentService
import br.com.alice.moneyin.converters.CompanyInvoiceConverter.toCompanyInvoicePayment
import br.com.alice.moneyin.converters.CompanyInvoiceConverter.toCompanyInvoiceResponse
import br.com.alice.moneyin.converters.CompanyInvoiceConverter.toMemberInvoiceDetail
import br.com.alice.moneyin.models.CompanyInvoiceModel
import br.com.alice.moneyin.models.MemberInvoiceBeneficiaryDetail
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.product.client.ProductService
import io.mockk.called
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.spyk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test

class CompanyInvoiceServiceTest {
    companion object {
        const val CHUNK_GROUP_SIZE = 500
    }

    private val memberInvoiceGroupService: MemberInvoiceGroupService = mockk()
    private val invoiceLiquidationService: InvoiceLiquidationService = mockk()
    private val preActivationPaymentService: PreActivationPaymentService = mockk()
    private val invoicePaymentService: InvoicePaymentService = mockk()
    private val invoicesService: InvoicesService = mockk()
    private val personService: PersonService = mockk()
    private val memberService: MemberService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val productService: ProductService = mockk()

    val service = CompanyInvoiceServiceImpl(
        memberInvoiceGroupService,
        invoiceLiquidationService,
        preActivationPaymentService,
        invoicePaymentService,
        invoicesService,
        personService,
        memberService,
        beneficiaryService,
        productService,
    )

    val spyService = spyk(service)

    @AfterTest
    fun tearDown() = clearAllMocks()

    private val companyId = RangeUUID.generate()
    private val subContractId = RangeUUID.generate()

    private val memberInvoiceGroup =
        TestModelFactory.buildMemberInvoiceGroup(
            companyId = companyId,
            companySubcontractId = subContractId,
        )
    private val invoiceLiquidation =
        TestModelFactory.buildInvoiceLiquidation(
            companyId = companyId,
            subcontractId = subContractId,
        )

    private val preActivationPayment =
        TestModelFactory.buildPreActivationPayment(
            companyId = companyId,
            companySubContractId = subContractId,
            referenceDate = LocalDate.now().minusMonths(1)
        )

    private val memberInvoiceGroupCanceledEventInvoicePayment = TestModelFactory.buildInvoicePayment(
        invoiceGroupId = memberInvoiceGroup.id,
        invoiceLiquidationId = null,
        preActivationPaymentId = null,
        status = InvoicePaymentStatus.CANCELED,
    )

    private val memberInvoiceGroupPendingInvoicePayment = TestModelFactory.buildInvoicePayment(
        invoiceGroupId = memberInvoiceGroup.id,
        invoiceLiquidationId = null,
        preActivationPaymentId = null,
    )

    private val preActivationPaymentApprovedInvoicePayment = TestModelFactory.buildInvoicePayment(
        invoiceGroupId = null,
        invoiceLiquidationId = null,
        preActivationPaymentId = preActivationPayment.id,
        status = InvoicePaymentStatus.APPROVED,
        approvedAt = LocalDateTime.now(),
    )

    @Nested
    inner class GetByIdAndModel {

        @Test
        fun `should return the invoice successfully`() = runBlocking<Unit> {
            val invoiceId = RangeUUID.generate()
            val model = CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT

            val invoice = preActivationPayment.toCompanyInvoiceResponse()
                .copy(payments = listOf(preActivationPaymentApprovedInvoicePayment.toCompanyInvoicePayment()))

            coEvery {
                spyService.listByIdsAndModel(
                    CompanyInvoiceService.ListByIdsAndModelPayload(
                        listOf(invoiceId to model),
                        withPayments = true,
                        withMemberInvoiceDetails = false,
                    )
                )
            } returns listOf(invoice)

            val result =
                spyService.getByIdAndModel(
                    CompanyInvoiceService.GetByIdAndModelPayload(
                        invoiceId,
                        model,
                        withPayments = true,
                        withMemberInvoiceDetails = false,
                    )
                )

            ResultAssert.assertThat(result).isSuccessWithData(invoice)
        }

        @Test
        fun `should throw the not found exception when any invoice is found`() = runBlocking<Unit> {
            val invoiceId = RangeUUID.generate()
            val model = CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT


            coEvery {
                spyService.listByIdsAndModel(
                    CompanyInvoiceService.ListByIdsAndModelPayload(
                        listOf(invoiceId to model),
                        withPayments = true,
                        withMemberInvoiceDetails = false,
                    )
                )
            } returns emptyList()

            val result =
                spyService.getByIdAndModel(
                    CompanyInvoiceService.GetByIdAndModelPayload(
                        invoiceId,
                        model,
                        withPayments = true,
                        withMemberInvoiceDetails = false,
                    )
                )

            ResultAssert.assertThat(result).isFailureOfType(NotFoundException::class)
        }
    }

    @Nested
    inner class ListByCompanyId {

        @Test
        fun `should return the company invoice for whole models with its payments`() = runBlocking<Unit> {
            coEvery { memberInvoiceGroupService.getByCompanyId(companyId) } returns listOf(memberInvoiceGroup)
            coEvery { invoiceLiquidationService.listByCompanyId(companyId) } returns listOf(invoiceLiquidation)

            coEvery { invoicePaymentService.getByInvoiceGroupIds(listOf(memberInvoiceGroup.id), true) } returns listOf(
                memberInvoiceGroupCanceledEventInvoicePayment,
                memberInvoiceGroupPendingInvoicePayment,
            )

            coEvery {
                invoicePaymentService.getByInvoiceLiquidationIds(
                    listOf(invoiceLiquidation.id),
                    true
                )
            } returns emptyList()

            val expectedResult = listOf(
                invoiceLiquidation.toCompanyInvoiceResponse(listOf(memberInvoiceGroup)),
                memberInvoiceGroup.toCompanyInvoiceResponse().copy(
                    payments = listOf(
                        memberInvoiceGroupPendingInvoicePayment.toCompanyInvoicePayment(),
                        memberInvoiceGroupCanceledEventInvoicePayment.toCompanyInvoicePayment()
                    )
                ),
            )

            val result = service.listByCompanyId(companyId, CompanyInvoiceService.FindOptions(withPayments = true))


            ResultAssert.assertThat(result).isSuccessWithData(expectedResult)

            coVerifyOnce {
                memberInvoiceGroupService.getByCompanyId(companyId)
                invoiceLiquidationService.listByCompanyId(companyId)

                invoicePaymentService.getByInvoiceGroupIds(listOf(memberInvoiceGroup.id), true)
                invoicePaymentService.getByInvoiceLiquidationIds(
                    listOf(invoiceLiquidation.id),
                    true
                )
            }

            coVerifyNone {
                preActivationPaymentService.listByCompanyId(any())
                invoicePaymentService.getByInvoicePreActivationPayment(
                    any(),
                    any()
                )
            }
        }

        @Test
        fun `should return the company invoice for whole models with its member invoices`() = runBlocking<Unit> {
            val memberInvoiceGroupIds = listOf(memberInvoiceGroup.id)

            val product = TestModelFactory.buildProduct()
            val person = TestModelFactory.buildPerson()
            val member = TestModelFactory.buildMember(productId = product.id, personId = person.id)
            val beneficiary = TestModelFactory.buildBeneficiary(memberId = member.id)
            val memberInvoice =
                TestModelFactory.buildMemberInvoice(memberInvoiceGroupId = memberInvoiceGroup.id, memberId = member.id)

            coEvery { memberInvoiceGroupService.getByCompanyId(companyId) } returns listOf(memberInvoiceGroup)
            coEvery { invoiceLiquidationService.listByCompanyId(companyId) } returns listOf(invoiceLiquidation)
            coEvery {
                invoicesService.listByMemberInvoiceGroupIdsPaginated(memberInvoiceGroupIds, false, 0, CHUNK_GROUP_SIZE)
            } returns listOf(
                memberInvoice
            )
            coEvery {
                memberService.findByIds(listOf(member.id))
            } returns listOf(member)

            coEvery { personService.findByIds(listOf(member.personId.toString())) } returns listOf(person)

            coEvery { beneficiaryService.findByMemberIds(listOf(member.id)) } returns listOf(beneficiary)

            coEvery {
                productService.findByIds(
                    listOf(member.selectedProduct.id),
                    ProductService.FindOptions(
                        withPriceListing = false,
                        withBundles = false
                    )
                )
            } returns listOf(product)

            val expectedBeneficiaryDetail = MemberInvoiceBeneficiaryDetail(
                memberId = member.id,
                firstName = person.firstName,
                lastName = person.lastName,
                isMemberActive = member.active,
                nationalId = person.nationalId,
                nationalIdHolder = null,
                type = beneficiary.type,
                productTitle = product.title,
                productDisplayName = product.salesProductName,
            )

            val expectedResult = listOf(
                invoiceLiquidation.toCompanyInvoiceResponse(listOf(memberInvoiceGroup)),
                memberInvoiceGroup.toCompanyInvoiceResponse().copy(
                    memberInvoiceDetails = listOf(
                        memberInvoice.toMemberInvoiceDetail(expectedBeneficiaryDetail),
                    )
                ),
            )

            val result =
                service.listByCompanyId(companyId, CompanyInvoiceService.FindOptions(withMemberInvoiceDetails = true))

            ResultAssert.assertThat(result).isSuccessWithData(expectedResult)

            coVerifyOnce {
                memberInvoiceGroupService.getByCompanyId(companyId)
                invoiceLiquidationService.listByCompanyId(companyId)
                invoicesService.listByMemberInvoiceGroupIdsPaginated(memberInvoiceGroupIds, false, 0, CHUNK_GROUP_SIZE)
                memberService.findByIds(listOf(member.id))
                personService.findByIds(listOf(member.personId.toString()))
                beneficiaryService.findByMemberIds(listOf(member.id))
                productService.findByIds(
                    listOf(member.selectedProduct.id),
                    ProductService.FindOptions(
                        withPriceListing = false,
                        withBundles = false
                    )
                )
            }

            coVerifyNone {
                preActivationPaymentService.listByCompanyId(any())
                invoicePaymentService wasNot called
            }
        }


        @Nested
        inner class WithFFEnabled {
            @Test
            fun `should return the company invoice for whole models with its member invoices`() = runBlocking<Unit> {
                withFeatureFlag(FeatureNamespace.MONEY_IN, "enable_pre_activation_payment", true) {
                    val memberInvoiceGroupIds = listOf(memberInvoiceGroup.id)
                    val preActivationPaymentIds = listOf(preActivationPayment.id)

                    val product = TestModelFactory.buildProduct()
                    val person = TestModelFactory.buildPerson()
                    val member = TestModelFactory.buildMember(productId = product.id, personId = person.id)
                    val beneficiary = TestModelFactory.buildBeneficiary(memberId = member.id)

                    val persons = mutableListOf<Person>().apply {
                        repeat(600) {
                            add(
                                TestModelFactory.buildPerson()
                            )
                        }
                    }

                    val personIds = persons.map { it.id.toString() }

                    val members = mutableListOf<Member>().apply {
                        repeat(600) { index ->
                            add(
                                TestModelFactory.buildMember(productId = product.id, personId = persons[index].id)
                            )
                        }
                    }

                    val memberIds = members.map { it.id }

                    val beneficiaries = mutableListOf<Beneficiary>().apply {
                        repeat(600) { index ->
                            add(
                                TestModelFactory.buildBeneficiary(
                                    memberId = memberIds[index],
                                    personId = persons[index].id
                                )
                            )
                        }
                    }
                    val memberInvoicesFromMIG = mutableListOf<MemberInvoice>().apply {
                        repeat(600) { index ->
                            add(
                                TestModelFactory.buildMemberInvoice(
                                    memberInvoiceGroupId = memberInvoiceGroup.id,
                                    preActivationPaymentId = null,
                                    memberId = members[index].id,
                                )
                            )
                        }
                    }

                    val memberInvoiceFromPAP = TestModelFactory.buildMemberInvoice(
                        memberInvoiceGroupId = null,
                        preActivationPaymentId = preActivationPayment.id,
                        memberId = member.id,
                        referenceDate = LocalDate.now().minusMonths(1)
                    )

                    coEvery { memberInvoiceGroupService.getByCompanyId(companyId) } returns listOf(memberInvoiceGroup)
                    coEvery { invoiceLiquidationService.listByCompanyId(companyId) } returns listOf(invoiceLiquidation)
                    coEvery { preActivationPaymentService.listByCompanyId(companyId) } returns listOf(
                        preActivationPayment
                    )
                    coEvery {
                        invoicesService.listByMemberInvoiceGroupIdsPaginated(
                            memberInvoiceGroupIds,
                            false,
                            0,
                            CHUNK_GROUP_SIZE
                        )
                    } returns memberInvoicesFromMIG.take(CHUNK_GROUP_SIZE)

                    coEvery {
                        invoicesService.listByMemberInvoiceGroupIdsPaginated(
                            memberInvoiceGroupIds,
                            false,
                            CHUNK_GROUP_SIZE,
                            CHUNK_GROUP_SIZE
                        )
                    } returns memberInvoicesFromMIG.drop(CHUNK_GROUP_SIZE)

                    coEvery {
                        invoicesService.listByPreActivationPaymentIdsPaginated(
                            preActivationPaymentIds,
                            false,
                            0,
                            CHUNK_GROUP_SIZE
                        )
                    } returns listOf(
                        memberInvoiceFromPAP
                    )
                    coEvery {
                        memberService.findByIds(memberIds.take(CHUNK_GROUP_SIZE))
                    } returns members.take(CHUNK_GROUP_SIZE)

                    coEvery {
                        memberService.findByIds(memberIds.drop(CHUNK_GROUP_SIZE) + listOf(member.id))
                    } returns members.drop(CHUNK_GROUP_SIZE) + listOf(member)

                    coEvery { personService.findByIds(personIds.take(CHUNK_GROUP_SIZE)) } returns persons.take(
                        CHUNK_GROUP_SIZE
                    )

                    coEvery { personService.findByIds(personIds.drop(CHUNK_GROUP_SIZE) + listOf(person.id.toString())) } returns persons.drop(
                        CHUNK_GROUP_SIZE
                    ) + listOf(person)

                    coEvery { beneficiaryService.findByMemberIds(memberIds.take(CHUNK_GROUP_SIZE)) } returns beneficiaries.take(
                        CHUNK_GROUP_SIZE
                    )

                    coEvery { beneficiaryService.findByMemberIds(memberIds.drop(CHUNK_GROUP_SIZE) + listOf(member.id)) } returns beneficiaries.drop(
                        CHUNK_GROUP_SIZE
                    ) + listOf(beneficiary)

                    coEvery {
                        productService.findByIds(
                            listOf(member.selectedProduct.id),
                            ProductService.FindOptions(
                                withPriceListing = false,
                                withBundles = false
                            )
                        )
                    } returns listOf(product)

                    val expectedBeneficiaryDetails = persons.mapIndexed { index, person ->
                        MemberInvoiceBeneficiaryDetail(
                            memberId = members[index].id,
                            firstName = person.firstName,
                            lastName = person.lastName,
                            isMemberActive = members[index].active,
                            nationalId = person.nationalId,
                            nationalIdHolder = null,
                            type = beneficiaries[index].type,
                            productTitle = product.title,
                            productDisplayName = product.salesProductName,
                        )
                    }

                    val expectedBeneficiaryDetail =
                        MemberInvoiceBeneficiaryDetail(
                            memberId = member.id,
                            firstName = person.firstName,
                            lastName = person.lastName,
                            isMemberActive = member.active,
                            nationalId = person.nationalId,
                            nationalIdHolder = null,
                            type = beneficiary.type,
                            productTitle = product.title,
                            productDisplayName = product.salesProductName,
                        )

                    val expectedResult = listOf(
                        invoiceLiquidation.toCompanyInvoiceResponse(listOf(memberInvoiceGroup)),
                        memberInvoiceGroup.toCompanyInvoiceResponse().copy(
                            memberInvoiceDetails = expectedBeneficiaryDetails.mapIndexed { index, detail ->
                                memberInvoicesFromMIG[index].toMemberInvoiceDetail(
                                    detail
                                )
                            }

                        ),
                        preActivationPayment.toCompanyInvoiceResponse()
                            .copy(
                                memberInvoiceDetails = listOf(
                                    memberInvoiceFromPAP.toMemberInvoiceDetail(
                                        expectedBeneficiaryDetail
                                    )
                                )
                            )
                    )

                    val result = service.listByCompanyId(
                        companyId,
                        CompanyInvoiceService.FindOptions(withMemberInvoiceDetails = true)
                    )

                    ResultAssert.assertThat(result).isSuccessWithData(expectedResult)

                    coVerifyOnce {
                        memberInvoiceGroupService.getByCompanyId(companyId)
                        invoiceLiquidationService.listByCompanyId(companyId)
                        preActivationPaymentService.listByCompanyId(companyId)
                        invoicesService.listByMemberInvoiceGroupIdsPaginated(
                            memberInvoiceGroupIds,
                            false,
                            0,
                            CHUNK_GROUP_SIZE
                        )
                        invoicesService.listByMemberInvoiceGroupIdsPaginated(
                            memberInvoiceGroupIds,
                            false,
                            CHUNK_GROUP_SIZE,
                            CHUNK_GROUP_SIZE
                        )
                        invoicesService.listByPreActivationPaymentIdsPaginated(
                            preActivationPaymentIds,
                            false,
                            0,
                            CHUNK_GROUP_SIZE
                        )
                        memberService.findByIds(memberIds.take(CHUNK_GROUP_SIZE))
                        memberService.findByIds(memberIds.drop(CHUNK_GROUP_SIZE) + listOf(member.id))
                        personService.findByIds(personIds.take(CHUNK_GROUP_SIZE))
                        personService.findByIds(personIds.drop(CHUNK_GROUP_SIZE) + listOf(person.id.toString()))
                        beneficiaryService.findByMemberIds(memberIds.take(CHUNK_GROUP_SIZE))
                        beneficiaryService.findByMemberIds(memberIds.drop(CHUNK_GROUP_SIZE) + listOf(member.id))
                        productService.findByIds(
                            listOf(member.selectedProduct.id),
                            ProductService.FindOptions(
                                withPriceListing = false,
                                withBundles = false
                            )
                        )
                    }

                    coVerifyNone {
                        invoicePaymentService wasNot called
                    }
                }
            }

            @Test
            fun `should return the company invoice for whole models with its payments with FF enabled`() =
                runBlocking<Unit> {
                    withFeatureFlag(FeatureNamespace.MONEY_IN, "enable_pre_activation_payment", true) {
                        coEvery { memberInvoiceGroupService.getByCompanyId(companyId) } returns listOf(
                            memberInvoiceGroup
                        )
                        coEvery { invoiceLiquidationService.listByCompanyId(companyId) } returns listOf(
                            invoiceLiquidation
                        )
                        coEvery { preActivationPaymentService.listByCompanyId(companyId) } returns listOf(
                            preActivationPayment
                        )

                        coEvery {
                            invoicePaymentService.getByInvoiceGroupIds(
                                listOf(memberInvoiceGroup.id),
                                true
                            )
                        } returns listOf(
                            memberInvoiceGroupCanceledEventInvoicePayment,
                            memberInvoiceGroupPendingInvoicePayment,
                        )

                        coEvery {
                            invoicePaymentService.getByInvoiceLiquidationIds(
                                listOf(invoiceLiquidation.id),
                                true
                            )
                        } returns emptyList()

                        coEvery {
                            invoicePaymentService.getByInvoicePreActivationPayment(
                                listOf(preActivationPayment.id),
                                true
                            )
                        } returns listOf(
                            preActivationPaymentApprovedInvoicePayment
                        )

                        val expectedResult = listOf(
                            invoiceLiquidation.toCompanyInvoiceResponse(listOf(memberInvoiceGroup)),
                            memberInvoiceGroup.toCompanyInvoiceResponse().copy(
                                payments = listOf(
                                    memberInvoiceGroupPendingInvoicePayment.toCompanyInvoicePayment(),
                                    memberInvoiceGroupCanceledEventInvoicePayment.toCompanyInvoicePayment()
                                )
                            ),
                            preActivationPayment.toCompanyInvoiceResponse()
                                .copy(payments = listOf(preActivationPaymentApprovedInvoicePayment.toCompanyInvoicePayment()))
                        )

                        val result =
                            service.listByCompanyId(companyId, CompanyInvoiceService.FindOptions(withPayments = true))


                        ResultAssert.assertThat(result).isSuccessWithData(expectedResult)

                        coVerifyOnce {
                            memberInvoiceGroupService.getByCompanyId(companyId)
                            invoiceLiquidationService.listByCompanyId(companyId)
                            preActivationPaymentService.listByCompanyId(companyId)

                            invoicePaymentService.getByInvoiceGroupIds(listOf(memberInvoiceGroup.id), true)
                            invoicePaymentService.getByInvoiceLiquidationIds(
                                listOf(invoiceLiquidation.id),
                                true
                            )
                            invoicePaymentService.getByInvoicePreActivationPayment(
                                listOf(preActivationPayment.id),
                                true
                            )
                        }
                    }
                }

            @Test
            fun `should return the company invoice for MEMBER_INVOICE_GROUP model with its payments`() =
                runBlocking<Unit> {
                    withFeatureFlag(FeatureNamespace.MONEY_IN, "enable_pre_activation_payment", true) {
                        coEvery { memberInvoiceGroupService.getByCompanyId(companyId) } returns listOf(
                            memberInvoiceGroup
                        )

                        coEvery {
                            invoicePaymentService.getByInvoiceGroupIds(
                                listOf(memberInvoiceGroup.id),
                                true
                            )
                        } returns listOf(
                            memberInvoiceGroupCanceledEventInvoicePayment,
                            memberInvoiceGroupPendingInvoicePayment,
                        )

                        val expectedResult = listOf(
                            memberInvoiceGroup.toCompanyInvoiceResponse().copy(
                                payments = listOf(
                                    memberInvoiceGroupPendingInvoicePayment.toCompanyInvoicePayment(),
                                    memberInvoiceGroupCanceledEventInvoicePayment.toCompanyInvoicePayment()
                                )
                            ),
                        )

                        val result = service.listByCompanyId(
                            companyId, CompanyInvoiceService.FindOptions(
                                models = listOf(CompanyInvoiceModel.MEMBER_INVOICE_GROUP),
                                withPayments = true
                            )
                        )

                        ResultAssert.assertThat(result).isSuccessWithData(expectedResult)

                        coVerifyOnce {
                            memberInvoiceGroupService.getByCompanyId(companyId)
                            invoicePaymentService.getByInvoiceGroupIds(listOf(memberInvoiceGroup.id), true)

                        }

                        coVerifyNone {
                            invoiceLiquidationService.listByCompanyId(any())
                            preActivationPaymentService.listByCompanyId(any())
                            invoicePaymentService.getByInvoicePreActivationPayment(
                                any(),
                                any()
                            )
                            invoicePaymentService.getByInvoiceLiquidationIds(
                                any(),
                                any()
                            )
                        }
                    }
                }

            @Test
            fun `should return the company invoice for PRE_ACTIVATION_PAYMENT model without its payments`() =
                runBlocking<Unit> {
                    withFeatureFlag(FeatureNamespace.MONEY_IN, "enable_pre_activation_payment", true) {
                        coEvery { preActivationPaymentService.listByCompanyId(companyId) } returns listOf(
                            preActivationPayment
                        )

                        val expectedResult = listOf(
                            preActivationPayment.toCompanyInvoiceResponse(),
                        )

                        val result = service.listByCompanyId(
                            companyId, CompanyInvoiceService.FindOptions(
                                models = listOf(CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT),
                                withPayments = false
                            )
                        )

                        ResultAssert.assertThat(result).isSuccessWithData(expectedResult)

                        coVerifyOnce {
                            preActivationPaymentService.listByCompanyId(companyId)
                        }

                        coVerifyNone {
                            invoiceLiquidationService.listByCompanyId(any())
                            memberInvoiceGroupService.getByCompanyId(any())
                            invoicePaymentService.getByInvoiceGroupIds(any(), any())
                            invoicePaymentService.getByInvoicePreActivationPayment(
                                any(),
                                any()
                            )
                            invoicePaymentService.getByInvoiceLiquidationIds(
                                any(),
                                any()
                            )
                        }
                    }
                }
        }
    }

    @Nested
    inner class ListBySubContractIdAndModelsWithPayments {
        @Test
        fun `should return the company invoice for whole models with its payments`() = runBlocking<Unit> {
            coEvery { memberInvoiceGroupService.getBySubcontractId(subContractId) } returns listOf(memberInvoiceGroup)
            coEvery { invoiceLiquidationService.getBySubContractId(subContractId) } returns listOf(invoiceLiquidation)

            coEvery { invoicePaymentService.getByInvoiceGroupIds(listOf(memberInvoiceGroup.id), true) } returns listOf(
                memberInvoiceGroupCanceledEventInvoicePayment,
                memberInvoiceGroupPendingInvoicePayment,
            )

            coEvery {
                invoicePaymentService.getByInvoiceLiquidationIds(
                    listOf(invoiceLiquidation.id),
                    true
                )
            } returns emptyList()

            val expectedResult = listOf(
                invoiceLiquidation.toCompanyInvoiceResponse(listOf(memberInvoiceGroup)),
                memberInvoiceGroup.toCompanyInvoiceResponse().copy(
                    payments = listOf(
                        memberInvoiceGroupPendingInvoicePayment.toCompanyInvoicePayment(),
                        memberInvoiceGroupCanceledEventInvoicePayment.toCompanyInvoicePayment()
                    )
                ),
            )

            val result =
                service.listBySubContractIdAndModelsWithPayments(
                    subContractId, models = listOf(
                        CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
                        CompanyInvoiceModel.INVOICE_LIQUIDATION,
                        CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT,
                    )
                )

            ResultAssert.assertThat(result).isSuccessWithData(expectedResult)

            coVerifyOnce {
                memberInvoiceGroupService.getBySubcontractId(subContractId)
                invoiceLiquidationService.getBySubContractId(subContractId)

                invoicePaymentService.getByInvoiceGroupIds(listOf(memberInvoiceGroup.id), true)
                invoicePaymentService.getByInvoiceLiquidationIds(
                    listOf(invoiceLiquidation.id),
                    true
                )
            }

            coVerifyNone {
                preActivationPaymentService.listByCompanyId(any())
                invoicePaymentService.getByInvoicePreActivationPayment(
                    any(),
                    any()
                )
            }
        }

        @Nested
        inner class WithFFEnabled {
            @Test
            fun `should return the company invoice for whole models with its payments with FF enabled`() =
                runBlocking<Unit> {
                    withFeatureFlag(FeatureNamespace.MONEY_IN, "enable_pre_activation_payment", true) {
                        coEvery { memberInvoiceGroupService.getBySubcontractId(subContractId) } returns listOf(
                            memberInvoiceGroup
                        )
                        coEvery { invoiceLiquidationService.getBySubContractId(subContractId) } returns listOf(
                            invoiceLiquidation
                        )
                        coEvery { preActivationPaymentService.getBySubcontractId(subContractId) } returns listOf(
                            preActivationPayment
                        )

                        coEvery {
                            invoicePaymentService.getByInvoiceGroupIds(
                                listOf(memberInvoiceGroup.id),
                                true
                            )
                        } returns listOf(
                            memberInvoiceGroupCanceledEventInvoicePayment,
                            memberInvoiceGroupPendingInvoicePayment,
                        )

                        coEvery {
                            invoicePaymentService.getByInvoiceLiquidationIds(
                                listOf(invoiceLiquidation.id),
                                true
                            )
                        } returns emptyList()

                        coEvery {
                            invoicePaymentService.getByInvoicePreActivationPayment(
                                listOf(preActivationPayment.id),
                                true
                            )
                        } returns listOf(
                            preActivationPaymentApprovedInvoicePayment
                        )

                        val expectedResult = listOf(
                            invoiceLiquidation.toCompanyInvoiceResponse(listOf(memberInvoiceGroup)),
                            memberInvoiceGroup.toCompanyInvoiceResponse().copy(
                                payments = listOf(
                                    memberInvoiceGroupPendingInvoicePayment.toCompanyInvoicePayment(),
                                    memberInvoiceGroupCanceledEventInvoicePayment.toCompanyInvoicePayment()
                                )
                            ),
                            preActivationPayment.toCompanyInvoiceResponse()
                                .copy(payments = listOf(preActivationPaymentApprovedInvoicePayment.toCompanyInvoicePayment()))
                        )

                        val result =
                            service.listBySubContractIdAndModelsWithPayments(subContractId)


                        ResultAssert.assertThat(result).isSuccessWithData(expectedResult)

                        coVerifyOnce {
                            memberInvoiceGroupService.getBySubcontractId(subContractId)
                            invoiceLiquidationService.getBySubContractId(subContractId)
                            preActivationPaymentService.getBySubcontractId(subContractId)

                            invoicePaymentService.getByInvoiceGroupIds(listOf(memberInvoiceGroup.id), true)
                            invoicePaymentService.getByInvoiceLiquidationIds(
                                listOf(invoiceLiquidation.id),
                                true
                            )
                            invoicePaymentService.getByInvoicePreActivationPayment(
                                listOf(preActivationPayment.id),
                                true
                            )
                        }
                    }
                }
        }
    }

    @Nested
    inner class ListByBillingAccountablePartyIdAndModelsWithPayments {
        private val billingAccountablePartyId = RangeUUID.generate()

        @Test
        fun `should return the company invoices`() = runBlocking<Unit> {
            coEvery { memberInvoiceGroupService.getByBillingAccountablePartyId(billingAccountablePartyId) } returns listOf(
                memberInvoiceGroup
            )
            coEvery { invoiceLiquidationService.getByBillingAccountablePartyId(billingAccountablePartyId) } returns listOf(
                invoiceLiquidation
            )

            coEvery {
                invoicePaymentService.getByInvoiceGroupIds(
                    listOf(memberInvoiceGroup.id),
                    true
                )
            } returns listOf(
                memberInvoiceGroupPendingInvoicePayment,
                memberInvoiceGroupCanceledEventInvoicePayment
            )

            coEvery {
                invoicePaymentService.getByInvoiceLiquidationIds(
                    listOf(invoiceLiquidation.id),
                    true
                )
            } returns emptyList()

            val expectedResult = listOf(
                invoiceLiquidation.toCompanyInvoiceResponse(listOf(memberInvoiceGroup)),
                memberInvoiceGroup.toCompanyInvoiceResponse().copy(
                    payments = listOf(
                        memberInvoiceGroupPendingInvoicePayment.toCompanyInvoicePayment(),
                        memberInvoiceGroupCanceledEventInvoicePayment.toCompanyInvoicePayment()
                    )
                )
            )

            val result = service.listByBillingAccountablePartyIdAndModelsWithPayments(
                billingAccountablePartyId,
                models = listOf(
                    CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
                    CompanyInvoiceModel.INVOICE_LIQUIDATION,
                    CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT,
                )
            )

            ResultAssert.assertThat(result).isSuccessWithData(expectedResult)

            coVerifyOnce {
                memberInvoiceGroupService.getByBillingAccountablePartyId(billingAccountablePartyId)
                invoiceLiquidationService.getByBillingAccountablePartyId(billingAccountablePartyId)
                invoicePaymentService.getByInvoiceGroupIds(listOf(memberInvoiceGroup.id), true)
                invoicePaymentService.getByInvoiceLiquidationIds(listOf(invoiceLiquidation.id), true)
            }

            coVerifyNone {
                preActivationPaymentService wasNot called
            }
        }

        @Nested
        inner class WithFFEnabled {
            @Test
            fun `should return the company invoice for whole models with its payments with FF enabled`() =
                runBlocking<Unit> {
                    withFeatureFlag(FeatureNamespace.MONEY_IN, "enable_pre_activation_payment", true) {
                        coEvery { memberInvoiceGroupService.getByBillingAccountablePartyId(billingAccountablePartyId) } returns listOf(
                            memberInvoiceGroup
                        )
                        coEvery { invoiceLiquidationService.getByBillingAccountablePartyId(billingAccountablePartyId) } returns listOf(
                            invoiceLiquidation
                        )
                        coEvery {
                            preActivationPaymentService.getByBillingAccountablePartyId(
                                billingAccountablePartyId
                            )
                        } returns listOf(
                            preActivationPayment
                        )

                        coEvery {
                            invoicePaymentService.getByInvoiceGroupIds(
                                listOf(memberInvoiceGroup.id),
                                true
                            )
                        } returns listOf(
                            memberInvoiceGroupCanceledEventInvoicePayment,
                            memberInvoiceGroupPendingInvoicePayment,
                        )

                        coEvery {
                            invoicePaymentService.getByInvoiceLiquidationIds(
                                listOf(invoiceLiquidation.id),
                                true
                            )
                        } returns emptyList()

                        coEvery {
                            invoicePaymentService.getByInvoicePreActivationPayment(
                                listOf(preActivationPayment.id),
                                true
                            )
                        } returns listOf(
                            preActivationPaymentApprovedInvoicePayment
                        )

                        val expectedResult = listOf(
                            invoiceLiquidation.toCompanyInvoiceResponse(listOf(memberInvoiceGroup)),
                            memberInvoiceGroup.toCompanyInvoiceResponse().copy(
                                payments = listOf(
                                    memberInvoiceGroupPendingInvoicePayment.toCompanyInvoicePayment(),
                                    memberInvoiceGroupCanceledEventInvoicePayment.toCompanyInvoicePayment()
                                )
                            ),
                            preActivationPayment.toCompanyInvoiceResponse()
                                .copy(payments = listOf(preActivationPaymentApprovedInvoicePayment.toCompanyInvoicePayment()))
                        )

                        val result =
                            service.listByBillingAccountablePartyIdAndModelsWithPayments(
                                billingAccountablePartyId
                            )

                        ResultAssert.assertThat(result).isSuccessWithData(expectedResult)

                        coVerifyOnce {
                            memberInvoiceGroupService.getByBillingAccountablePartyId(billingAccountablePartyId)
                            invoiceLiquidationService.getByBillingAccountablePartyId(billingAccountablePartyId)
                            preActivationPaymentService.getByBillingAccountablePartyId(billingAccountablePartyId)

                            invoicePaymentService.getByInvoiceGroupIds(listOf(memberInvoiceGroup.id), true)
                            invoicePaymentService.getByInvoiceLiquidationIds(
                                listOf(invoiceLiquidation.id),
                                true
                            )
                            invoicePaymentService.getByInvoicePreActivationPayment(
                                listOf(preActivationPayment.id),
                                true
                            )
                        }
                    }
                }
        }
    }

    @Nested
    inner class ListByIdsAndModel {

        @Test
        fun `should return the company invoice for whole models with its payments`() = runBlocking<Unit> {
            coEvery {
                memberInvoiceGroupService.getByIds(
                    listOf(memberInvoiceGroup.id),
                )
            } returns listOf(memberInvoiceGroup)

            coEvery { invoiceLiquidationService.listByIds(listOf(invoiceLiquidation.id)) } returns listOf(
                invoiceLiquidation
            )

            coEvery {
                invoicePaymentService.getByInvoiceGroupIds(
                    listOf(memberInvoiceGroup.id),
                    true
                )
            } returns listOf(
                memberInvoiceGroupCanceledEventInvoicePayment,
                memberInvoiceGroupPendingInvoicePayment,
            )

            coEvery {
                invoicePaymentService.getByInvoiceLiquidationIds(
                    listOf(invoiceLiquidation.id),
                    true
                )
            } returns emptyList()

            val expectedResult = listOf(
                invoiceLiquidation.toCompanyInvoiceResponse(listOf(memberInvoiceGroup)),
                memberInvoiceGroup.toCompanyInvoiceResponse().copy(
                    payments = listOf(
                        memberInvoiceGroupPendingInvoicePayment.toCompanyInvoicePayment(),
                        memberInvoiceGroupCanceledEventInvoicePayment.toCompanyInvoicePayment()
                    )
                ),
            )

            val result = service.listByIdsAndModel(
                CompanyInvoiceService.ListByIdsAndModelPayload(
                    invoiceIds = listOf(
                        memberInvoiceGroup.id to CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
                        invoiceLiquidation.id to CompanyInvoiceModel.INVOICE_LIQUIDATION,
                        preActivationPayment.id to CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT,
                    ),
                    withPayments = true,
                )
            )


            ResultAssert.assertThat(result).isSuccessWithData(expectedResult)

            coVerifyOnce {
                memberInvoiceGroupService.getByIds(listOf(memberInvoiceGroup.id))
                invoiceLiquidationService.listByIds(listOf(invoiceLiquidation.id))

                invoicePaymentService.getByInvoiceGroupIds(listOf(memberInvoiceGroup.id), true)
                invoicePaymentService.getByInvoiceLiquidationIds(
                    listOf(invoiceLiquidation.id),
                    true
                )
            }

            coVerifyNone {
                preActivationPaymentService.listByCompanyId(any())
                invoicePaymentService.getByInvoicePreActivationPayment(
                    any(),
                    any()
                )
            }
        }

        @Test
        fun `should return the company invoice for whole models with its member invoices`() = runBlocking<Unit> {
            val memberInvoiceGroupIds = listOf(memberInvoiceGroup.id)

            val product = TestModelFactory.buildProduct()
            val person = TestModelFactory.buildPerson()
            val member = TestModelFactory.buildMember(productId = product.id, personId = person.id)
            val beneficiary = TestModelFactory.buildBeneficiary(memberId = member.id)
            val memberInvoice =
                TestModelFactory.buildMemberInvoice(
                    memberInvoiceGroupId = memberInvoiceGroup.id,
                    memberId = member.id
                )

            coEvery {
                memberInvoiceGroupService.getByIds(
                    listOf(memberInvoiceGroup.id),
                )
            } returns listOf(memberInvoiceGroup)

            coEvery { invoiceLiquidationService.listByIds(listOf(invoiceLiquidation.id)) } returns listOf(
                invoiceLiquidation
            )

            coEvery {
                invoicesService.listByMemberInvoiceGroupIdsPaginated(
                    memberInvoiceGroupIds,
                    false,
                    0,
                    CHUNK_GROUP_SIZE
                )
            } returns listOf(
                memberInvoice
            )
            coEvery {
                memberService.findByIds(listOf(member.id))
            } returns listOf(member)

            coEvery { personService.findByIds(listOf(member.personId.toString())) } returns listOf(person)

            coEvery { beneficiaryService.findByMemberIds(listOf(member.id)) } returns listOf(beneficiary)

            coEvery {
                productService.findByIds(
                    listOf(member.selectedProduct.id),
                    ProductService.FindOptions(
                        withPriceListing = false,
                        withBundles = false
                    )
                )
            } returns listOf(product)

            val expectedBeneficiaryDetail = MemberInvoiceBeneficiaryDetail(
                memberId = member.id,
                firstName = person.firstName,
                lastName = person.lastName,
                isMemberActive = member.active,
                nationalId = person.nationalId,
                nationalIdHolder = null,
                type = beneficiary.type,
                productTitle = product.title,
                productDisplayName = product.salesProductName,
            )

            val expectedResult = listOf(
                invoiceLiquidation.toCompanyInvoiceResponse(listOf(memberInvoiceGroup)),
                memberInvoiceGroup.toCompanyInvoiceResponse().copy(
                    memberInvoiceDetails = listOf(
                        memberInvoice.toMemberInvoiceDetail(expectedBeneficiaryDetail),
                    )
                ),
            )

            val result = service.listByIdsAndModel(
                CompanyInvoiceService.ListByIdsAndModelPayload(
                    invoiceIds = listOf(
                        memberInvoiceGroup.id to CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
                        invoiceLiquidation.id to CompanyInvoiceModel.INVOICE_LIQUIDATION,
                        preActivationPayment.id to CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT,
                    ),
                    withMemberInvoiceDetails = true,
                )
            )

            ResultAssert.assertThat(result).isSuccessWithData(expectedResult)

            coVerifyOnce {
                memberInvoiceGroupService.getByIds(listOf(memberInvoiceGroup.id))
                invoiceLiquidationService.listByIds(listOf(invoiceLiquidation.id))
                invoicesService.listByMemberInvoiceGroupIdsPaginated(
                    memberInvoiceGroupIds,
                    false,
                    0,
                    CHUNK_GROUP_SIZE
                )
                memberService.findByIds(listOf(member.id))
                personService.findByIds(listOf(member.personId.toString()))
                beneficiaryService.findByMemberIds(listOf(member.id))
                productService.findByIds(
                    listOf(member.selectedProduct.id),
                    ProductService.FindOptions(
                        withPriceListing = false,
                        withBundles = false
                    )
                )
            }

            coVerifyNone {
                preActivationPaymentService.listByCompanyId(any())
                invoicePaymentService wasNot called
            }
        }

        @Nested
        inner class WithFFEnabled {
            @Test
            fun `should return the company invoice for whole models with its member invoices`() =
                runBlocking<Unit> {
                    withFeatureFlag(FeatureNamespace.MONEY_IN, "enable_pre_activation_payment", true) {
                        val memberInvoiceGroupIds = listOf(memberInvoiceGroup.id)
                        val preActivationPaymentIds = listOf(preActivationPayment.id)

                        val product = TestModelFactory.buildProduct()
                        val person = TestModelFactory.buildPerson()
                        val member = TestModelFactory.buildMember(productId = product.id, personId = person.id)
                        val beneficiary = TestModelFactory.buildBeneficiary(memberId = member.id)
                        val memberInvoiceFromMIG = TestModelFactory.buildMemberInvoice(
                            memberInvoiceGroupId = memberInvoiceGroup.id,
                            preActivationPaymentId = null,
                            memberId = member.id,
                        )

                        val memberInvoiceFromPAP = TestModelFactory.buildMemberInvoice(
                            memberInvoiceGroupId = null,
                            preActivationPaymentId = preActivationPayment.id,
                            memberId = member.id,
                            referenceDate = LocalDate.now().minusMonths(1)
                        )

                        coEvery {
                            memberInvoiceGroupService.getByIds(
                                listOf(memberInvoiceGroup.id),
                            )
                        } returns listOf(memberInvoiceGroup)

                        coEvery {
                            invoiceLiquidationService.listByIds(listOf(invoiceLiquidation.id))
                        } returns listOf(invoiceLiquidation)

                        coEvery {
                            preActivationPaymentService.listByIds(listOf(preActivationPayment.id))
                        } returns listOf(preActivationPayment)

                        coEvery {
                            invoicesService.listByMemberInvoiceGroupIdsPaginated(
                                memberInvoiceGroupIds,
                                false,
                                0,
                                CHUNK_GROUP_SIZE
                            )
                        } returns listOf(
                            memberInvoiceFromMIG
                        )
                        coEvery {
                            invoicesService.listByPreActivationPaymentIdsPaginated(
                                preActivationPaymentIds,
                                false,
                                0,
                                CHUNK_GROUP_SIZE
                            )
                        } returns listOf(
                            memberInvoiceFromPAP
                        )
                        coEvery {
                            memberService.findByIds(listOf(member.id))
                        } returns listOf(member)

                        coEvery { personService.findByIds(listOf(member.personId.toString())) } returns listOf(
                            person
                        )

                        coEvery { beneficiaryService.findByMemberIds(listOf(member.id)) } returns listOf(beneficiary)

                        coEvery {
                            productService.findByIds(
                                listOf(member.selectedProduct.id),
                                ProductService.FindOptions(
                                    withPriceListing = false,
                                    withBundles = false
                                )
                            )
                        } returns listOf(product)

                        val expectedBeneficiaryDetail = MemberInvoiceBeneficiaryDetail(
                            memberId = member.id,
                            firstName = person.firstName,
                            lastName = person.lastName,
                            isMemberActive = member.active,
                            nationalId = person.nationalId,
                            nationalIdHolder = null,
                            type = beneficiary.type,
                            productTitle = product.title,
                            productDisplayName = product.salesProductName,
                        )

                        val expectedResult = listOf(
                            invoiceLiquidation.toCompanyInvoiceResponse(listOf(memberInvoiceGroup)),
                            memberInvoiceGroup.toCompanyInvoiceResponse().copy(
                                memberInvoiceDetails = listOf(
                                    memberInvoiceFromMIG.toMemberInvoiceDetail(expectedBeneficiaryDetail),
                                )
                            ),
                            preActivationPayment.toCompanyInvoiceResponse()
                                .copy(
                                    memberInvoiceDetails = listOf(
                                        memberInvoiceFromPAP.toMemberInvoiceDetail(
                                            expectedBeneficiaryDetail
                                        )
                                    )
                                )
                        )

                        val result = service.listByIdsAndModel(
                            CompanyInvoiceService.ListByIdsAndModelPayload(
                                invoiceIds = listOf(
                                    memberInvoiceGroup.id to CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
                                    invoiceLiquidation.id to CompanyInvoiceModel.INVOICE_LIQUIDATION,
                                    preActivationPayment.id to CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT,
                                ),
                                withMemberInvoiceDetails = true,
                            )
                        )

                        ResultAssert.assertThat(result).isSuccessWithData(expectedResult)

                        coVerifyOnce {
                            memberInvoiceGroupService.getByIds(listOf(memberInvoiceGroup.id))
                            invoiceLiquidationService.listByIds(listOf(invoiceLiquidation.id))
                            preActivationPaymentService.listByIds(listOf(preActivationPayment.id))
                            invoicesService.listByMemberInvoiceGroupIdsPaginated(
                                memberInvoiceGroupIds,
                                false,
                                0,
                                CHUNK_GROUP_SIZE
                            )
                            invoicesService.listByPreActivationPaymentIdsPaginated(
                                preActivationPaymentIds,
                                false,
                                0,
                                CHUNK_GROUP_SIZE
                            )
                            memberService.findByIds(listOf(member.id))
                            personService.findByIds(listOf(member.personId.toString()))
                            beneficiaryService.findByMemberIds(listOf(member.id))
                            productService.findByIds(
                                listOf(member.selectedProduct.id),
                                ProductService.FindOptions(
                                    withPriceListing = false,
                                    withBundles = false
                                )
                            )
                        }

                        coVerifyNone {
                            invoicePaymentService wasNot called
                        }
                    }
                }

            @Test
            fun `should return the company invoice for whole models with its payments with FF enabled`() =
                runBlocking<Unit> {
                    withFeatureFlag(FeatureNamespace.MONEY_IN, "enable_pre_activation_payment", true) {
                        coEvery {
                            memberInvoiceGroupService.getByIds(
                                listOf(memberInvoiceGroup.id),
                            )
                        } returns listOf(memberInvoiceGroup)

                        coEvery {
                            invoiceLiquidationService.listByIds(listOf(invoiceLiquidation.id))
                        } returns listOf(invoiceLiquidation)

                        coEvery {
                            preActivationPaymentService.listByIds(listOf(preActivationPayment.id))
                        } returns listOf(preActivationPayment)

                        coEvery {
                            invoicePaymentService.getByInvoiceGroupIds(
                                listOf(memberInvoiceGroup.id),
                                true
                            )
                        } returns listOf(
                            memberInvoiceGroupCanceledEventInvoicePayment,
                            memberInvoiceGroupPendingInvoicePayment,
                        )

                        coEvery {
                            invoicePaymentService.getByInvoiceLiquidationIds(
                                listOf(invoiceLiquidation.id),
                                true
                            )
                        } returns emptyList()

                        coEvery {
                            invoicePaymentService.getByInvoicePreActivationPayment(
                                listOf(preActivationPayment.id),
                                true
                            )
                        } returns listOf(
                            preActivationPaymentApprovedInvoicePayment
                        )

                        val expectedResult = listOf(
                            invoiceLiquidation.toCompanyInvoiceResponse(listOf(memberInvoiceGroup)),
                            memberInvoiceGroup.toCompanyInvoiceResponse().copy(
                                payments = listOf(
                                    memberInvoiceGroupPendingInvoicePayment.toCompanyInvoicePayment(),
                                    memberInvoiceGroupCanceledEventInvoicePayment.toCompanyInvoicePayment()
                                )
                            ),
                            preActivationPayment.toCompanyInvoiceResponse()
                                .copy(payments = listOf(preActivationPaymentApprovedInvoicePayment.toCompanyInvoicePayment()))
                        )

                        val result = service.listByIdsAndModel(
                            CompanyInvoiceService.ListByIdsAndModelPayload(
                                invoiceIds = listOf(
                                    memberInvoiceGroup.id to CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
                                    invoiceLiquidation.id to CompanyInvoiceModel.INVOICE_LIQUIDATION,
                                    preActivationPayment.id to CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT,
                                ),
                                withPayments = true,
                            )
                        )

                        ResultAssert.assertThat(result).isSuccessWithData(expectedResult)

                        coVerifyOnce {
                            memberInvoiceGroupService.getByIds(listOf(memberInvoiceGroup.id))
                            invoiceLiquidationService.listByIds(listOf(invoiceLiquidation.id))
                            preActivationPaymentService.listByIds(listOf(preActivationPayment.id))

                            invoicePaymentService.getByInvoiceGroupIds(listOf(memberInvoiceGroup.id), true)
                            invoicePaymentService.getByInvoiceLiquidationIds(
                                listOf(invoiceLiquidation.id),
                                true
                            )
                            invoicePaymentService.getByInvoicePreActivationPayment(
                                listOf(preActivationPayment.id),
                                true
                            )
                        }
                    }
                }
        }
    }
}
