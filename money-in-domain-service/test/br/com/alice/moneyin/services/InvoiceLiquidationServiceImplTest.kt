package br.com.alice.moneyin.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atBeginningOfTheMonth
import br.com.alice.common.core.extensions.atEndOfTheMonth
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CancellationReason
import br.com.alice.data.layer.models.InvoiceLiquidationStatus
import br.com.alice.data.layer.models.InvoicePaymentOrigin
import br.com.alice.data.layer.models.InvoiceStatus
import br.com.alice.data.layer.models.MemberInvoiceGroupStatus
import br.com.alice.common.PaymentMethod
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.data.layer.services.InvoiceLiquidationModelDataService
import br.com.alice.moneyin.client.InvoiceLiquidationService
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.converters.toModel
import br.com.alice.moneyin.model.InvalidInvoiceLiquidationReasonException
import br.com.alice.moneyin.notification.InvoiceNotifier
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.Test

class InvoiceLiquidationServiceImplTest {

    private val invoiceLiquidationDataService: InvoiceLiquidationModelDataService = mockk()
    private val invoiceNotifier: InvoiceNotifier = mockk()
    private val memberInvoiceGroupService: MemberInvoiceGroupService = mockk()
    private val invoicePaymentService: InvoicePaymentService = mockk()
    private val invoicesService: InvoicesService = mockk()

    private val invoiceLiquidationService = InvoiceLiquidationServiceImpl(
        invoiceLiquidationDataService,
        invoiceNotifier,
        memberInvoiceGroupService,
        invoicePaymentService,
        invoicesService,
    )

    companion object {
        @JvmStatic
        fun createOptions() = listOf(
            arrayOf(PaymentReason.B2C_LIQUIDATION, PaymentMethod.BOLETO),
            arrayOf(PaymentReason.B2B_LIQUIDATION, PaymentMethod.BOLEPIX),
        )
    }

    @Test
    fun `#add should add invoice liquidation`() = runBlocking {
        val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation()
        val invoiceLiquidationModel = invoiceLiquidation.toModel()

        coEvery { invoiceLiquidationDataService.add(any()) } returns invoiceLiquidationModel
        coEvery {
            invoiceLiquidationDataService.findOne(queryEq {
                where {
                    this.externalId.eq(invoiceLiquidation.externalId) and status.diff(
                        InvoiceLiquidationStatus.CANCELED
                    )
                }
            })
        } returns NotFoundException("")

        val result = invoiceLiquidationService.add(invoiceLiquidation)
        assertThat(result).isSuccessWithData(invoiceLiquidation)

        coVerifyOnce {
            invoiceLiquidationDataService.add(invoiceLiquidationModel)
            invoiceLiquidationDataService.findOne(queryEq {
                where {
                    this.externalId.eq(invoiceLiquidation.externalId) and status.diff(
                        InvoiceLiquidationStatus.CANCELED
                    )
                }
            })
        }
    }

    @Test
    fun `#add should get the invoice liquidation when trying to add one and the externalid already registered`() =
        runBlocking {
            val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation()
            val invoiceLiquidationModel = invoiceLiquidation.toModel()

            coEvery {
                invoiceLiquidationDataService.findOne(queryEq {
                    where {
                        this.externalId.eq(invoiceLiquidation.externalId) and status.diff(
                            InvoiceLiquidationStatus.CANCELED
                        )
                    }
                })
            } returns invoiceLiquidationModel

            val result = invoiceLiquidationService.add(invoiceLiquidation)
            assertThat(result).isSuccessWithData(invoiceLiquidation)

            coVerifyOnce {
                invoiceLiquidationDataService.findOne(queryEq {
                    where {
                        this.externalId.eq(invoiceLiquidation.externalId) and status.diff(
                            InvoiceLiquidationStatus.CANCELED
                        )
                    }
                })
            }

            coVerifyNone { invoiceLiquidationDataService.add(invoiceLiquidationModel) }
        }

    @Test
    fun `#get should get invoice liquidation`() = runBlocking {
        val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation()
        val invoiceLiquidationModel = invoiceLiquidation.toModel()

        coEvery { invoiceLiquidationDataService.get(any()) } returns invoiceLiquidationModel

        val result = invoiceLiquidationService.get(invoiceLiquidation.id)
        assertThat(result).isSuccessWithData(invoiceLiquidation)

        coVerifyOnce { invoiceLiquidationDataService.get(invoiceLiquidation.id) }
    }

    @Test
    fun `#udpate should update invoice liquidation`() = runBlocking {
        val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation()
        val invoiceLiquidationModel = invoiceLiquidation.toModel()

        coEvery { invoiceLiquidationDataService.update(any()) } returns invoiceLiquidationModel

        val result = invoiceLiquidationService.update(invoiceLiquidation)
        assertThat(result).isSuccessWithData(invoiceLiquidation)

        coVerifyOnce { invoiceLiquidationDataService.update(invoiceLiquidationModel) }
    }

    @Nested
    inner class Create {

        @ParameterizedTest
        @MethodSource("br.com.alice.moneyin.services.InvoiceLiquidationServiceImplTest#createOptions")
        fun `#add should create an invoice liquidation`(paymentReason: PaymentReason, paymentMethod: PaymentMethod) =
            runBlocking {
                val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation(
                    companyId = RangeUUID.generate(),
                    subcontractId = RangeUUID.generate(),
                )
                val invoiceLiquidationModel = invoiceLiquidation.toModel()
                val invoiceLiquidationUpdated = invoiceLiquidation.copy(status = InvoiceLiquidationStatus.PROCESSED)
                val invoiceLiquidationUpdatedModel = invoiceLiquidationUpdated.toModel()

                val invoicePayment = TestModelFactory.buildInvoicePayment()

                coEvery {
                    invoiceLiquidationDataService.add(match {
                        it.externalId == invoiceLiquidation.externalId &&
                                it.billingAccountablePartyId == invoiceLiquidation.billingAccountablePartyId &&
                                it.dueDate == invoiceLiquidation.dueDate
                    })
                } returns invoiceLiquidationModel

                coEvery {
                    invoiceLiquidationDataService.findOne(queryEq {
                        where {
                            externalId.eq(invoiceLiquidation.externalId) and status.diff(
                                InvoiceLiquidationStatus.CANCELED
                            )
                        }
                    })
                } returns NotFoundException("")

                coEvery {
                    invoicePaymentService.createPaymentForLiquidation(
                        invoiceLiquidation,
                        paymentMethod,
                        paymentReason,
                        InvoicePaymentOrigin.ISSUED_BY_HEALTHCARE_OPS,
                    )
                } returns invoicePayment

                coEvery {
                    invoiceLiquidationDataService.update(invoiceLiquidationUpdatedModel)
                } returns invoiceLiquidationUpdatedModel

                val result = invoiceLiquidationService.create(
                    InvoiceLiquidationService.CreateInvoiceLiquidationPayload(
                        externalId = invoiceLiquidation.externalId,
                        amount = invoiceLiquidation.amount,
                        discount = invoiceLiquidation.discount,
                        dueDate = invoiceLiquidation.dueDate,
                        memberInvoiceGroupIds = invoiceLiquidation.memberInvoiceGroupIds,
                        billingAccountablePartyId = invoiceLiquidation.billingAccountablePartyId,
                        companyIds = listOf(invoiceLiquidation.companyId!!),
                        subcontractIds = listOf(invoiceLiquidation.subcontractId!!),
                        companyId = invoiceLiquidation.companyId,
                        subcontractId = invoiceLiquidation.subcontractId,
                        installment = invoiceLiquidation.installment,
                        totalInstallments = invoiceLiquidation.totalInstallments,
                        reason = paymentReason,
                        origin = InvoicePaymentOrigin.ISSUED_BY_HEALTHCARE_OPS,
                        businessType = invoiceLiquidation.businessType,
                    )
                )

                assertThat(result).isSuccessWithData(invoiceLiquidation)

                coVerifyOnce {
                    invoiceLiquidationDataService.findOne(queryEq {
                        where {
                            externalId.eq(invoiceLiquidation.externalId) and status.diff(
                                InvoiceLiquidationStatus.CANCELED
                            )
                        }
                    })

                    invoiceLiquidationDataService.add(match {
                        it.externalId == invoiceLiquidation.externalId &&
                                it.billingAccountablePartyId == invoiceLiquidation.billingAccountablePartyId &&
                                it.dueDate == invoiceLiquidation.dueDate
                    })

                    invoicePaymentService.createPaymentForLiquidation(
                        invoiceLiquidation,
                        paymentMethod,
                        paymentReason,
                        InvoicePaymentOrigin.ISSUED_BY_HEALTHCARE_OPS,
                    )

                    invoiceLiquidationDataService.update(invoiceLiquidationUpdatedModel)
                }
            }

        @ParameterizedTest
        @MethodSource("br.com.alice.moneyin.services.InvoiceLiquidationServiceImplTest#createOptions")
        fun `#add should get an invoice liquidation when it already exists`(
            paymentReason: PaymentReason,
            paymentMethod: PaymentMethod
        ) =
            runBlocking {
                val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation(
                    companyId = RangeUUID.generate(),
                    subcontractId = RangeUUID.generate(),
                )
                val invoiceLiquidationModel = invoiceLiquidation.toModel()
                val invoiceLiquidationUpdated = invoiceLiquidation.copy(status = InvoiceLiquidationStatus.PROCESSED)
                val invoiceLiquidationUpdatedModel = invoiceLiquidationUpdated.toModel()

                val invoicePayment = TestModelFactory.buildInvoicePayment()

                coEvery {
                    invoiceLiquidationDataService.findOne(queryEq {
                        where {
                            externalId.eq(invoiceLiquidation.externalId) and status.diff(
                                InvoiceLiquidationStatus.CANCELED
                            )
                        }
                    })
                } returns invoiceLiquidationModel

                coEvery {
                    invoicePaymentService.createPaymentForLiquidation(
                        invoiceLiquidation,
                        paymentMethod,
                        paymentReason,
                        InvoicePaymentOrigin.ISSUED_BY_HEALTHCARE_OPS,
                    )
                } returns invoicePayment

                coEvery {
                    invoiceLiquidationDataService.update(invoiceLiquidationUpdatedModel)
                } returns invoiceLiquidationUpdatedModel

                val result = invoiceLiquidationService.create(
                    InvoiceLiquidationService.CreateInvoiceLiquidationPayload(
                        externalId = invoiceLiquidation.externalId,
                        amount = invoiceLiquidation.amount,
                        discount = invoiceLiquidation.discount,
                        dueDate = invoiceLiquidation.dueDate,
                        memberInvoiceGroupIds = invoiceLiquidation.memberInvoiceGroupIds,
                        billingAccountablePartyId = invoiceLiquidation.billingAccountablePartyId,
                        companyIds = listOf(invoiceLiquidation.companyId!!),
                        subcontractIds = listOf(invoiceLiquidation.subcontractId!!),
                        companyId = invoiceLiquidation.companyId,
                        subcontractId = invoiceLiquidation.subcontractId,
                        installment = invoiceLiquidation.installment,
                        totalInstallments = invoiceLiquidation.totalInstallments,
                        reason = paymentReason,
                        origin = InvoicePaymentOrigin.ISSUED_BY_HEALTHCARE_OPS,
                        businessType = invoiceLiquidation.businessType,
                    )
                )

                assertThat(result).isSuccessWithData(invoiceLiquidation)

                coVerifyOnce {
                    invoiceLiquidationDataService.findOne(queryEq {
                        where {
                            externalId.eq(invoiceLiquidation.externalId) and status.diff(
                                InvoiceLiquidationStatus.CANCELED
                            )
                        }
                    })

                    invoicePaymentService.createPaymentForLiquidation(
                        invoiceLiquidation,
                        paymentMethod,
                        paymentReason,
                        InvoicePaymentOrigin.ISSUED_BY_HEALTHCARE_OPS,
                    )

                    invoiceLiquidationDataService.update(invoiceLiquidationUpdatedModel)
                }

                coVerifyNone {
                    invoiceLiquidationDataService.add(any())
                }
            }

        @Test
        fun `#should not create an invoice liquidation when the reason is not a LIQUIDATION`() =
            runBlocking {
                val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation()

                val result = invoiceLiquidationService.create(
                    InvoiceLiquidationService.CreateInvoiceLiquidationPayload(
                        externalId = invoiceLiquidation.externalId,
                        amount = invoiceLiquidation.amount,
                        discount = invoiceLiquidation.discount,
                        dueDate = invoiceLiquidation.dueDate,
                        memberInvoiceGroupIds = invoiceLiquidation.memberInvoiceGroupIds,
                        billingAccountablePartyId = invoiceLiquidation.billingAccountablePartyId,
                        companyIds = emptyList(),
                        subcontractIds = emptyList(),
                        companyId = null,
                        subcontractId = null,
                        installment = invoiceLiquidation.installment,
                        totalInstallments = invoiceLiquidation.totalInstallments,
                        reason = PaymentReason.REGULAR_PAYMENT,
                        origin = InvoicePaymentOrigin.ISSUED_BY_HEALTHCARE_OPS,
                        businessType = invoiceLiquidation.businessType,
                    )
                )

                assertThat(result).isFailureOfType(InvalidInvoiceLiquidationReasonException::class)

                coVerifyNone {
                    invoiceLiquidationDataService.add(any())
                    invoicePaymentService.createPaymentForLiquidation(
                        any(),
                        any(),
                        any(),
                        any(),
                    )
                }
            }
    }

    @Nested
    inner class PayMemberInvoiceGroups {
        @Test
        fun `#should pay the member invoices by liquidation`() = runBlocking {
            val memberInvoiceGroup1 = TestModelFactory.buildMemberInvoiceGroup()
            val memberInvoiceGroup2 = TestModelFactory.buildMemberInvoiceGroup()

            val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation(
                memberInvoiceGroupIds = listOf(
                    memberInvoiceGroup1.id,
                    memberInvoiceGroup2.id
                )
            )

            coEvery {
                memberInvoiceGroupService.getByIds(invoiceLiquidation.memberInvoiceGroupIds)
            } returns listOf(memberInvoiceGroup1, memberInvoiceGroup2)

            coEvery {
                memberInvoiceGroupService.markAsPaidByLiquidation(memberInvoiceGroup1, listOf(invoiceLiquidation.id))
            } returns memberInvoiceGroup1.markAsPaidByLiquidation(listOf(invoiceLiquidation.id))

            coEvery {
                memberInvoiceGroupService.markAsPaidByLiquidation(memberInvoiceGroup2, listOf(invoiceLiquidation.id))
            } returns memberInvoiceGroup2.markAsPaidByLiquidation(listOf(invoiceLiquidation.id))

            val result = invoiceLiquidationService.payMemberInvoiceGroups(listOf(invoiceLiquidation))

            assertThat(result).isSuccessWithData(true)

            coVerifyOnce {
                memberInvoiceGroupService.getByIds(invoiceLiquidation.memberInvoiceGroupIds)
                memberInvoiceGroupService.markAsPaidByLiquidation(memberInvoiceGroup1, listOf(invoiceLiquidation.id))
                memberInvoiceGroupService.markAsPaidByLiquidation(memberInvoiceGroup2, listOf(invoiceLiquidation.id))
            }
        }

        @Test
        fun `#should return error when something is wrong`() = runBlocking {
            val memberInvoiceGroup1 = TestModelFactory.buildMemberInvoiceGroup()
            val memberInvoiceGroup2 = TestModelFactory.buildMemberInvoiceGroup()

            val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation(
                memberInvoiceGroupIds = listOf(
                    memberInvoiceGroup1.id,
                    memberInvoiceGroup2.id
                )
            )

            coEvery {
                memberInvoiceGroupService.getByIds(invoiceLiquidation.memberInvoiceGroupIds)
            } returns listOf(memberInvoiceGroup1, memberInvoiceGroup2)

            coEvery {
                memberInvoiceGroupService.markAsPaidByLiquidation(memberInvoiceGroup1, listOf(invoiceLiquidation.id))
            } returns memberInvoiceGroup1.markAsPaidByLiquidation(listOf(invoiceLiquidation.id))

            coEvery {
                memberInvoiceGroupService.markAsPaidByLiquidation(memberInvoiceGroup2, listOf(invoiceLiquidation.id))
            } returns Exception("Something is wrong")

            val result = invoiceLiquidationService.payMemberInvoiceGroups(listOf(invoiceLiquidation))

            assertThat(result).isFailureOfType(Exception::class)

            coVerifyOnce {
                memberInvoiceGroupService.getByIds(invoiceLiquidation.memberInvoiceGroupIds)
                memberInvoiceGroupService.markAsPaidByLiquidation(memberInvoiceGroup1, listOf(invoiceLiquidation.id))
                memberInvoiceGroupService.markAsPaidByLiquidation(memberInvoiceGroup2, listOf(invoiceLiquidation.id))
            }
        }
    }

    @Test
    fun `#cancel should cancel invoice liquidation`() = runBlocking {
        val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation()
        val invoiceLiquidationModel = invoiceLiquidation.toModel()
        val updatedLiquidation = invoiceLiquidation.copy(
            status = InvoiceLiquidationStatus.CANCELED
        )
        val updatedLiquidationModel = updatedLiquidation.toModel()

        coEvery { invoiceLiquidationDataService.get(any()) } returns invoiceLiquidationModel
        coEvery { invoiceLiquidationDataService.update(updatedLiquidationModel) } returns updatedLiquidationModel
        coEvery {
            invoicePaymentService.cancelByInvoiceLiquidationId(
                invoiceLiquidation.id,
                CancellationReason.CANCELED_BY_LIQUIDATION
            )
        } returns listOf()

        val result = invoiceLiquidationService.cancel(invoiceLiquidation.id)
        assertThat(result).isSuccessWithData(updatedLiquidation)

        coVerifyOnce { invoiceLiquidationDataService.get(any()) }
        coVerifyOnce { invoiceLiquidationDataService.update(any()) }
        coVerifyOnce {
            invoicePaymentService.cancelByInvoiceLiquidationId(
                invoiceLiquidation.id,
                CancellationReason.CANCELED_BY_LIQUIDATION
            )
        }
    }

    @Test
    fun `#cancel should return the invoice liquidation when it is already canceled`() = runBlocking {
        val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation(
            status = InvoiceLiquidationStatus.CANCELED
        )
        val invoiceLiquidationModel = invoiceLiquidation.toModel()

        coEvery { invoiceLiquidationDataService.get(any()) } returns invoiceLiquidationModel

        val result = invoiceLiquidationService.cancel(invoiceLiquidation.id)
        assertThat(result).isSuccessWithData(invoiceLiquidation)

        coVerifyOnce { invoiceLiquidationDataService.get(any()) }
        coVerifyNone { invoiceLiquidationDataService.update(any()) }
        coVerifyNone {
            invoicePaymentService.cancelByInvoiceLiquidationId(
                invoiceLiquidation.id,
                CancellationReason.CANCELED_BY_LIQUIDATION
            )
        }
    }

    @Test
    fun `#markAsPaid should mark invoice liquidation as paid`() = runBlocking {
        val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation()
        val invoiceLiquidationModel = invoiceLiquidation.toModel()
        val invoicePayment = TestModelFactory.buildInvoicePayment()
        val updatedLiquidation = invoiceLiquidation.copy(
            status = InvoiceLiquidationStatus.PAID
        )
        val updatedLiquidationModel = updatedLiquidation.toModel()

        val producerResult = ProducerResult(
            producedAt = LocalDateTime.now(),
            topic = "InvoiceLiquidationPaid",
            offset = 30.toLong(),
        )

        coEvery { invoiceLiquidationDataService.get(any()) } returns invoiceLiquidationModel
        coEvery { invoiceLiquidationDataService.update(updatedLiquidationModel) } returns updatedLiquidationModel
        coEvery {
            invoiceNotifier.publishPaidLiquidationInvoice(
                updatedLiquidation,
                invoicePayment
            )
        } returns producerResult

        val result = invoiceLiquidationService.markAsPaid(updatedLiquidation, invoicePayment)
        assertThat(result).isSuccessWithData(updatedLiquidation)

        coVerifyOnce { invoiceLiquidationDataService.get(any()) }
        coVerifyOnce { invoiceLiquidationDataService.update(any()) }
        coVerifyOnce { invoiceNotifier.publishPaidLiquidationInvoice(any(), any()) }
    }

    @Nested
    inner class ListByMemberInvoiceGroupIds {

        @Test
        fun `#should list by mig ids`() = runBlocking {
            val memberInvoiceGroupId = RangeUUID.generate()
            val invoiceLiquidation =
                TestModelFactory.buildInvoiceLiquidation(memberInvoiceGroupIds = listOf(memberInvoiceGroupId))
            val invoiceLiquidationModel = invoiceLiquidation.toModel()

            coEvery {
                invoiceLiquidationDataService.find(queryEq {
                    where { memberInvoiceGroupIds.containsAny(listOf(memberInvoiceGroupId)) }
                })
            } returns listOf(invoiceLiquidationModel)

            val result = invoiceLiquidationService.listByMemberInvoiceGroupIds(listOf(memberInvoiceGroupId))

            assertThat(result).isSuccessWithData(listOf(invoiceLiquidation))

            coVerifyOnce {
                invoiceLiquidationDataService.find(queryEq {
                    where { memberInvoiceGroupIds.containsAny(listOf(memberInvoiceGroupId)) }
                })
            }
        }

        @Test
        fun `#should return an empty list when the ids params is an empty list`() = runBlocking {
            val result = invoiceLiquidationService.listByMemberInvoiceGroupIds(emptyList())

            assertThat(result).isSuccessWithData(emptyList())

            coVerifyNone {
                invoiceLiquidationDataService.find(any())
            }
        }
    }

    @Nested
    inner class ListByExternalIds {

        @Test
        fun `#should list by mig ids`() = runBlocking {
            val externalId = "123"
            val invoiceLiquidation =
                TestModelFactory.buildInvoiceLiquidation(externalId = externalId)
            val invoiceLiquidationModel = invoiceLiquidation.toModel()

            coEvery {
                invoiceLiquidationDataService.find(queryEq {
                    where { this.externalId.inList(listOf(externalId)) }
                })
            } returns listOf(invoiceLiquidationModel)

            val result = invoiceLiquidationService.listByExternalIds(listOf(externalId))

            assertThat(result).isSuccessWithData(listOf(invoiceLiquidation))

            coVerifyOnce {
                invoiceLiquidationDataService.find(queryEq {
                    where { this.externalId.inList(listOf(externalId)) }
                })
            }
        }

        @Test
        fun `#should return an empty list when the ids params is an empty list`() = runBlocking {
            val result = invoiceLiquidationService.listByExternalIds(emptyList())

            assertThat(result).isSuccessWithData(emptyList())

            coVerifyNone {
                invoiceLiquidationDataService.find(any())
            }
        }
    }

    @Test
    fun `#listByPersonId - should list by personId`() = runBlocking {
        val memberInvoiceGroupId = RangeUUID.generate()
        val invoiceLiquidation =
            TestModelFactory.buildInvoiceLiquidation(memberInvoiceGroupIds = listOf(memberInvoiceGroupId))
        val invoiceLiquidationModel = invoiceLiquidation.toModel()

        val personId = PersonId()
        val memberInvoice = TestModelFactory.buildMemberInvoice(
            personId = personId,
            memberInvoiceGroupId = memberInvoiceGroupId
        )

        coEvery {
            invoicesService.listByPersonAndStatuses(personId, listOf(InvoiceStatus.CANCELED_BY_LIQUIDATION))
        } returns listOf(memberInvoice)

        coEvery {
            invoiceLiquidationDataService.find(queryEq {
                where { memberInvoiceGroupIds.containsAny(listOf(memberInvoiceGroupId)) }
            })
        } returns listOf(invoiceLiquidationModel)

        val result = invoiceLiquidationService.listByPersonId(personId)

        assertThat(result).isSuccessWithData(listOf(invoiceLiquidation))

        coVerifyOnce {
            invoicesService.listByPersonAndStatuses(personId, listOf(InvoiceStatus.CANCELED_BY_LIQUIDATION))
            invoiceLiquidationDataService.find(queryEq {
                where { memberInvoiceGroupIds.containsAny(listOf(memberInvoiceGroupId)) }
            })
        }
    }

    @Test
    fun `#listByCompanyId - should list by company id`() = runBlocking {
        val company = TestModelFactory.buildCompany(
            billingAccountablePartyId = null
        )
        val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation(
            companyId = company.id
        )
        val invoiceLiquidationModel = invoiceLiquidation.toModel()

        coEvery {
            invoiceLiquidationDataService.find(queryEq {
                where { companyIds.contains(company.id) }
            })
        } returns listOf(invoiceLiquidationModel)

        val result = invoiceLiquidationService.listByCompanyId(company.id)

        assertThat(result).isSuccessWithData(listOf(invoiceLiquidation))

        coVerifyOnce { invoiceLiquidationDataService.find(any()) }
    }

    @Test
    fun `#listByIds - should list by ids`() = runBlocking {
        val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation()

        val invoiceLiquidationModel = invoiceLiquidation.toModel()

        coEvery {
            invoiceLiquidationDataService.find(queryEq {
                where { id.inList(listOf(invoiceLiquidation.id)) }
            })
        } returns listOf(invoiceLiquidationModel)

        val result = invoiceLiquidationService.listByIds(listOf(invoiceLiquidation.id))

        assertThat(result).isSuccessWithData(listOf(invoiceLiquidation))

        coVerifyOnce { invoiceLiquidationDataService.find(any()) }
    }

    @Test
    fun `#restoreMemberInvoiceGroups should update member invoice groups and member invoices`() = runBlocking {
        val memberInvoiceId1 = RangeUUID.generate()
        val memberInvoiceId2 = RangeUUID.generate()
        val memberInvoiceGroup1 = TestModelFactory.buildMemberInvoiceGroup(memberInvoiceIds = listOf(memberInvoiceId1))
        val memberInvoiceGroup2 = TestModelFactory.buildMemberInvoiceGroup(memberInvoiceIds = listOf(memberInvoiceId2))
        val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation(
            memberInvoiceGroupIds = listOf(memberInvoiceGroup1.id, memberInvoiceGroup2.id)
        )
        val memberInvoice1 = TestModelFactory.buildMemberInvoice(
            id = memberInvoiceId1,
            memberInvoiceGroupId = memberInvoiceGroup1.id,
            status = InvoiceStatus.OPEN
        )
        val memberInvoice2 = TestModelFactory.buildMemberInvoice(
            id = memberInvoiceId2,
            memberInvoiceGroupId = memberInvoiceGroup2.id,
            status = InvoiceStatus.OPEN
        )

        coEvery { memberInvoiceGroupService.getByIds(invoiceLiquidation.memberInvoiceGroupIds) } returns listOf(
            memberInvoiceGroup1,
            memberInvoiceGroup2
        ).success()
        coEvery {
            memberInvoiceGroupService.update(
                memberInvoiceGroup1.copy(
                    invoiceLiquidationIds = emptyList(),
                    status = MemberInvoiceGroupStatus.PROCESSED
                )
            )
        } returns memberInvoiceGroup1.copy(
            invoiceLiquidationIds = emptyList(),
            status = MemberInvoiceGroupStatus.PROCESSED
        ).success()
        coEvery {
            memberInvoiceGroupService.update(
                memberInvoiceGroup2.copy(
                    invoiceLiquidationIds = emptyList(),
                    status = MemberInvoiceGroupStatus.PROCESSED
                )
            )
        } returns memberInvoiceGroup2.copy(
            invoiceLiquidationIds = emptyList(),
            status = MemberInvoiceGroupStatus.PROCESSED
        ).success()
        coEvery { invoicesService.findInvoicesByIds(listOf(memberInvoiceId1, memberInvoiceId2)) } returns listOf(
            memberInvoice1,
            memberInvoice2
        ).success()
        coEvery { invoicesService.update(memberInvoice1) } returns memberInvoice1.success()
        coEvery { invoicesService.update(memberInvoice2) } returns memberInvoice2.success()
        coEvery {
            invoicePaymentService.createInvoicePaymentForInvoiceGroup(
                any(),
                PaymentMethod.BOLEPIX,
                dueDate = LocalDate.now().plusDays(10)
            )
        } returns mockk()
        coEvery {
            invoicePaymentService.createInvoicePaymentForInvoiceGroup(
                any(),
                PaymentMethod.BOLEPIX,
                dueDate = LocalDate.now().plusDays(10)
            )
        } returns mockk()

        val result = invoiceLiquidationService.restoreMemberInvoiceGroups(invoiceLiquidation)

        assertThat(result).isSuccess()
        coVerifyOnce { memberInvoiceGroupService.getByIds(invoiceLiquidation.memberInvoiceGroupIds) }
        coVerify(exactly = 2) { memberInvoiceGroupService.update(any()) }
        coVerifyOnce { invoicesService.findInvoicesByIds(any()) }
        coVerify(exactly = 2) { invoicesService.update(any()) }
        coVerify(exactly = 2) {
            invoicePaymentService.createInvoicePaymentForInvoiceGroup(
                any(),
                any(),
                dueDate = any()
            )
        }
    }

    @Test
    fun `#restoreMemberInvoiceGroups should handle empty member invoice group ids`() = runBlocking {
        val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation(memberInvoiceGroupIds = emptyList())

        val result = invoiceLiquidationService.restoreMemberInvoiceGroups(invoiceLiquidation)

        assertThat(result).isSuccessWithData(emptyList())
        coVerifyNone { memberInvoiceGroupService.getByIds(any()) }
    }

    @Test
    fun `#getByBillingAccountableAndDueDateAndNotCanceled should list of liquidations`() = runBlocking {
        val billingAccountablePartyId = RangeUUID.generate()
        val invoiceLiquidation =
            TestModelFactory.buildInvoiceLiquidation(billingAccountablePartyId = billingAccountablePartyId)
        val invoiceLiquidationModel = invoiceLiquidation.toModel()
        val dueDate = LocalDate.now()

        coEvery {
            invoiceLiquidationDataService.find(queryEq {
                where {
                    this.billingAccountablePartyId.eq(billingAccountablePartyId)
                        .and(
                            this.dueDate.greaterEq(dueDate.atBeginningOfTheMonth())
                                .and(this.dueDate.lessEq(dueDate.atEndOfTheMonth()))
                                .and(this.status.diff(InvoiceLiquidationStatus.CANCELED))
                        )
                }
            })
        } returns listOf(invoiceLiquidationModel)

        val result = invoiceLiquidationService.getByBillingAccountableAndDueDateAndNotCanceled(
            billingAccountablePartyId,
            dueDate,
        )

        assertThat(result).isSuccessWithData(listOf(invoiceLiquidation))

        coVerifyOnce {
            invoiceLiquidationDataService.find(any())
        }
    }

    @Test
    fun `#listInvoicesNearOverdue should list invoices near overdue`() = runBlocking {
        val nearOverdueDate = LocalDate.now()
        val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation()
        val invoiceLiquidationModel = invoiceLiquidation.toModel()

        coEvery {
            invoiceLiquidationDataService.find(queryEq {
                where {
                    status.inList(
                        listOf(
                            InvoiceLiquidationStatus.PROCESSING,
                            InvoiceLiquidationStatus.PROCESSED,
                            InvoiceLiquidationStatus.WAITING_PAYMENT
                        )
                    )
                        .and(dueDate.eq(nearOverdueDate))
                }
            })
        } returns listOf(invoiceLiquidationModel)

        val result = invoiceLiquidationService.listInvoicesNearOverdue(nearOverdueDate)

        assertThat(result).isSuccessWithData(listOf(invoiceLiquidation))

        coVerifyOnce {
            invoiceLiquidationDataService.find(any())
        }
    }

    @Test
    fun `#listInvoicesNearOverdue should return empty list when no invoices found`() = runBlocking {
        val nearOverdueDate = LocalDate.now()

        coEvery {
            invoiceLiquidationDataService.find(queryEq {
                where {
                    status.inList(
                        listOf(
                            InvoiceLiquidationStatus.PROCESSING,
                            InvoiceLiquidationStatus.PROCESSED,
                            InvoiceLiquidationStatus.WAITING_PAYMENT
                        )
                    )
                        .and(dueDate.eq(nearOverdueDate))
                }
            })
        } returns emptyList()

        val result = invoiceLiquidationService.listInvoicesNearOverdue(nearOverdueDate)

        assertThat(result).isSuccessWithData(emptyList())

        coVerifyOnce {
            invoiceLiquidationDataService.find(any())
        }
    }

    @Test
    fun `#findBySubContractId should list invoices by sub contract id`() = runBlocking {
        val subContractId = RangeUUID.generate()
        val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation(subcontractIds = listOf(subContractId))

        val invoiceLiquidationModel = invoiceLiquidation.toModel()

        coEvery {
            invoiceLiquidationDataService.findBySubContractId(subContractId)
        } returns listOf(invoiceLiquidationModel)

        val result = invoiceLiquidationService.getBySubContractId(subContractId)

        assertThat(result).isSuccessWithData(listOf(invoiceLiquidation))

        coVerifyOnce { invoiceLiquidationDataService.findBySubContractId(subContractId) }
    }

    @Test
    fun `#findByBillingAccountablePartyId should list invoices by billing accountable party id`() = runBlocking {
        val billingAccountablePartyId = RangeUUID.generate()
        val invoiceLiquidation =
            TestModelFactory.buildInvoiceLiquidation(billingAccountablePartyId = billingAccountablePartyId)
        val invoiceLiquidationModel = invoiceLiquidation.toModel()

        coEvery {
            invoiceLiquidationDataService.findByBillingAccountablePartyId(billingAccountablePartyId)
        } returns listOf(invoiceLiquidationModel)

        val result = invoiceLiquidationService.getByBillingAccountablePartyId(billingAccountablePartyId)

        assertThat(result).isSuccessWithData(listOf(invoiceLiquidation))

        coVerifyOnce { invoiceLiquidationDataService.findByBillingAccountablePartyId(billingAccountablePartyId) }
    }
}
