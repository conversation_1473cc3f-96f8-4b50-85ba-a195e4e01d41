package br.com.alice.moneyin.services

import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.business.exceptions.CompanyNotFoundException
import br.com.alice.business.exceptions.SubContractDoesNotBelongToCompanyException
import br.com.alice.business.exceptions.SubContractNotFoundException
import br.com.alice.common.PaymentMethod
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atBeginningOfTheMonth
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CancellationReason
import br.com.alice.data.layer.models.InvoicePaymentOrigin
import br.com.alice.data.layer.models.InvoicePaymentStatus
import br.com.alice.data.layer.models.MemberInvoicePriceType
import br.com.alice.data.layer.models.MemberInvoiceType
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.data.layer.models.PreActivationPaymentStatus
import br.com.alice.data.layer.models.PreActivationPaymentType
import br.com.alice.data.layer.services.PreActivationPaymentModelDataService
import br.com.alice.moneyin.client.InvoiceItemService
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.client.PreActivationPaymentService
import br.com.alice.moneyin.converters.toModel
import br.com.alice.moneyin.event.PreActivationPaymentCanceledEvent
import br.com.alice.moneyin.event.PreActivationPaymentCreatedEvent
import br.com.alice.moneyin.event.PreActivationPaymentPaidEvent
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import java.math.BigDecimal
import java.time.LocalDate
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class PreActivationPaymentServiceImplTest {

    private val preActivationPaymentDataService: PreActivationPaymentModelDataService = mockk()
    private val invoicesService: InvoicesService = mockk()
    private val invoicePaymentService: InvoicePaymentService = mockk()
    private val companyService: CompanyService = mockk()
    private val invoiceItemService: InvoiceItemService = mockk()
    private val companySubcontractService: CompanySubContractService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()

    val service = PreActivationPaymentServiceImpl(
        preActivationPaymentDataService,
        companyService,
        companySubcontractService,
        invoicesService,
        invoiceItemService,
        invoicePaymentService,
        kafkaProducerService,
    )

    @AfterTest
    fun tearDown() {
        clearAllMocks()
    }


    @Nested
    inner class GenerateForB2B {
        private val referenceDate = LocalDate.of(2023, 4, 1)
        private val dueDate = LocalDate.of(2023, 4, 10)
        private val companyId = RangeUUID.generate()
        private val subContractId = RangeUUID.generate()

        private val company = TestModelFactory.buildCompany(id = companyId)
        private val subContract = TestModelFactory.buildCompanySubContract(id = subContractId, companyId = companyId)
        private val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        private val globalItems = listOf(
            TestModelFactory.buildInvoiceItem(personId = null, resolvedValue = BigDecimal("120.00"))
        )

        private val preActivationPayment =
            TestModelFactory.buildPreActivationPayment(
                billingAccountablePartyId = billingAccountableParty.id,
                externalId = null,
                referenceDate = referenceDate,
                dueDate = dueDate,
                globalItems = globalItems,
                companyId = companyId,
                companySubContractId = subContractId,
                type = PreActivationPaymentType.B2B,
                memberInvoiceIds = emptyList()
            )

        private val preActivationPaymentModel = preActivationPayment.toModel()
        private val firstMember = TestModelFactory.buildMember()
        private val secondMember = TestModelFactory.buildMember()
        private val members = listOf(firstMember, secondMember)

        private val firstMemberInvoice =
            TestModelFactory.buildMemberInvoice(
                preActivationPaymentId = preActivationPayment.id,
                memberId = firstMember.id,
                type = MemberInvoiceType.B2B_PRE_ACTIVATION_PAYMENT,
            )
        private val secondMemberInvoice =
            TestModelFactory.buildMemberInvoice(
                preActivationPaymentId = preActivationPayment.id,
                memberId = secondMember.id,
                type = MemberInvoiceType.B2B_PRE_ACTIVATION_PAYMENT,
            )
        private val memberInvoices = listOf(firstMemberInvoice, secondMemberInvoice)
        private val memberInvoiceIds = memberInvoices.map { it.id }
        private val status = listOf(
            PreActivationPaymentStatus.PAID,
            PreActivationPaymentStatus.PROCESSED,
        )

        val invoicePayment = TestModelFactory.buildInvoicePayment(preActivationPaymentId = preActivationPayment.id)

        @Test
        fun `#should create a pre activation payment`() = runBlocking<Unit> {
            val preActivationPaymentWithMemberInvoiceIds = preActivationPayment.copy(
                memberInvoiceIds = memberInvoices.map { it.id }
            )

            val preActivationPaymentWithStatus =
                preActivationPaymentWithMemberInvoiceIds.copy(status = PreActivationPaymentStatus.PROCESSED)

            val invoiceItem = TestModelFactory.buildInvoiceItem(
                companyId = companyId,
                subcontractId = subContractId,
                resolvedValue = BigDecimal("120.00")
            )

            coEvery { companyService.get(companyId) } returns company
            coEvery { companySubcontractService.get(subContractId) } returns subContract

            coEvery {
                invoiceItemService.listActiveInvoiceItemsBySubcontractId(any(), any())
            } returns listOf(invoiceItem)

            coEvery {
                invoicesService.generateForB2B(
                    subContract,
                    any(),
                    referenceDate.atBeginningOfTheMonth(),
                    dueDate.atStartOfDay(),
                    PaymentMethod.BOLEPIX,
                    MemberInvoiceType.B2B_PRE_ACTIVATION_PAYMENT,
                    MemberInvoicePriceType.FULL,
                )
            } answers {
                listOf(
                    firstMemberInvoice,
                    secondMemberInvoice
                ).success()
            }

            coEvery {
                preActivationPaymentDataService.findOne(
                    queryEq {
                        where {
                            this.companySubContractId.eq(subContractId) and this.referenceDate.eq(<EMAIL>) and this.status.inList(
                                <EMAIL>
                            )
                        }
                    })
            } returns NotFoundException()

            coEvery {
                preActivationPaymentDataService.add(
                    match {
                        it.referenceDate == referenceDate &&
                                it.dueDate == dueDate &&
                                it.billingAccountablePartyId == billingAccountableParty.id
                    }
                )
            } returns preActivationPaymentModel

            coEvery { kafkaProducerService.produce(match<PreActivationPaymentCreatedEvent> { it.payload.preActivationPayment == preActivationPayment }) } returns mockk()


            coEvery {
                invoicesService.createInvoices(
                    listOf(
                        firstMemberInvoice,
                        secondMemberInvoice
                    )
                )
            } returns listOf(firstMemberInvoice, secondMemberInvoice)

            coEvery {
                preActivationPaymentDataService.update(match {
                    it.referenceDate == referenceDate &&
                            it.dueDate == dueDate &&
                            it.memberInvoiceIds == memberInvoiceIds &&
                            it.companyId == companyId &&
                            it.companySubContractId == subContractId &&
                            it.status == PreActivationPaymentStatus.PROCESSING
                })
            } returns preActivationPaymentWithMemberInvoiceIds.toModel()

            coEvery {
                invoicePaymentService.createFromPreActivationPayment(
                    match {
                        it.referenceDate == referenceDate &&
                                it.dueDate == dueDate &&
                                it.memberInvoiceIds == memberInvoiceIds
                    },
                    PaymentMethod.BOLEPIX,
                    dueDate = dueDate,
                    origin = InvoicePaymentOrigin.UNDEFINED,
                    syncProcess = true
                )
            } returns invoicePayment

            coEvery {
                preActivationPaymentDataService.update(match {
                    it.referenceDate == referenceDate &&
                            it.dueDate == dueDate &&
                            it.billingAccountablePartyId == billingAccountableParty.id &&
                            it.status == PreActivationPaymentStatus.PROCESSED &&
                            it.memberInvoiceIds == memberInvoiceIds &&
                            it.companyId == companyId &&
                            it.companySubContractId == subContractId
                })
            } returns preActivationPaymentWithStatus.toModel()

            val result =
                service.generateForB2B(
                    companyId,
                    subContractId,
                    members,
                    billingAccountableParty.id,
                    referenceDate,
                    dueDate
                )

            ResultAssert.assertThat(result).isSuccessWithData(preActivationPaymentWithStatus)

            coVerifyOnce {
                companyService.get(companyId)
                companySubcontractService.get(subContractId)

                invoiceItemService.listActiveInvoiceItemsBySubcontractId(subContractId, referenceDate)

                preActivationPaymentDataService.findOne(
                    queryEq {
                        where {
                            this.companySubContractId.eq(subContractId) and this.referenceDate.eq(<EMAIL>) and this.status.inList(
                                <EMAIL>
                            )
                        }
                    })

                invoicesService.generateForB2B(
                    subContract,
                    any(),
                    referenceDate.atBeginningOfTheMonth(),
                    dueDate.atStartOfDay(),
                    PaymentMethod.BOLEPIX,
                    MemberInvoiceType.B2B_PRE_ACTIVATION_PAYMENT,
                    MemberInvoicePriceType.FULL,
                )

                preActivationPaymentDataService.add(
                    match {
                        it.referenceDate == referenceDate &&
                                it.dueDate == dueDate &&
                                it.billingAccountablePartyId == billingAccountableParty.id
                    }
                )

                kafkaProducerService.produce(match<PreActivationPaymentCreatedEvent> { it.payload.preActivationPayment == preActivationPayment })

                invoicesService.createInvoices(
                    listOf(
                        firstMemberInvoice,
                        secondMemberInvoice
                    )
                )

                preActivationPaymentDataService.update(match {
                    it.referenceDate == referenceDate &&
                            it.dueDate == dueDate &&
                            it.memberInvoiceIds == memberInvoiceIds &&
                            it.companyId == companyId &&
                            it.companySubContractId == subContractId &&
                            it.status == PreActivationPaymentStatus.PROCESSING
                })

                invoicePaymentService.createFromPreActivationPayment(
                    match {
                        it.referenceDate == referenceDate &&
                                it.dueDate == dueDate &&
                                it.memberInvoiceIds == memberInvoiceIds
                    },
                    PaymentMethod.BOLEPIX,
                    dueDate = dueDate,
                    origin = InvoicePaymentOrigin.UNDEFINED,
                    syncProcess = true
                )

                preActivationPaymentDataService.update(match {
                    it.referenceDate == referenceDate &&
                            it.dueDate == dueDate &&
                            it.billingAccountablePartyId == billingAccountableParty.id &&
                            it.status == PreActivationPaymentStatus.PROCESSED &&
                            it.memberInvoiceIds == memberInvoiceIds &&
                            it.companyId == companyId &&
                            it.companySubContractId == subContractId
                })
            }
        }

        @Test
        fun `#should return some already created member invoice group`() = runBlocking<Unit> {
            coEvery { companyService.get(companyId) } returns company
            coEvery { companySubcontractService.get(subContractId) } returns subContract

            coEvery {
                preActivationPaymentDataService.findOne(
                    queryEq {
                        where {
                            this.companySubContractId.eq(subContractId) and this.referenceDate.eq(<EMAIL>) and this.status.inList(
                                <EMAIL>
                            )
                        }
                    })
            } returns preActivationPaymentModel


            val result = service.generateForB2B(
                companyId,
                subContractId,
                members,
                billingAccountableParty.id,
                referenceDate,
                dueDate
            )

            ResultAssert.assertThat(result).isSuccessWithData(preActivationPayment)

            coVerifyOnce {
                companyService.get(companyId)
                companySubcontractService.get(subContractId)
                preActivationPaymentDataService.findOne(
                    queryEq {
                        where {
                            this.companySubContractId.eq(subContractId) and this.referenceDate.eq(<EMAIL>) and this.status.inList(
                                <EMAIL>
                            )
                        }
                    })
            }

            coVerifyNone {
                invoiceItemService.listActiveInvoiceItemsBySubcontractId(any(), any())
                preActivationPaymentDataService.add(any())
                invoicesService.createInvoices(any())
                preActivationPaymentDataService.update(any())
                invoicePaymentService.createInvoicePaymentForInvoiceGroup(
                    any(),
                    PaymentMethod.BOLETO,
                    PaymentReason.B2B_FIRST_PAYMENT,
                )
            }
        }

        @Test
        fun `#shouldn't create a pre activation payment when the data service add method call is failed`() =
            runBlocking {
                val invoiceItem = TestModelFactory.buildInvoiceItem(
                    companyId = companyId,
                    subcontractId = subContractId,
                )

                coEvery { companyService.get(companyId) } returns company
                coEvery { companySubcontractService.get(subContractId) } returns subContract

                coEvery {
                    invoiceItemService.listActiveInvoiceItemsBySubcontractId(any(), any())
                } returns listOf(invoiceItem)

                coEvery {
                    preActivationPaymentDataService.findOne(
                        queryEq {
                            where {
                                this.companySubContractId.eq(subContractId) and this.referenceDate.eq(<EMAIL>) and this.status.inList(
                                    <EMAIL>
                                )
                            }
                        })
                } returns NotFoundException()

                coEvery {
                    invoicesService.generateForB2B(
                        subContract,
                        members,
                        referenceDate.atBeginningOfTheMonth(),
                        dueDate.atStartOfDay(),
                        PaymentMethod.BOLEPIX,
                        MemberInvoiceType.B2B_PRE_ACTIVATION_PAYMENT,
                        MemberInvoicePriceType.FULL,
                    )
                } answers {
                    listOf(firstMemberInvoice, secondMemberInvoice).success()
                }

                coEvery {
                    preActivationPaymentDataService.add(
                        match {
                            it.referenceDate == referenceDate &&
                                    it.dueDate == dueDate &&
                                    it.billingAccountablePartyId == billingAccountableParty.id
                        }
                    )
                } returns Exception("")

                val result = service.generateForB2B(
                    companyId,
                    subContractId,
                    members,
                    billingAccountableParty.id,
                    referenceDate,
                    dueDate
                )

                ResultAssert.assertThat(result).isFailure()

                coVerifyOnce {
                    companyService.get(companyId)
                    companySubcontractService.get(subContractId)
                    invoiceItemService.listActiveInvoiceItemsBySubcontractId(
                        subContractId,
                        referenceDate
                    )

                    preActivationPaymentDataService.findOne(
                        queryEq {
                            where {
                                this.companySubContractId.eq(subContractId) and this.referenceDate.eq(<EMAIL>) and this.status.inList(
                                    <EMAIL>
                                )
                            }
                        })

                    invoicesService.generateForB2B(
                        subContract,
                        members,
                        referenceDate.atBeginningOfTheMonth(),
                        dueDate.atStartOfDay(),
                        PaymentMethod.BOLEPIX,
                        MemberInvoiceType.B2B_PRE_ACTIVATION_PAYMENT,
                        MemberInvoicePriceType.FULL,
                    )
                    preActivationPaymentDataService.add(
                        match {
                            it.referenceDate == referenceDate &&
                                    it.dueDate == dueDate &&
                                    it.billingAccountablePartyId == billingAccountableParty.id
                        }
                    )
                }

                coVerifyNone {
                    invoicesService.createInvoices(any())
                    preActivationPaymentDataService.update(any())
                    invoicePaymentService.createFromPreActivationPayment(any(), any(), any(), any(), any())
                    kafkaProducerService.produce(any())
                }
            }

        @Test
        fun `#shouldn't create a pre activation payment when the subcontract does not belong to the company`() =
            runBlocking {
                coEvery { companyService.get(companyId) } returns company
                coEvery { companySubcontractService.get(subContractId) } returns subContract.copy(companyId = RangeUUID.generate())

                val result = service.generateForB2B(
                    companyId,
                    subContractId,
                    members,
                    billingAccountableParty.id,
                    referenceDate,
                    dueDate
                )

                ResultAssert.assertThat(result).isFailureOfType(SubContractDoesNotBelongToCompanyException::class)

                coVerifyOnce {
                    companyService.get(companyId)
                    companySubcontractService.get(subContractId)
                }

                coVerifyNone {
                    invoiceItemService.listActiveInvoiceItemsBySubcontractId(any(), any())
                    preActivationPaymentDataService.findOne(any())
                    invoicesService.generateForB2B(any(), any(), any(), any(), any(), any(), any())
                    preActivationPaymentDataService.add(any())
                    invoicesService.createInvoice(any(), skipValidation = true)
                    preActivationPaymentDataService.update(any())
                    invoicePaymentService.createFromPreActivationPayment(any(), any(), any(), any(), any())
                    kafkaProducerService.produce(any())
                }
            }

        @Test
        fun `#shouldn't create a member invoice group when company is not found`() =
            runBlocking {
                coEvery { companyService.get(companyId) } returns NotFoundException()
                coEvery { companySubcontractService.get(subContractId) } returns subContract

                val result = service.generateForB2B(
                    companyId,
                    subContractId,
                    members,
                    billingAccountableParty.id,
                    referenceDate,
                    dueDate
                )

                ResultAssert.assertThat(result).isFailureOfType(CompanyNotFoundException::class)

                coVerifyOnce {
                    companyService.get(companyId)
                    companySubcontractService.get(subContractId)
                }

                coVerifyNone {
                    invoiceItemService.listActiveInvoiceItemsBySubcontractId(any(), any())
                    preActivationPaymentDataService.findOne(any())
                    invoicesService.generateForB2B(any(), any(), any(), any(), any(), any(), any())
                    preActivationPaymentDataService.add(any())
                    invoicesService.createInvoices(any())
                    preActivationPaymentDataService.update(any())
                    invoicePaymentService.createFromPreActivationPayment(any(), any(), any(), any(), any())
                    kafkaProducerService.produce(any())
                }
            }

        @Test
        fun `#shouldn't create a member invoice group when subcontract is not found`() =
            runBlocking {
                coEvery { companyService.get(companyId) } returns company
                coEvery { companySubcontractService.get(subContractId) } returns NotFoundException()

                val result = service.generateForB2B(
                    companyId,
                    subContractId,
                    members,
                    billingAccountableParty.id,
                    referenceDate,
                    dueDate
                )

                ResultAssert.assertThat(result).isFailureOfType(SubContractNotFoundException::class)

                coVerifyOnce {
                    companyService.get(companyId)
                    companySubcontractService.get(subContractId)
                }

                coVerifyNone {
                    invoiceItemService.listActiveInvoiceItemsBySubcontractId(any(), any())
                    preActivationPaymentDataService.findOne(any())
                    invoicesService.generateForB2B(any(), any(), any(), any(), any(), any(), any())
                    preActivationPaymentDataService.add(any())
                    invoicesService.createInvoices(any())
                    preActivationPaymentDataService.update(any())
                    invoicePaymentService.createFromPreActivationPayment(any(), any(), any(), any(), any())
                    kafkaProducerService.produce(any())
                }
            }

        @Test
        fun `#should cancel the pre activation payment when something is wrong when validate the member invoices`() =
            runBlocking<Unit> {
                val invoiceItem = TestModelFactory.buildInvoiceItem(
                    companyId = companyId,
                    subcontractId = subContractId,
                    resolvedValue = BigDecimal("120.00")
                )

                coEvery { companyService.get(companyId) } returns company
                coEvery { companySubcontractService.get(subContractId) } returns subContract

                coEvery {
                    invoiceItemService.listActiveInvoiceItemsBySubcontractId(any(), any())
                } returns listOf(invoiceItem)

                coEvery {
                    invoicesService.generateForB2B(
                        subContract,
                        any(),
                        referenceDate.atBeginningOfTheMonth(),
                        dueDate.atStartOfDay(),
                        PaymentMethod.BOLEPIX,
                        MemberInvoiceType.B2B_PRE_ACTIVATION_PAYMENT,
                        MemberInvoicePriceType.FULL,
                    )
                } answers {
                    listOf(
                        firstMemberInvoice,
                        secondMemberInvoice
                    ).success()
                }

                coEvery {
                    preActivationPaymentDataService.findOne(
                        queryEq {
                            where {
                                this.companySubContractId.eq(subContractId) and this.referenceDate.eq(<EMAIL>) and this.status.inList(
                                    <EMAIL>
                                )
                            }
                        })
                } returns NotFoundException()

                coEvery {
                    preActivationPaymentDataService.add(
                        match {
                            it.referenceDate == referenceDate &&
                                    it.dueDate == dueDate &&
                                    it.billingAccountablePartyId == billingAccountableParty.id
                        }
                    )
                } returns preActivationPaymentModel

                coEvery { kafkaProducerService.produce(match<PreActivationPaymentCreatedEvent> { it.payload.preActivationPayment == preActivationPayment }) } returns mockk()

                coEvery {
                    invoicesService.createInvoices(
                        listOf(
                            firstMemberInvoice,
                            secondMemberInvoice
                        )
                    )
                } returns Exception("")

                coEvery {
                    preActivationPaymentDataService.update(match {
                        it.referenceDate == referenceDate &&
                                it.dueDate == dueDate &&
                                it.billingAccountablePartyId == billingAccountableParty.id &&
                                it.status == PreActivationPaymentStatus.CANCELED &&
                                it.memberInvoiceIds.isEmpty() &&
                                it.companyId == companyId &&
                                it.companySubContractId == subContractId
                    })
                } returns preActivationPaymentModel

                coEvery { kafkaProducerService.produce(match<PreActivationPaymentCanceledEvent> { it.payload.preActivationPayment == preActivationPayment }) } returns mockk()

                val result =
                    service.generateForB2B(
                        companyId,
                        subContractId,
                        members,
                        billingAccountableParty.id,
                        referenceDate,
                        dueDate
                    )

                ResultAssert.assertThat(result).isFailure()

                coVerifyOnce {
                    companyService.get(companyId)
                    companySubcontractService.get(subContractId)

                    invoiceItemService.listActiveInvoiceItemsBySubcontractId(subContractId, referenceDate)

                    preActivationPaymentDataService.findOne(
                        queryEq {
                            where {
                                this.companySubContractId.eq(subContractId) and this.referenceDate.eq(<EMAIL>) and this.status.inList(
                                    <EMAIL>
                                )
                            }
                        })

                    invoicesService.generateForB2B(
                        subContract,
                        any(),
                        referenceDate.atBeginningOfTheMonth(),
                        dueDate.atStartOfDay(),
                        PaymentMethod.BOLEPIX,
                        MemberInvoiceType.B2B_PRE_ACTIVATION_PAYMENT,
                        MemberInvoicePriceType.FULL,
                    )

                    preActivationPaymentDataService.add(
                        match {
                            it.referenceDate == referenceDate &&
                                    it.dueDate == dueDate &&
                                    it.billingAccountablePartyId == billingAccountableParty.id
                        }
                    )

                    kafkaProducerService.produce(match<PreActivationPaymentCreatedEvent> { it.payload.preActivationPayment == preActivationPayment })

                    invoicesService.createInvoices(
                        listOf(
                            firstMemberInvoice,
                            secondMemberInvoice
                        )
                    )

                    preActivationPaymentDataService.update(match {
                        it.referenceDate == referenceDate &&
                                it.dueDate == dueDate &&
                                it.billingAccountablePartyId == billingAccountableParty.id &&
                                it.status == PreActivationPaymentStatus.CANCELED &&
                                it.memberInvoiceIds.isEmpty() &&
                                it.companyId == companyId &&
                                it.companySubContractId == subContractId
                    })

                    kafkaProducerService.produce(match<PreActivationPaymentCanceledEvent> { it.payload.preActivationPayment == preActivationPayment })
                }

                coVerifyNone {
                    preActivationPaymentDataService.update(match {
                        it.referenceDate == referenceDate &&
                                it.dueDate == dueDate &&
                                it.memberInvoiceIds == memberInvoiceIds &&
                                it.companyId == companyId &&
                                it.companySubContractId == subContractId &&
                                it.status == PreActivationPaymentStatus.PROCESSING
                    })

                    preActivationPaymentDataService.update(match {
                        it.referenceDate == referenceDate &&
                                it.dueDate == dueDate &&
                                it.billingAccountablePartyId == billingAccountableParty.id &&
                                it.status == PreActivationPaymentStatus.PROCESSED &&
                                it.memberInvoiceIds == memberInvoiceIds &&
                                it.companyId == companyId &&
                                it.companySubContractId == subContractId
                    })

                    invoicePaymentService.createFromPreActivationPayment(any(), any(), any(), any(), any())
                }
            }

        @Test
        fun `#should cancel the pre activation payment when something is wrong when try to create some invoice payment`() =
            runBlocking<Unit> {
                val invoiceItem = TestModelFactory.buildInvoiceItem(
                    companyId = companyId,
                    subcontractId = subContractId,
                    resolvedValue = BigDecimal("120.00")
                )

                val preActivationPaymentWithMemberInvoiceIds = preActivationPayment.copy(
                    memberInvoiceIds = memberInvoices.map { it.id }
                )

                coEvery { companyService.get(companyId) } returns company
                coEvery { companySubcontractService.get(subContractId) } returns subContract

                coEvery {
                    invoiceItemService.listActiveInvoiceItemsBySubcontractId(any(), any())
                } returns listOf(invoiceItem)

                coEvery {
                    invoicesService.generateForB2B(
                        subContract,
                        any(),
                        referenceDate.atBeginningOfTheMonth(),
                        dueDate.atStartOfDay(),
                        PaymentMethod.BOLEPIX,
                        MemberInvoiceType.B2B_PRE_ACTIVATION_PAYMENT,
                        MemberInvoicePriceType.FULL,
                    )
                } answers {
                    listOf(
                        firstMemberInvoice,
                        secondMemberInvoice
                    ).success()
                }

                coEvery {
                    preActivationPaymentDataService.findOne(
                        queryEq {
                            where {
                                this.companySubContractId.eq(subContractId) and this.referenceDate.eq(<EMAIL>) and this.status.inList(
                                    <EMAIL>
                                )
                            }
                        })
                } returns NotFoundException()

                coEvery {
                    preActivationPaymentDataService.add(
                        match {
                            it.referenceDate == referenceDate &&
                                    it.dueDate == dueDate &&
                                    it.billingAccountablePartyId == billingAccountableParty.id
                        }
                    )
                } returns preActivationPaymentModel

                coEvery { kafkaProducerService.produce(match<PreActivationPaymentCreatedEvent> { it.payload.preActivationPayment == preActivationPayment }) } returns mockk()

                coEvery {
                    invoicesService.createInvoices(
                        listOf(
                            firstMemberInvoice,
                            secondMemberInvoice
                        )
                    )
                } returns listOf(firstMemberInvoice, secondMemberInvoice)

                coEvery {
                    preActivationPaymentDataService.update(match {
                        it.referenceDate == referenceDate &&
                                it.dueDate == dueDate &&
                                it.memberInvoiceIds == memberInvoiceIds &&
                                it.companyId == companyId &&
                                it.companySubContractId == subContractId &&
                                it.status == PreActivationPaymentStatus.PROCESSING
                    })
                } returns preActivationPaymentWithMemberInvoiceIds.toModel()

                coEvery {
                    invoicePaymentService.createFromPreActivationPayment(
                        match {
                            it.referenceDate == referenceDate &&
                                    it.dueDate == dueDate &&
                                    it.memberInvoiceIds == memberInvoiceIds
                        },
                        PaymentMethod.BOLEPIX,
                        dueDate = dueDate,
                        origin = InvoicePaymentOrigin.UNDEFINED,
                        syncProcess = true
                    )
                } returns Exception()

                coEvery {
                    invoicePaymentService.cancelByPreActivationPaymentId(
                        preActivationPayment.id,
                        CancellationReason.PAYMENT_PROCESSOR_CANCELED
                    )
                } returns listOf(invoicePayment.copy(status = InvoicePaymentStatus.CANCELED))

                coEvery {
                    invoicesService.cancel(
                        firstMemberInvoice.id,
                        CancellationReason.PAYMENT_PROCESSOR_CANCELED
                    )
                } returns firstMemberInvoice.cancel(CancellationReason.PAYMENT_PROCESSOR_CANCELED)

                coEvery { invoicesService.listByPreActivationPaymentId(preActivationPayment.id) } returns listOf(
                    firstMemberInvoice
                )

                coEvery {
                    preActivationPaymentDataService.update(match {
                        it.referenceDate == referenceDate &&
                                it.dueDate == dueDate &&
                                it.billingAccountablePartyId == billingAccountableParty.id &&
                                it.status == PreActivationPaymentStatus.CANCELED &&
                                it.memberInvoiceIds == memberInvoiceIds &&
                                it.companyId == companyId &&
                                it.companySubContractId == subContractId
                    })
                } returns preActivationPaymentModel

                coEvery { kafkaProducerService.produce(match<PreActivationPaymentCanceledEvent> { it.payload.preActivationPayment == preActivationPayment }) } returns mockk()

                val result =
                    service.generateForB2B(
                        companyId,
                        subContractId,
                        members,
                        billingAccountableParty.id,
                        referenceDate,
                        dueDate
                    )

                ResultAssert.assertThat(result).isFailure()

                coVerifyOnce {
                    companyService.get(companyId)
                    companySubcontractService.get(subContractId)

                    invoiceItemService.listActiveInvoiceItemsBySubcontractId(subContractId, referenceDate)

                    preActivationPaymentDataService.findOne(
                        queryEq {
                            where {
                                this.companySubContractId.eq(subContractId) and this.referenceDate.eq(<EMAIL>) and this.status.inList(
                                    <EMAIL>
                                )
                            }
                        })

                    invoicesService.generateForB2B(
                        subContract,
                        any(),
                        referenceDate.atBeginningOfTheMonth(),
                        dueDate.atStartOfDay(),
                        PaymentMethod.BOLEPIX,
                        MemberInvoiceType.B2B_PRE_ACTIVATION_PAYMENT,
                        MemberInvoicePriceType.FULL,
                    )

                    preActivationPaymentDataService.add(
                        match {
                            it.referenceDate == referenceDate &&
                                    it.dueDate == dueDate &&
                                    it.billingAccountablePartyId == billingAccountableParty.id
                        }
                    )

                    kafkaProducerService.produce(match<PreActivationPaymentCreatedEvent> { it.payload.preActivationPayment == preActivationPayment })

                    invoicesService.createInvoices(
                        listOf(
                            firstMemberInvoice,
                            secondMemberInvoice
                        )
                    )

                    preActivationPaymentDataService.update(match {
                        it.referenceDate == referenceDate &&
                                it.dueDate == dueDate &&
                                it.memberInvoiceIds == memberInvoiceIds &&
                                it.companyId == companyId &&
                                it.companySubContractId == subContractId &&
                                it.status == PreActivationPaymentStatus.PROCESSING
                    })

                    preActivationPaymentDataService.update(match {
                        it.referenceDate == referenceDate &&
                                it.dueDate == dueDate &&
                                it.billingAccountablePartyId == billingAccountableParty.id &&
                                it.status == PreActivationPaymentStatus.CANCELED &&
                                it.memberInvoiceIds == memberInvoiceIds &&
                                it.companyId == companyId &&
                                it.companySubContractId == subContractId
                    })

                    invoicePaymentService.createFromPreActivationPayment(
                        match {
                            it.referenceDate == referenceDate &&
                                    it.dueDate == dueDate &&
                                    it.memberInvoiceIds == memberInvoiceIds
                        },
                        PaymentMethod.BOLEPIX,
                        dueDate = dueDate,
                        origin = InvoicePaymentOrigin.UNDEFINED,
                        syncProcess = true
                    )

                    invoicePaymentService.cancelByPreActivationPaymentId(
                        preActivationPayment.id,
                        CancellationReason.PAYMENT_PROCESSOR_CANCELED
                    )

                    invoicesService.listByPreActivationPaymentId(preActivationPayment.id)

                    invoicesService.cancel(firstMemberInvoice.id, CancellationReason.PAYMENT_PROCESSOR_CANCELED)

                    kafkaProducerService.produce(match<PreActivationPaymentCanceledEvent> { it.payload.preActivationPayment == preActivationPayment })
                }

                coVerifyNone {

                    preActivationPaymentDataService.update(match {
                        it.referenceDate == referenceDate &&
                                it.dueDate == dueDate &&
                                it.memberInvoiceIds == memberInvoiceIds &&
                                it.companyId == companyId &&
                                it.companySubContractId == subContractId &&
                                it.status == PreActivationPaymentStatus.PROCESSED
                    })
                }
            }
    }

    @Test
    fun `should get a pre-activation payment by id`() = runBlocking {
        val preActivationPayment =
            TestModelFactory.buildPreActivationPayment()
        val preActivationPaymentModel = preActivationPayment.toModel()

        coEvery { preActivationPaymentDataService.get(preActivationPayment.id) } returns preActivationPaymentModel

        val result = service.get(preActivationPayment.id)

        ResultAssert.assertThat(result).isSuccessWithData(preActivationPayment)

        coVerifyOnce { preActivationPaymentDataService.get(preActivationPayment.id) }
    }

    @Nested
    inner class MarkAsPaid {

        private val preActivationPayment =
            TestModelFactory.buildPreActivationPayment(
                type = PreActivationPaymentType.B2B,
            ).markAsProcessed()

        private val invoicePayment = TestModelFactory.buildInvoicePayment(
            preActivationPaymentId = preActivationPayment.id,
            invoiceGroupId = null,
            invoiceLiquidationId = null,
        )

        @Test
        fun `should mark a pre-activation payment as paid when it is not paid yet`() = runBlocking {
            val preActivationPaymentUpdated = preActivationPayment.markAsPaid()
            val preActivationPaymentUpdatedModel = preActivationPaymentUpdated.toModel()

            coEvery {
                preActivationPaymentDataService.update(match {
                    it.id == preActivationPaymentUpdated.id &&
                            it.status == PreActivationPaymentStatus.PAID
                })
            } returns preActivationPaymentUpdatedModel

            coEvery { kafkaProducerService.produce(match<PreActivationPaymentPaidEvent> { it.payload.preActivationPayment == preActivationPaymentUpdated && it.payload.invoicePayment == invoicePayment }) } returns mockk()

            val result = service.markAsPaid(preActivationPayment, invoicePayment)

            ResultAssert.assertThat(result).isSuccessWithData(preActivationPaymentUpdated)

            coVerifyOnce {
                preActivationPaymentDataService.update(match {
                    it.id == preActivationPaymentUpdated.id &&
                            it.status == PreActivationPaymentStatus.PAID
                })
                kafkaProducerService.produce(match<PreActivationPaymentPaidEvent> { it.payload.preActivationPayment == preActivationPaymentUpdated })
            }
        }

        @Test
        fun `should not mark a pre-activation payment as paid when it is already paid`() = runBlocking {
            val preActivationPaymentPaid = preActivationPayment.markAsPaid()

            val result = service.markAsPaid(preActivationPaymentPaid, invoicePayment)

            ResultAssert.assertThat(result).isSuccessWithData(preActivationPaymentPaid)

            coVerifyNone {
                preActivationPaymentDataService.update(any())
                kafkaProducerService.produce(any())
            }
        }
    }

    @Nested
    inner class GetBySubcontractId {
        private val subContractId = RangeUUID.generate()

        private val preActivationPayment =
            TestModelFactory.buildPreActivationPayment(
                type = PreActivationPaymentType.B2B,
                companySubContractId = subContractId,
            )

        private val preActivationPaymentModel =
            preActivationPayment.toModel()

        @Test
        fun `should return the pre-activation payments that belongs to the subcontract id`() = runBlocking {
            coEvery {
                preActivationPaymentDataService.find(queryEq {
                    where {
                        this.companySubContractId.eq(subContractId)
                    }
                })
            } returns listOf(preActivationPaymentModel)

            val result = service.getBySubcontractId(subContractId)

            ResultAssert.assertThat(result).isSuccessWithData(listOf(preActivationPayment))

            coVerifyOnce {
                preActivationPaymentDataService.find(queryEq {
                    where {
                        this.companySubContractId.eq(subContractId)
                    }
                })
            }
        }

        @Test
        fun `should return the pre-activation payments that belongs to the subcontract id and for a specific status`() =
            runBlocking {
                coEvery {
                    preActivationPaymentDataService.find(queryEq {
                        where {
                            this.companySubContractId.eq(subContractId)
                                .and(this.status.inList(listOf(PreActivationPaymentStatus.PROCESSED)))
                        }
                    })
                } returns listOf(preActivationPaymentModel)

                val result = service.getBySubcontractId(
                    subContractId,
                    options = PreActivationPaymentService.FindOptions(status = listOf(PreActivationPaymentStatus.PROCESSED)),
                )

                ResultAssert.assertThat(result).isSuccessWithData(listOf(preActivationPayment))

                coVerifyOnce {
                    preActivationPaymentDataService.find(queryEq {
                        where {
                            this.companySubContractId.eq(subContractId)
                                .and(this.status.inList(listOf(PreActivationPaymentStatus.PROCESSED)))
                        }
                    })
                }
            }

        @Test
        fun `should return the pre-activation payments that belongs to the subcontract id and for a specific type`() =
            runBlocking {
                coEvery {
                    preActivationPaymentDataService.find(queryEq {
                        where {
                            this.companySubContractId.eq(subContractId)
                                .and(this.type.inList(listOf(PreActivationPaymentType.B2B)))
                        }
                    })
                } returns listOf(preActivationPaymentModel)

                val result = service.getBySubcontractId(
                    subContractId,
                    options = PreActivationPaymentService.FindOptions(
                        type = listOf(PreActivationPaymentType.B2B),
                    ),
                )

                ResultAssert.assertThat(result).isSuccessWithData(listOf(preActivationPayment))

                coVerifyOnce {
                    preActivationPaymentDataService.find(queryEq {
                        where {
                            this.companySubContractId.eq(subContractId)
                                .and(this.type.inList(listOf(PreActivationPaymentType.B2B)))
                        }
                    })
                }
            }
    }

    @Nested
    inner class ListByCompanyId {
        private val companyId = RangeUUID.generate()

        private val preActivationPayment =
            TestModelFactory.buildPreActivationPayment(
                type = PreActivationPaymentType.B2B,
                companySubContractId = companyId,
            )

        private val preActivationPaymentModel =
            preActivationPayment.toModel()

        @Test
        fun `should return the pre-activation payments that belongs to the company id`() = runBlocking {
            coEvery {
                preActivationPaymentDataService.find(queryEq {
                    where {
                        this.companyId.eq(<EMAIL>)
                    }
                })
            } returns listOf(preActivationPaymentModel)

            val result = service.listByCompanyId(companyId)

            ResultAssert.assertThat(result).isSuccessWithData(listOf(preActivationPayment))

            coVerifyOnce {
                preActivationPaymentDataService.find(queryEq {
                    where {
                        this.companyId.eq(<EMAIL>)
                    }
                })
            }
        }

        @Test
        fun `should return the pre-activation payments that belongs to the company id and for a specific status`() =
            runBlocking {
                coEvery {
                    preActivationPaymentDataService.find(queryEq {
                        where {
                            this.companyId.eq(<EMAIL>)
                                .and(this.status.inList(listOf(PreActivationPaymentStatus.PROCESSED)))
                        }
                    })
                } returns listOf(preActivationPaymentModel)

                val result = service.listByCompanyId(
                    companyId,
                    options = PreActivationPaymentService.FindOptions(status = listOf(PreActivationPaymentStatus.PROCESSED)),
                )

                ResultAssert.assertThat(result).isSuccessWithData(listOf(preActivationPayment))

                coVerifyOnce {
                    preActivationPaymentDataService.find(queryEq {
                        where {
                            this.companyId.eq(<EMAIL>)
                                .and(this.status.inList(listOf(PreActivationPaymentStatus.PROCESSED)))
                        }
                    })
                }
            }
    }

    @Nested
    inner class ListByIds {
        private val companyId = RangeUUID.generate()

        private val preActivationPayment =
            TestModelFactory.buildPreActivationPayment(
                type = PreActivationPaymentType.B2B,
                companySubContractId = companyId,
            )

        private val preActivationPaymentModel =
            preActivationPayment.toModel()

        @Test
        fun `should return the pre-activation payments by ids`() = runBlocking {
            coEvery {
                preActivationPaymentDataService.find(queryEq {
                    where {
                        this.id.inList(listOf(preActivationPayment.id))
                    }
                })
            } returns listOf(preActivationPaymentModel)

            val result = service.listByIds(listOf(preActivationPayment.id))

            ResultAssert.assertThat(result).isSuccessWithData(listOf(preActivationPayment))

            coVerifyOnce {
                preActivationPaymentDataService.find(queryEq {
                    where {
                        this.id.inList(listOf(preActivationPayment.id))
                    }
                })
            }
        }
    }

    @Nested
    inner class GetByBillingAccountablePartyId {
        private val preActivationPayment =
            TestModelFactory.buildPreActivationPayment(
                type = PreActivationPaymentType.B2B,
                billingAccountablePartyId = RangeUUID.generate(),
            )

        private val preActivationPaymentModel =
            preActivationPayment.toModel()

        @Test
        fun `should return the pre-activation payments by ids`() = runBlocking {

            coEvery {
                preActivationPaymentDataService.findByBillingAccountablePartyId(preActivationPayment.billingAccountablePartyId)
            } returns listOf(preActivationPaymentModel)

            val result = service.getByBillingAccountablePartyId(preActivationPayment.billingAccountablePartyId)

            ResultAssert.assertThat(result).isSuccessWithData(listOf(preActivationPayment))

            coVerifyOnce {
                preActivationPaymentDataService.findByBillingAccountablePartyId(preActivationPayment.billingAccountablePartyId)
            }
        }
    }
}
