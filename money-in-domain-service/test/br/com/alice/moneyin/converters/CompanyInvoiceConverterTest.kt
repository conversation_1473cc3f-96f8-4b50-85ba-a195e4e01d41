package br.com.alice.moneyin.converters

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.atBeginningOfTheMonth
import br.com.alice.common.core.extensions.atEndOfTheMonth
import br.com.alice.common.core.extensions.money
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MemberInvoiceType
import br.com.alice.data.layer.models.PreActivationPaymentType
import br.com.alice.moneyin.converters.CompanyInvoiceConverter.toCompanyInvoiceResponse
import br.com.alice.moneyin.models.CompanyInvoiceModel
import br.com.alice.moneyin.models.CompanyInvoiceRelationship
import br.com.alice.moneyin.models.CompanyInvoiceResponse
import br.com.alice.moneyin.models.CompanyInvoiceStatus
import br.com.alice.moneyin.models.CompanyInvoiceType
import br.com.alice.moneyin.models.CompanyInvoiceValidityPeriod
import org.junit.jupiter.api.Nested
import java.time.LocalDate
import kotlin.test.Test
import kotlin.test.assertEquals

class CompanyInvoiceConverterTest {

    @Nested
    inner class MemberInvoiceGroup {

        @Test
        fun `should convert a MemberInvoiceGroup to CompanyInvoiceResponse`() {
            val companyId = RangeUUID.generate()
            val companySubContractId = RangeUUID.generate()

            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
                companyId = companyId,
                companySubcontractId = companySubContractId,
                type = MemberInvoiceType.B2B_REGULAR_PAYMENT,
            )
            val expectedResult = CompanyInvoiceResponse(
                id = memberInvoiceGroup.id,
                externalId = memberInvoiceGroup.externalId,
                status = CompanyInvoiceStatus.PROCESSING,
                referenceDate = memberInvoiceGroup.referenceDate,
                billingAccountablePartyId = memberInvoiceGroup.billingAccountablePartyId,
                validityPeriod = CompanyInvoiceValidityPeriod(
                    start = memberInvoiceGroup.referenceDate.atBeginningOfTheMonth(),
                    end = memberInvoiceGroup.referenceDate.atEndOfTheMonth()
                ),
                multipleValidityPeriods = emptyList(),
                companySubContractIds = listOf(companySubContractId),
                companyIds = listOf(companyId),
                type = CompanyInvoiceType.RECURRENT, model = CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
                totalAmount = memberInvoiceGroup.totalAmount!!,
                discount = 0.money,
                addition = 0.money,
                dueDate = memberInvoiceGroup.dueDate,
                quantityMemberInvoices = memberInvoiceGroup.memberInvoiceIds.size,
                memberInvoiceIds = memberInvoiceGroup.memberInvoiceIds,
                relationships = emptyList(),
                globalItems = emptyList(),
                installments = 0,
                totalInstallments = 0,
                version = memberInvoiceGroup.version,
                createdAt = memberInvoiceGroup.createdAt,
                updatedAt = memberInvoiceGroup.updatedAt,
                payments = emptyList(),
            )

            val result = memberInvoiceGroup.toCompanyInvoiceResponse()

            assertEquals(expectedResult, result)
        }

        @Test
        fun `should convert a MemberInvoiceGroup to CompanyInvoiceResponse when it is related to pre-activation payment`() {
            val companyId = RangeUUID.generate()
            val companySubContractId = RangeUUID.generate()
            val preActivationPaymentId = RangeUUID.generate()

            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
                companyId = companyId,
                companySubcontractId = companySubContractId,
                type = MemberInvoiceType.B2B_REGULAR_PAYMENT,
                preActivationPaymentId = preActivationPaymentId,
            )
            val expectedResult = CompanyInvoiceResponse(
                id = memberInvoiceGroup.id,
                externalId = memberInvoiceGroup.externalId,
                billingAccountablePartyId = memberInvoiceGroup.billingAccountablePartyId,
                status = CompanyInvoiceStatus.PROCESSING,
                referenceDate = memberInvoiceGroup.referenceDate,
                validityPeriod = CompanyInvoiceValidityPeriod(
                    start = memberInvoiceGroup.referenceDate.atBeginningOfTheMonth(),
                    end = memberInvoiceGroup.referenceDate.atEndOfTheMonth()
                ),
                multipleValidityPeriods = emptyList(),
                companySubContractIds = listOf(companySubContractId),
                companyIds = listOf(companyId),
                type = CompanyInvoiceType.RECURRENT,
                model = CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
                totalAmount = memberInvoiceGroup.totalAmount!!,
                discount = 0.money,
                addition = 0.money,
                dueDate = memberInvoiceGroup.dueDate,
                quantityMemberInvoices = memberInvoiceGroup.memberInvoiceIds.size,
                memberInvoiceIds = memberInvoiceGroup.memberInvoiceIds,
                relationships = listOf(
                    CompanyInvoiceRelationship(
                        preActivationPaymentId,
                        CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT,
                    )
                ),
                globalItems = emptyList(),
                installments = 0,
                totalInstallments = 0,
                version = memberInvoiceGroup.version,
                createdAt = memberInvoiceGroup.createdAt,
                updatedAt = memberInvoiceGroup.updatedAt,
                payments = emptyList(),
            )

            val result = memberInvoiceGroup.toCompanyInvoiceResponse()

            assertEquals(expectedResult, result)
        }

        @Test
        fun `should convert a MemberInvoiceGroup to CompanyInvoiceResponse when it is related to some invoice liquidations`() {
            val companyId = RangeUUID.generate()
            val companySubContractId = RangeUUID.generate()
            val invoiceLiquidationId1 = RangeUUID.generate()
            val invoiceLiquidationId2 = RangeUUID.generate()

            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
                companyId = companyId,
                companySubcontractId = companySubContractId,
                type = MemberInvoiceType.B2B_REGULAR_PAYMENT,
                invoiceLiquidationIds = listOf(invoiceLiquidationId1, invoiceLiquidationId2),
            )
            val expectedResult = CompanyInvoiceResponse(
                id = memberInvoiceGroup.id,
                externalId = memberInvoiceGroup.externalId,
                billingAccountablePartyId = memberInvoiceGroup.billingAccountablePartyId,
                status = CompanyInvoiceStatus.PROCESSING,
                referenceDate = memberInvoiceGroup.referenceDate,
                validityPeriod = CompanyInvoiceValidityPeriod(
                    start = memberInvoiceGroup.referenceDate.atBeginningOfTheMonth(),
                    end = memberInvoiceGroup.referenceDate.atEndOfTheMonth()
                ),
                multipleValidityPeriods = emptyList(),
                companySubContractIds = listOf(companySubContractId),
                companyIds = listOf(companyId),
                type = CompanyInvoiceType.RECURRENT,
                model = CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
                totalAmount = memberInvoiceGroup.totalAmount!!,
                discount = 0.money,
                addition = 0.money,
                dueDate = memberInvoiceGroup.dueDate,
                quantityMemberInvoices = memberInvoiceGroup.memberInvoiceIds.size,
                memberInvoiceIds = memberInvoiceGroup.memberInvoiceIds,
                relationships = listOf(
                    CompanyInvoiceRelationship(
                        invoiceLiquidationId1,
                        CompanyInvoiceModel.INVOICE_LIQUIDATION,
                    ),
                    CompanyInvoiceRelationship(
                        invoiceLiquidationId2,
                        CompanyInvoiceModel.INVOICE_LIQUIDATION,
                    )
                ),
                globalItems = emptyList(),
                installments = 0,
                totalInstallments = 0,
                version = memberInvoiceGroup.version,
                createdAt = memberInvoiceGroup.createdAt,
                updatedAt = memberInvoiceGroup.updatedAt,
                payments = emptyList(),
            )

            val result = memberInvoiceGroup.toCompanyInvoiceResponse()

            assertEquals(expectedResult, result)
        }
    }

    @Nested
    inner class InvoiceLiquidation {

        @Test
        fun `should convert a InvoiceLiquidation to CompanyInvoiceResponse`() {
            val companyId = RangeUUID.generate()
            val companySubContractId = RangeUUID.generate()

            val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation(
                companyIds = listOf(companyId),
                subcontractIds = listOf(companySubContractId),
                discount = 120.money,
                addition = 0.money,
                memberInvoiceGroupIds = emptyList()
            )

            val expectedResult = CompanyInvoiceResponse(
                id = invoiceLiquidation.id,
                externalId = invoiceLiquidation.externalId,
                billingAccountablePartyId = invoiceLiquidation.billingAccountablePartyId,
                status = CompanyInvoiceStatus.PROCESSED,
                referenceDate = invoiceLiquidation.dueDate,
                validityPeriod = CompanyInvoiceValidityPeriod(
                    start = invoiceLiquidation.dueDate.atBeginningOfTheMonth(),
                    end = invoiceLiquidation.dueDate.atEndOfTheMonth()
                ),
                multipleValidityPeriods = emptyList(),
                companySubContractIds = listOf(companySubContractId),
                companyIds = listOf(companyId),
                type = CompanyInvoiceType.LIQUIDATION,
                model = CompanyInvoiceModel.INVOICE_LIQUIDATION,
                totalAmount = invoiceLiquidation.amount,
                discount = 120.money,
                addition = 0.money,
                dueDate = invoiceLiquidation.dueDate,
                quantityMemberInvoices = 0,
                memberInvoiceIds = emptyList(),
                relationships = emptyList(),
                globalItems = emptyList(),
                installments = 1,
                totalInstallments = 1,
                version = invoiceLiquidation.version,
                createdAt = invoiceLiquidation.createdAt,
                updatedAt = invoiceLiquidation.updatedAt,
                payments = emptyList(),
            )

            val result = invoiceLiquidation.toCompanyInvoiceResponse(emptyList())

            assertEquals(expectedResult, result)
        }

        @Test
        fun `should convert an InvoiceLiquiation to CompanyInvoiceResponse when it is related to member invoice group`() {
            val companyId = RangeUUID.generate()
            val companySubContractId = RangeUUID.generate()
            val memberInvoiceGroupId1 = RangeUUID.generate()
            val memberInvoiceGroupId2 = RangeUUID.generate()

            val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation(
                companyId = companyId,
                companyIds = listOf(companyId),
                subcontractIds = listOf(companySubContractId),
                memberInvoiceGroupIds = listOf(memberInvoiceGroupId1, memberInvoiceGroupId2),
                addition = 99.money,
                discount = 0.money,
            )

            val memberInvoiceGroup1 = TestModelFactory.buildMemberInvoiceGroup(
                id = memberInvoiceGroupId1,
                companyId = companyId,
                companySubcontractId = companySubContractId,
                type = MemberInvoiceType.B2B_REGULAR_PAYMENT,
                referenceDate = LocalDate.now().minusMonths(1),
            )

            val memberInvoiceGroup2 = TestModelFactory.buildMemberInvoiceGroup(
                id = memberInvoiceGroupId2,
                companyId = companyId,
                companySubcontractId = companySubContractId,
                type = MemberInvoiceType.B2B_REGULAR_PAYMENT,
                referenceDate = LocalDate.now().minusMonths(1),
            )

            val expectedResult = CompanyInvoiceResponse(
                id = invoiceLiquidation.id,
                externalId = invoiceLiquidation.externalId,
                billingAccountablePartyId = invoiceLiquidation.billingAccountablePartyId,
                status = CompanyInvoiceStatus.PROCESSED,
                referenceDate = invoiceLiquidation.dueDate,
                validityPeriod = CompanyInvoiceValidityPeriod(
                    start = invoiceLiquidation.dueDate.atBeginningOfTheMonth(),
                    end = invoiceLiquidation.dueDate.atEndOfTheMonth()
                ),
                multipleValidityPeriods = listOf(
                    CompanyInvoiceValidityPeriod(
                        memberInvoiceGroup1.referenceDate.atBeginningOfTheMonth(),
                        memberInvoiceGroup1.referenceDate.atEndOfTheMonth()
                    ),
                    CompanyInvoiceValidityPeriod(
                        memberInvoiceGroup2.referenceDate.atBeginningOfTheMonth(),
                        memberInvoiceGroup2.referenceDate.atEndOfTheMonth()
                    )
                ),
                companySubContractIds = listOf(companySubContractId),
                companyIds = listOf(companyId),
                type = CompanyInvoiceType.LIQUIDATION,
                model = CompanyInvoiceModel.INVOICE_LIQUIDATION,
                totalAmount = invoiceLiquidation.amount,
                discount = 0.money,
                addition = 99.money,
                dueDate = invoiceLiquidation.dueDate,
                quantityMemberInvoices = 0,
                memberInvoiceIds = emptyList(),
                relationships = listOf(
                    CompanyInvoiceRelationship(
                        memberInvoiceGroupId1,
                        CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
                    ),
                    CompanyInvoiceRelationship(
                        memberInvoiceGroupId2,
                        CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
                    )
                ),
                globalItems = emptyList(),
                installments = 1,
                totalInstallments = 1,
                version = invoiceLiquidation.version,
                createdAt = invoiceLiquidation.createdAt,
                updatedAt = invoiceLiquidation.updatedAt,
                payments = emptyList(),
            )

            val result = invoiceLiquidation.toCompanyInvoiceResponse(listOf(memberInvoiceGroup1, memberInvoiceGroup2))

            assertEquals(expectedResult, result)
        }
    }

    @Nested
    inner class PreActivationPayment {

        @Test
        fun `should convert a PreActivationPayment to CompanyInvoiceResponse`() {
            val companyId = RangeUUID.generate()
            val companySubContractId = RangeUUID.generate()

            val preActivationPayment = TestModelFactory.buildPreActivationPayment(
                companyId = companyId,
                companySubContractId = companySubContractId,
                type = PreActivationPaymentType.B2B,
            )

            val expectedResult = CompanyInvoiceResponse(
                id = preActivationPayment.id,
                externalId = preActivationPayment.externalId,
                billingAccountablePartyId = preActivationPayment.billingAccountablePartyId,
                status = CompanyInvoiceStatus.PROCESSING,
                referenceDate = preActivationPayment.referenceDate,
                validityPeriod = CompanyInvoiceValidityPeriod(
                    start = preActivationPayment.referenceDate.atBeginningOfTheMonth(),
                    end = preActivationPayment.referenceDate.atEndOfTheMonth()
                ),
                multipleValidityPeriods = emptyList(),
                companySubContractIds = listOf(companySubContractId),
                companyIds = listOf(companyId),
                type = CompanyInvoiceType.FIRST_PAYMENT,
                model = CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT,
                totalAmount = preActivationPayment.totalAmount,
                discount = 0.money,
                addition = 0.money,
                dueDate = preActivationPayment.dueDate,
                quantityMemberInvoices = preActivationPayment.memberInvoiceIds.size,
                memberInvoiceIds = preActivationPayment.memberInvoiceIds,
                relationships = emptyList(),
                globalItems = emptyList(),
                installments = 0,
                totalInstallments = 0,
                version = preActivationPayment.version,
                createdAt = preActivationPayment.createdAt,
                updatedAt = preActivationPayment.updatedAt,
                payments = emptyList(),
            )

            val result = preActivationPayment.toCompanyInvoiceResponse()

            assertEquals(expectedResult, result)
        }

        @Test
        fun `should convert a MemberInvoiceGroup to CompanyInvoiceResponse when it is related to member invoice group`() {
            val companyId = RangeUUID.generate()
            val companySubContractId = RangeUUID.generate()
            val memberInvoiceGroupId = RangeUUID.generate()

            val preActivationPayment = TestModelFactory.buildPreActivationPayment(
                companyId = companyId,
                companySubContractId = companySubContractId,
                type = PreActivationPaymentType.B2B,
                memberInvoiceGroupId = memberInvoiceGroupId,
            )

            val expectedResult = CompanyInvoiceResponse(
                id = preActivationPayment.id,
                externalId = preActivationPayment.externalId,
                billingAccountablePartyId = preActivationPayment.billingAccountablePartyId,
                status = CompanyInvoiceStatus.PROCESSING,
                referenceDate = preActivationPayment.referenceDate,
                validityPeriod = CompanyInvoiceValidityPeriod(
                    start = preActivationPayment.referenceDate.atBeginningOfTheMonth(),
                    end = preActivationPayment.referenceDate.atEndOfTheMonth()
                ),
                multipleValidityPeriods = emptyList(),
                companySubContractIds = listOf(companySubContractId),
                companyIds = listOf(companyId),
                type = CompanyInvoiceType.FIRST_PAYMENT,
                model = CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT,
                totalAmount = preActivationPayment.totalAmount,
                discount = 0.money,
                addition = 0.money,
                dueDate = preActivationPayment.dueDate,
                quantityMemberInvoices = preActivationPayment.memberInvoiceIds.size,
                memberInvoiceIds = preActivationPayment.memberInvoiceIds,
                relationships = listOf(
                    CompanyInvoiceRelationship(
                        memberInvoiceGroupId,
                        CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
                    )
                ),
                globalItems = emptyList(),
                installments = 0,
                totalInstallments = 0,
                version = preActivationPayment.version,
                createdAt = preActivationPayment.createdAt,
                updatedAt = preActivationPayment.updatedAt,
                payments = emptyList(),
            )

            val result = preActivationPayment.toCompanyInvoiceResponse()

            assertEquals(expectedResult, result)
        }
    }
}
