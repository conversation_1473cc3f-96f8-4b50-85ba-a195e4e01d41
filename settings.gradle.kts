/*
 * This file was generated by the Gradle 'init' task.
 *
 * The settings file is used to specify which projects to include in your build.
 *
 * Detailed information about configuring a multi-project build in <PERSON><PERSON><PERSON> can be found
 * in the user manual at https://docs.gradle.org/5.6.2/userguide/multi_project_builds.html
 */

rootProject.name = "mono"

include(
    "staff-integration-api",
    "atlas-domain-client",
    "atlas-domain-service",
    "akinator-domain-client",
    "akinator-domain-service",
    "app-content-domain-client",
    "app-content-domain-service",
    "action-plan-domain-client",
    "action-plan-domain-service",
    "amas-domain-client",
    "amas-domain-service",
    "amas-bff-api",
    "appointment-domain-client",
    "appointment-domain-service",
    "benchmarks",
    "coverage-domain-client",
    "coverage-domain-service",
    "bottini-domain-client",
    "bottini-domain-service",
    "bud-domain-client",
    "bud-domain-service",
    "business-domain-client",
    "business-domain-service",
    "business-risk-domain-client",
    "business-risk-domain-service",
    "business-platform-bff",
    "channel-bff-api",
    "channel-domain-client",
    "channel-domain-service",
    "clinical-account-domain-client",
    "clinical-account-domain-service",
    "common",
    "common-core",
    "common-clients",
    "common-document",
    "common-feature-access",
    "common-google-maps",
    "common-tests",
    "common-kafka",
    "common-logging",
    "common-redis",
    "common-service",
    "common-service-client",
    "common-sqs",
    "communication",
    "data-layer",
    "data-layer-async",
    "data-layer-common-tests",
    "data-layer-client",
    "data-layer-core",
    "data-packages:action-plan-domain-service-data-package",
    "data-packages:akinator-domain-service-data-package",
    "data-packages:amas-domain-service-data-package",
    "data-packages:amas-domain-service-model-package",
    "data-packages:app-content-domain-service-data-package",
    "data-packages:atlas-domain-service-data-package",
    "data-packages:appointment-domain-service-data-package",
    "data-packages:bottini-domain-service-data-package",
    "data-packages:business-domain-service-data-package",
    "data-packages:business-domain-service-model-package",
    "data-packages:business-domain-service-shared-data-package",
    "data-packages:business-domain-service-shared-model-package",
    "data-packages:business-risk-domain-service-data-package",
    "data-packages:business-risk-domain-service-model-package",
    "data-packages:bud-domain-service-data-package",
    "data-packages:channel-domain-service-data-package",
    "data-packages:dragon-radar-domain-service-data-package",
    "data-packages:duquesa-domain-service-data-package",
    "data-packages:clinical-account-domain-service-data-package",
    "data-packages:coverage-domain-service-data-package",
    "data-packages:ehr-domain-service-data-package",
    "data-packages:ehr-domain-service-model-package",
    "data-packages:eita-nullvs-integration-service-data-package",
    "data-packages:eita-nullvs-integration-service-model-package",
    "data-packages:eventinder-domain-service-data-package",
    "data-packages:eventinder-domain-service-model-package",
    "data-packages:exec-indicator-domain-service-data-package",
    "data-packages:exec-indicator-domain-service-model-package",
    "data-packages:haoc-integration-service-data-package",
    "data-packages:hippocrates-domain-service-data-package",
    "data-packages:hippocrates-domain-service-model-package",
    "data-packages:wanda-domain-service-data-package",
    "data-packages:health-condition-domain-service-data-package",
    "data-packages:health-logic-domain-service-data-package",
    "data-packages:health-plan-domain-service-data-package",
    "data-packages:marauders-map-domain-service-data-package",
    "data-packages:membership-domain-service-data-package",
    "data-packages:membership-domain-service-model-package",
    "data-packages:member-onboarding-domain-service-data-package",
    "data-packages:money-in-domain-service-data-package",
    "data-packages:money-in-domain-service-model-package",
    "data-packages:onboarding-domain-service-data-package",
    "data-packages:onboarding-domain-service-model-package",
    "data-packages:person-domain-service-data-package",
    "data-packages:person-domain-service-model-package",
    "data-packages:product-domain-service-data-package",
    "data-packages:product-domain-service-model-package",
    "data-packages:provider-domain-service-data-package",
    "data-packages:provider-domain-service-model-package",
    "data-packages:questionnaire-domain-service-data-package",
    "data-packages:refund-domain-service-data-package",
    "data-packages:refund-domain-service-model-package",
    "data-packages:schedule-domain-service-data-package",
    "data-packages:schedule-domain-service-model-package",
    "data-packages:screening-domain-service-data-package",
    "data-packages:secondary-attention-domain-service-data-package",
    "data-packages:sherlock-data-package",
    "data-packages:sorting-hat-domain-service-data-package",
    "data-packages:staff-domain-service-data-package",
    "data-packages:staff-domain-service-model-package",
    "data-packages:test-result-domain-service-data-package",
    "data-packages:file-vault-service-data-package",
    "data-packages:nullvs-integration-service-data-package",
    "data-packages:nullvs-integration-service-model-package",
    "data-packages:db-integration-service-data-package",
    "data-packages:fleury-integration-service-data-package",
    "data-packages:einstein-integration-service-data-package",
    "data-packages:fhir-domain-service-data-package",
    "data-packages:limbo-api-data-package",
    "data-packages:feature-config-domain-service-data-package",
    "data-packages:feature-config-domain-service-model-package",
    "data-packages:legacy-data-package",
    "data-packages:sales-channel-domain-service-data-package",
    "data-packages:zendesk-integration-service-data-package",
    "data-packages:hr-core-domain-service-data-package",
    "data-packages:itau-integration-service-data-package",
    "data-packages:itau-integration-service-model-package",
    "data-packages:resource-sign-token-data-package",
    "data-packages:resource-sign-token-model-package",
    "feature-config-domain-client",
    "feature-config-domain-service",
    "db-integration-client",
    "db-integration-service",
    "document-signer",
    "dragon-radar-bff-api",
    "dragon-radar-domain-client",
    "dragon-radar-domain-service",
    "ehr-api",
    "ehr-domain-service",
    "ehr-domain-client",
    "einstein-bff-api",
    "einstein-integration-service",
    "einstein-integration-client",
    "eita-nullvs-integration-service",
    "eita-nullvs-integration-client",
    "event-api",
    "eventinder-domain-client",
    "eventinder-domain-service",
    "example-api",
    "exec-indicator-api",
    "exec-indicator-domain-client",
    "exec-indicator-domain-service",
    "file-vault-client",
    "file-vault-service",
    "fleury-integration-client",
    "fleury-integration-service",
    "healthcare-ops-api",
    "haoc-integration-client",
    "haoc-integration-service",
    "health-analytics-event-consumer",
    "health-condition-domain-client",
    "health-condition-domain-service",
    "health-logics-api",
    "health-logic-domain-client",
    "health-logic-domain-service",
    "health-plan-domain-client",
    "health-plan-domain-service",
    "limbo-api",
    "limbo-client",
    "member-api",
    "member-wannabe-api",
    "marauders-map-domain-client",
    "marauders-map-domain-service",
    "membership-domain-client",
    "membership-domain-service",
    "money-in-domain-service",
    "money-in-domain-client",
    "money-in-bff-api",
    "questionnaire-domain-client",
    "questionnaire-domain-service",
    "system-ops-bff-api",
    "test-result-domain-client",
    "test-result-domain-service",
    "wanda-bff-api",
    "wanda-domain-service",
    "wanda-domain-client",
    "schedule-domain-service",
    "schedule-domain-client",
    "scheduler-bff-api",
    "screening-domain-client",
    "screening-domain-service",
    "sherlock-domain-client",
    "sherlock-domain-service",
    "sherlock-api",
    "sorting-hat-domain-client",
    "sorting-hat-domain-service",
    "staff-domain-client",
    "staff-domain-service",
    "provider-domain-client",
    "provider-domain-service",
    "fhir-domain-client",
    "fhir-domain-service",
    "fhir-bff-api",
    "hippocrates-domain-client",
    "hippocrates-domain-service",
    "product-domain-client",
    "product-domain-service",
    "onboarding-domain-client",
    "onboarding-domain-service",
    "person-domain-client",
    "person-domain-service",
    "secondary-attention-domain-client",
    "secondary-attention-domain-service",
    "member-onboarding-domain-client",
    "member-onboarding-domain-service",
    "nullvs-integration-client",
    "nullvs-integration-service",
    "itau-integration-client",
    "itau-integration-service",
    "eita-external-api",
    "duquesa-domain-client",
    "duquesa-domain-service",
    "tiss-domain-client",
    "tiss-domain-service",
    "refund-domain-service",
    "refund-domain-client",
    "sales-channel-domain-client",
    "sales-channel-domain-service",
    "sales-channel-api",
    "zendesk-bff-api",
    "zendesk-integration-service",
    "zendesk-integration-client",
    "backoffice-bff-api",
    "acquisition-domain-client",
    "acquisition-domain-service",
    "hubspot-integration-lib",
    "hr-core-domain-client",
    "hr-core-domain-service",
)

pluginManagement {
    plugins {
        jacoco
        id("org.jetbrains.kotlin.jvm") version "1.7.20"
        id("org.sonarqube") version "3.3"
        id("com.github.johnrengelman.shadow") version "7.0.0"
        id("com.adarshr.test-logger") version "3.0.0"
        id("org.cqfn.diktat.diktat-gradle-plugin") version "1.0.1"
    }

    repositories {
        mavenCentral()
        gradlePluginPortal()
    }
}

plugins {
    id("com.gradle.enterprise") version "3.12.6"
    id("org.gradle.toolchains.foojay-resolver-convention") version "0.8.0"
}

gradleEnterprise {
    buildScan {
        termsOfServiceUrl = "https://gradle.com/terms-of-service"
        termsOfServiceAgree = "yes"
    }
}

buildCache {
    val jenkinsEnv = System.getenv()["JENKINS_ENVIRONMENT"]
    if (jenkinsEnv != null) {
        remote(HttpBuildCache::class) {
            url = uri("https://gradle-cache.devtools.alice.tools/cache/")
            credentials {
                username = "mono"
                password = "@liceM0no!"
            }
            isPush = true
        }
    } else {
        local {
            directory = System.getenv()["GRADLE_BUILD_CACHE_DIR"] ?: File(rootDir, "build-cache")
            removeUnusedEntriesAfterDays = 5
        }
    }
}
include("staff-integration-api")
