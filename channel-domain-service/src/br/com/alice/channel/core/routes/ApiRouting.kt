package br.com.alice.channel.core.routes

import br.com.alice.channel.core.controllers.BackfillController
import br.com.alice.channel.core.controllers.DebugFupController
import br.com.alice.channel.core.controllers.InternalFeaturesController
import br.com.alice.channel.core.controllers.InternalReadController
import br.com.alice.channel.core.services.internal.BackfillChannelModelService
import br.com.alice.common.coHandler
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.apiRoutes() {
    val backfillController by inject<BackfillController>()

    val internalFeaturesController by inject<InternalFeaturesController>()

    route("/internal") {
        post("/merge_channels") { coHandler(internalFeaturesController::mergeChannels) }
        post("/remove_staff_from_channel") { coHandler(internalFeaturesController::removeStaffFromChannel) }
        post("/toggle_member_input") { coHandler(internalFeaturesController::toggleMemberInput) }
        post("/send_messages") { coHandler(internalFeaturesController::sendMessages) }
        post("/create_staff") { coHandler(internalFeaturesController::createStaffs) }
        post("/create_channels") { coHandler(internalFeaturesController::createChannels) }
        post("/force_virtual_clinic_status_control") { coHandler(internalFeaturesController::forceVirtualClinicStatusControl) }
    }

    val internalReadController by inject<InternalReadController>()
    route("/internal/read") {
        get("/{personId}/{channelId}/messages") {
            coHandler(
                "personId",
                "channelId",
                internalReadController::getMessages
            )
        }
    }

    route("/backfill") {
        post("/update_channels") { coHandler(backfillController::updateChannels) }
        post("/update_channel_staffs") { coHandler(backfillController::updateChannelStaffs) }
        post("/update_channel_name_by_type") { coHandler(backfillController::updateChannelNameByType) }
        post("/send_message_to_channel") { coHandler(backfillController::sendMessageToChannel) }
        post("/add_gptag") { coHandler(backfillController::addGptag) }
        post("/add_protocol_ans") { coHandler(backfillController::addProtocolAns) }

        val backfillChannelService by inject<BackfillChannelModelService>()
        post("/channels") { coHandler(backfillChannelService::process) }
    }

    route("/debug") {

        route("/fup") {
            val debugFupController by inject<DebugFupController>()
            post("/{personHealthEventId}") {
                coHandler(
                    "personHealthEventId",
                    debugFupController::sendChannelFupByPersonHealthEvent
                )
            }
        }
    }
}
