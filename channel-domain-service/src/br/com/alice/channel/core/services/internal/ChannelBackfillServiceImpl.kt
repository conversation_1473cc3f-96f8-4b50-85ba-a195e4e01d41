package br.com.alice.channel.core.services.internal

import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.context.firestoreTransactional
import br.com.alice.channel.core.controllers.AddGptagRequest
import br.com.alice.channel.core.controllers.ChannelGenerateProtocolANS
import br.com.alice.channel.core.controllers.SendMessageToChannelRequest
import br.com.alice.channel.core.controllers.UpdateChannel
import br.com.alice.channel.core.controllers.UpdateChannelNameByTypeRequest
import br.com.alice.channel.core.controllers.UpdateChannelRequest
import br.com.alice.channel.core.controllers.UpdateChannelStaffRequest
import br.com.alice.channel.core.controllers.UpdateChannelStaffs
import br.com.alice.channel.core.controllers.UpdateChannelTag
import br.com.alice.channel.core.controllers.UpdateChannelTagAction
import br.com.alice.channel.core.extensions.toModel
import br.com.alice.channel.core.services.internal.firestore.MessageFirestoreService
import br.com.alice.channel.event.ShouldAddGptTagEvent
import br.com.alice.channel.event.ShouldAddProtocolANS
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.ChannelStaffInfo
import br.com.alice.channel.models.MessageDocument
import br.com.alice.channel.models.MessageType
import br.com.alice.common.core.extensions.atSaoPauloZone
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.catchResult
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.ChannelStatus
import br.com.alice.data.layer.models.ChannelType
import br.com.alice.data.layer.models.Staff
import br.com.alice.data.layer.services.NaiveTextualDeIdentificationService
import br.com.alice.person.client.PersonService
import br.com.alice.staff.client.StaffService
import br.com.alice.staff.converters.StaffGenderDescriptionConverter
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import com.google.cloud.Timestamp
import com.google.cloud.firestore.Query
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.Date
import java.util.concurrent.atomic.AtomicInteger
import kotlin.random.Random

class ChannelBackfillServiceImpl(
    private val staffService: StaffService,
    private val personService: PersonService,
    private val internalChannelService: ChannelService,
    private val messageFirestoreService: MessageFirestoreService,
    private val kafkaProducerService: KafkaProducerService,
) : FirestoreRepository() {

    @OptIn(FirestoreContextUsage::class)
    suspend fun updateChannels(request: UpdateChannelRequest): Result<BackfillResponse, Throwable> =
        firestoreTransactional {
            val channelsToBeUpdated = request.channelsToBeUpdated
            val updated = AtomicInteger(0)
            val error = AtomicInteger(0)

            logger.info(
                "ChannelBackfillServiceImpl::updateChannels",
                "qty_channels_to_be_updated" to (channelsToBeUpdated?.size ?: 0)
            )

            channelsToBeUpdated?.forEach { channelDocumentToBeUpdated ->
                try {
                    getChannelById(channelDocumentToBeUpdated.id).let { channelDocument ->
                        try {
                            var commitTransaction = true
                            val notifyAddParticipant = mutableMapOf<String, String>()
                            val notifyRemoveParticipant = mutableMapOf<String, String>()
                            val backfillDateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")

                            val paramsToUpdate = mutableMapOf<String, Any?>()
                            channelDocumentToBeUpdated.name?.let { name ->
                                paramsToUpdate.putIfAbsent(
                                    ChannelDocument::name.name,
                                    name
                                )
                            }
                            channelDocumentToBeUpdated.type?.let { type ->
                                paramsToUpdate.putIfAbsent(
                                    ChannelDocument::type.name,
                                    type
                                )
                            }
                            channelDocumentToBeUpdated.status?.let { status ->
                                paramsToUpdate.putIfAbsent(
                                    ChannelDocument::status.name,
                                    status
                                )
                                paramsToUpdate.putIfAbsent(
                                    ChannelDocument::archivedAt.name,
                                    if (status == ChannelStatus.ARCHIVED.name) Timestamp.now() else null
                                )
                                paramsToUpdate.putIfAbsent(
                                    ChannelDocument::hideMemberInput.name,
                                    status == ChannelStatus.ARCHIVED.name
                                )
                            }
                            channelDocumentToBeUpdated.canBeArchived?.let { canBeArchived ->
                                paramsToUpdate.putIfAbsent(
                                    ChannelDocument::canBeArchived.name,
                                    canBeArchived
                                )
                            }
                            channelDocumentToBeUpdated.staffs?.let { requestStaff ->
                                val staffIdsProperty = mutableSetOf<String>()
                                val staffProperty = mutableMapOf<String, ChannelStaffInfo>()

                                commitTransaction = buildUpdatedStaffMap(
                                    channelDocument,
                                    requestStaff,
                                    notifyRemoveParticipant,
                                    notifyAddParticipant,
                                    staffProperty,
                                    staffIdsProperty,
                                    error
                                )

                                if (staffProperty.isNotEmpty()) {
                                    paramsToUpdate.putIfAbsent(ChannelDocument::staff.name, staffProperty)
                                    channelDocument.staff.minus(staffProperty.keys).let {
                                        paramsToUpdate.putIfAbsent(
                                            ChannelDocument::staffHistory.name,
                                            channelDocument.staffHistory.plus(it)
                                        )
                                    }
                                }
                                if (staffIdsProperty.isNotEmpty()) paramsToUpdate.putIfAbsent(
                                    ChannelDocument::staffIds.name,
                                    staffIdsProperty.toList()
                                )
                            }
                            channelDocumentToBeUpdated.tags?.let { updateChannelTags ->
                                val tags = buildNewChannelTags(channelDocument.tags, updateChannelTags)
                                paramsToUpdate.putIfAbsent(ChannelDocument::tags.name, tags)
                            }

                            channelDocumentToBeUpdated.kind?.let { kind ->
                                paramsToUpdate.putIfAbsent(
                                    ChannelDocument::kind.name,
                                    kind
                                )
                            }

                            channelDocumentToBeUpdated.category?.let { category ->
                                paramsToUpdate.putIfAbsent(
                                    ChannelDocument::category.name,
                                    category
                                )
                            }

                            channelDocumentToBeUpdated.subCategory?.let { subCategory ->
                                paramsToUpdate.putIfAbsent(
                                    ChannelDocument::subCategory.name,
                                    subCategory
                                )
                            }

                            channelDocumentToBeUpdated.subCategoryClassifier?.let { subCategoryClassifier ->
                                paramsToUpdate.putIfAbsent(
                                    ChannelDocument::subCategoryClassifier.name,
                                    subCategoryClassifier
                                )
                            }

                            if (channelDocument.timeLastMessage == null) {
                                paramsToUpdate.putIfAbsent(
                                    ChannelDocument::timeLastMessage.name,
                                    channelDocument.createdAt
                                )
                            } else {
                                channelDocumentToBeUpdated.timeLastMessage?.let {

                                    val timeLastMessage =
                                        LocalDateTime.parse(it, backfillDateTimeFormatter).atSaoPauloZone()
                                    paramsToUpdate.putIfAbsent(
                                        ChannelDocument::timeLastMessage.name,
                                        Timestamp.of(Date.from(timeLastMessage.toInstant()))
                                    )
                                }
                            }

                            if (commitTransaction && request.execute && paramsToUpdate.isNotEmpty()) {
                                updateAndGetChannel(channelDocumentToBeUpdated.id, paramsToUpdate)
                                updated.incrementAndGet()

                                val shouldNotifyStaffsChanged = channelDocumentToBeUpdated.staffs?.notify ?: false

                                if (shouldNotifyStaffsChanged.not()) return@let

                                val notifyRemovedStaffs =
                                    channelDocumentToBeUpdated.staffs?.notifyRemovedStaffs ?: true

                                if (notifyRemovedStaffs) {
                                    sendStaffsChangedMessagesOnChannel(
                                        channelId = channelDocumentToBeUpdated.id,
                                        changedParticipants = notifyRemoveParticipant,
                                        contentDescription = MessageType.REMOVE_PARTICIPANT.description,
                                        messageType = MessageType.REMOVE_PARTICIPANT
                                    )
                                }

                                val notifyAddedStaffs =
                                    channelDocumentToBeUpdated.staffs?.notifyAddedStaffs ?: false

                                if (notifyAddedStaffs.not()) return@let

                                sendStaffsChangedMessagesOnChannel(
                                    channelId = channelDocumentToBeUpdated.id,
                                    changedParticipants = notifyRemoveParticipant,
                                    contentDescription = MessageType.ADD_PARTICIPANT.description,
                                    messageType = MessageType.ADD_PARTICIPANT
                                )
                            }
                        } catch (e: Exception) {
                            error.incrementAndGet()
                        }
                    }
                } catch (e: Exception) {
                    logger.error(
                        "Error retrieving channel by id",
                        "channel_id" to channelDocumentToBeUpdated.id,
                        "error_message" to e.message
                    )
                    error.incrementAndGet()
                }
            }

            BackfillResponse(updated.get(), error.get()).success()
        }

    @OptIn(FirestoreContextUsage::class)
    suspend fun updateChannelStaffs(request: UpdateChannelStaffRequest): Result<BackfillResponse, Throwable> =
        firestoreTransactional {
            val staffIdToBeReplaced = request.staffIdToBeReplaced
            val updated = AtomicInteger(0)
            val error = AtomicInteger(0)

            logger.info(
                "ChannelBackfillServiceImpl::updateChannelStaffs",
                "staff_to_be_replaced" to staffIdToBeReplaced
            )

            staffService.get(request.newStaffId.toUUID())
                .map { newStaff ->
                    val channelsMap = getChannelsByTypeAndStaff(request)
                    val personIds = channelsMap.map { it.value.personId }.distinct()
                    val persons = personService.findByIds(personIds).get()
                    val personsMap = persons.associateBy({ it.id.toString() }, { it })

                    logger.info(
                        "ChannelBackfillServiceImpl::updateChannelStaffs mapping persons",
                        "person_ids_count" to personIds.size,
                        "persons_count" to persons.size,
                        "persons_map_key_count" to personsMap.keys.size,
                    )

                    channelsMap.values.forEach { channelDocument ->
                        channelDocument.id?.let { channelId ->
                            channelDocument.personId.let { personId ->
                                val person = personsMap[personId]
                                val newChannelStaffInfo = getNewChannelStaffInfo(channelDocument, request, newStaff)
                                newChannelStaffInfo?.let { channelStaffInfo ->
                                    channelDocument.staff.remove(staffIdToBeReplaced)

                                    channelDocument.staff.putIfAbsent(request.newStaffId, channelStaffInfo)
                                    val newStaffIdsList = channelDocument.staff.keys.toList()

                                    val paramsToUpdate = mapOf(
                                        "staff" to channelDocument.staff,
                                        "staffIds" to newStaffIdsList
                                    )

                                    if (request.execute) {
                                        updateAndGetChannel(channelId, paramsToUpdate)

                                        val messageContent = buildUpdateChannelStaffMessage(
                                            personNickName = person?.nickName,
                                            staffName = newStaff.firstName
                                        )

                                        sendMessageToChannel(
                                            channelId,
                                            newStaff.id.toString(),
                                            messageContent,
                                            MessageType.TEXT
                                        )

                                        updated.incrementAndGet()
                                    }
                                }
                            } ?: run {
                                error.incrementAndGet()
                            }
                        } ?: run {
                            error.incrementAndGet()
                        }
                    }
                }

            BackfillResponse(updated.get(), error.get()).success()
        }

    suspend fun updateChannelNameByType(request: UpdateChannelNameByTypeRequest): Result.Success<BackfillResponse> {
        val channelTypeToBeRenamed = request.channelTypeToBeRenamed
        var updated = 0
        var error = 0
        logger.info(
            "Updating channels names by type",
            "channel_type_to_be_renamed" to channelTypeToBeRenamed
        )

        try {

            val channelsMap =
                getActiveChannelsByType(channelTypeToBeRenamed, request.channelPersonIds, request.limit, request.offset)

            val channelsToBeUpdated = mutableListOf<UpdateChannel>()

            channelsMap.forEach { channelToUpdate ->

                channelToUpdate.key?.let { channelId ->

                    val channelDocument = channelToUpdate.value

                    if (channelDocument.name != request.channelNewName)
                        channelsToBeUpdated.add(
                            UpdateChannel(
                                channelId,
                                null,
                                request.channelNewName,
                                null,
                                null,
                                null,
                                null,
                                null,
                                null
                            )
                        )
                }
            }

            logger.info(
                "qty channels available to update",
                "qty_channels_available_to_update" to channelsToBeUpdated.size
            )

            try {

                if (channelsToBeUpdated.isNotEmpty()) {
                    val updatedChannels =
                        updateChannels(UpdateChannelRequest(request.execute, channelsToBeUpdated)).get()
                    updated += updatedChannels.successCount
                    updated += updatedChannels.errorsCount
                }
            } catch (e: Exception) {
                logger.error(
                    "Error while updating channels name",
                    "error_message" to e.message
                )
                error += 1
            }
        } catch (e: Exception) {
            logger.error(
                "Error retrieving active channels by type",
                "channel_type" to channelTypeToBeRenamed,
                "error_message" to e.message
            )
            error += 1
        }

        return BackfillResponse(updated, error).success()
    }

    @OptIn(FirestoreContextUsage::class)
    suspend fun sendMessageToChannel(request: SendMessageToChannelRequest): Result<BackfillResponse, Throwable> =
        firestoreTransactional {
            val channelsWillReceiveNewMessage = request.channels
            var channelsReceivedMessage = 0
            var error = 0

            logger.info(
                "Sending message to channels",
                "qty_channels_will_receive_new_message" to (channelsWillReceiveNewMessage?.size ?: 0)
            )

            channelsWillReceiveNewMessage?.forEach { messageToChannel ->
                try {
                    val channelDocument = getChannelById(messageToChannel.channelId)

                    channelDocument.id?.let { channelId ->
                        val personId = channelDocument.personId
                        personService.get(personId.toPersonId(), withUserType = false)
                            .then { person ->

                                val message = NaiveTextualDeIdentificationService.identify(
                                    messageToChannel.message,
                                    person.toPersonPII()
                                )

                                val staffId = messageToChannel.staffId?.let { requestStaffId ->
                                    staffService.get(requestStaffId.toUUID())
                                        .map { it.id.toString() }.get()
                                } ?: ""

                                val channelHasRequestedStaffId = if (staffId.isNotEmpty()) {
                                    channelDocument.staffIds?.any { it == staffId } ?: false
                                } else {
                                    true
                                }

                                if (channelHasRequestedStaffId.not()) {
                                    error += 1
                                }

                                if (channelHasRequestedStaffId && request.execute && messageToChannel.message.isNotEmpty()) {

                                    val messageDocument = MessageDocument(
                                        aliceId = staffId,
                                        userId = staffId,
                                        content = message,
                                        type = MessageType.TEXT,
                                    )

                                    messageFirestoreService.add(channelId, messageDocument)
                                    channelsReceivedMessage += 1
                                }
                            }.thenError {
                                logger.error(
                                    "Person not found for id:${personId}",
                                    "personId" to personId,
                                    "error_message" to it.message,
                                )
                                error += 1
                                it
                            }
                    } ?: run {
                        error += 1
                    }
                } catch (e: Exception) {
                    logger.error(
                        "Error sending message to channel by id",
                        "channel_id" to messageToChannel.channelId,
                        "staff_id" to messageToChannel.staffId,
                        "message" to messageToChannel.message,
                        "error_message" to e.message
                    )
                    error += 1
                }
            }

            BackfillResponse(channelsReceivedMessage, error).success()
        }


    private suspend fun buildUpdatedStaffMap(
        channelDocument: ChannelDocument,
        requestStaff: UpdateChannelStaffs,
        notifyRemoveParticipant: MutableMap<String, String>,
        notifyAddParticipant: MutableMap<String, String>,
        staffProperty: MutableMap<String, ChannelStaffInfo>,
        staffIdsProperty: MutableSet<String>,
        error: AtomicInteger
    ): Boolean {
        var commitTransaction = true
        val maxLastSync =
            channelDocument.staff.maxByOrNull { it.value.lastSync }?.value?.lastSync
                ?: Timestamp.now()

        if (requestStaff.notify) {
            channelDocument.staff.forEach {
                notifyRemoveParticipant[it.key] = it.value.name ?: ""
            }
        }

        requestStaff.staffIds.forEach { staffId ->
            staffService.get(staffId.toUUID())
                .fold(
                    { staffData ->
                        val newStaffId = staffData.id.toString()
                        val lastSync = channelDocument.staff[newStaffId]?.lastSync ?: maxLastSync

                        if (requestStaff.notify) {
                            if (notifyRemoveParticipant.contains(newStaffId)) {
                                notifyRemoveParticipant.remove(newStaffId)
                            } else {
                                notifyAddParticipant[newStaffId] = staffData.firstName
                            }
                        }

                        staffProperty.putIfAbsent(
                            newStaffId,
                            ChannelStaffInfo(
                                id = newStaffId,
                                lastSync = lastSync,
                                name = staffData.firstName,
                                firstName = staffData.firstName,
                                lastName = staffData.lastName,
                                description = StaffGenderDescriptionConverter.convert(staffData),
                                profileImageUrl = staffData.profileImageUrl.orEmpty(),
                                owner = (requestStaff.owner == newStaffId),
                            )
                        )

                        staffIdsProperty.add(newStaffId)
                    },
                    { exception ->
                        logger.error(
                            "ChannelBackfillServiceImpl::buildUpdatedStaffMap Staff not found",
                            "staff_id" to staffId.toUUID(),
                            "exception_message" to exception.message
                        )

                        commitTransaction = false
                        error.incrementAndGet()
                    }
                )
        }

        return commitTransaction
    }

    private fun buildUpdateChannelStaffMessage(personNickName: String?, staffName: String) =
        """
            Oi${if (personNickName.isNullOrEmpty()) "" else " $personNickName"}, tudo bem?
            Meu nome é $staffName, sua nova nutricionista! Isso porque, a partir dessa semana, a Alessandra não atenderá mais como nutricionista na Alice.
            Alinhado com os seus objetivos com nutrição, eu vou dar continuidade ao seu acompanhamento e te ajudar a partir de agora. Reforço que todo seu histórico de consultas, planos de ação em nutrição e orientações está salvo no seu app.
            Qualquer dúvida é só me chamar!
        """.trimIndent()

    @FirestoreContextUsage
    private suspend fun sendMessageToChannel(channelId: String, staffId: String, content: String, type: MessageType) {
        messageFirestoreService.add(
            channelId,
            MessageDocument(
                aliceId = staffId,
                userId = staffId,
                content = content,
                type = type
            )
        )
    }

    private suspend fun updateAndGetChannel(channelId: String, paramsToUpdate: Map<String, Any?>): ChannelDocument {
        val batch = firestore.batch()
        val docRef = firestore.collection(channelsCollection).document(channelId)
        paramsToUpdate.entries.forEach { (key, value) -> batch.update(docRef, key, value) }
        batch.update(docRef, "updatedAt", Date())
        batch.commit().get()
        return getChannelById(channelId)
    }

    private fun getNewChannelStaffInfo(
        channelDocument: ChannelDocument,
        request: UpdateChannelStaffRequest,
        newStaff: Staff
    ) =
        channelDocument.staff[request.staffIdToBeReplaced]?.let { staffInfo ->
            val newChannelStaffInfo = ChannelStaffInfo(
                id = request.newStaffId,
                lastSync = staffInfo.lastSync,
                name = newStaff.firstName,
                firstName = newStaff.firstName,
                lastName = newStaff.lastName,
                description = StaffGenderDescriptionConverter.convert(newStaff),
                profileImageUrl = newStaff.profileImageUrl.orEmpty(),
                owner = staffInfo.owner
            )
            newChannelStaffInfo
        }

    private suspend fun getChannelById(channelId: String): ChannelDocument =
        internalChannelService.getChannel(channelId).get()

    private fun getActiveChannelsByType(
        typeName: String,
        channelPersonIds: List<String>?,
        limit: Int, offset: Int,
    ) =
        firestore.collection(channelsCollection)
            .whereEqualTo("type", typeName)
            .whereEqualTo("status", "ACTIVE")
            .orderBy("createdAt", Query.Direction.ASCENDING)
            .limit(limit)
            .offset(offset)
            .get()
            .get()
            .documents
            .map {
                it.toModel(ChannelDocument::class)
            }
            .filter {
                if (channelPersonIds.isNullOrEmpty()) {
                    true
                } else {
                    channelPersonIds.contains(it.channelPersonId)
                }
            }
            .associateBy({ it.id }, { it })

    private fun getChannelsByTypeAndStaff(request: UpdateChannelStaffRequest) =
        if (request.channelType.isNullOrBlank()) {
            firestore.collection(channelsCollection)
        } else {
            val channelType = ChannelType.valueOf(request.channelType)
            firestore.collection(channelsCollection)
                .whereEqualTo("type", channelType.name)

        }.let { query ->
            query
                .whereArrayContains("staffIds", request.staffIdToBeReplaced)
                .orderBy("createdAt", Query.Direction.ASCENDING)
                .limit(request.limit)
                .offset(request.offset)
                .get()
                .get()
                .documents
                .map { queryDocumentSnapshot ->
                    queryDocumentSnapshot.toModel(ChannelDocument::class)
                }
                .filter { channelDocument ->
                    if (request.channelPersonIds.isNotEmpty())
                        request.channelPersonIds.contains(channelDocument.channelPersonId)
                    else true
                }
                .associateBy({ channelDocument -> channelDocument.id }, { channelDocument -> channelDocument })
        }

    private fun buildNewChannelTags(
        currentTags: List<String>,
        updateChannelTags: List<UpdateChannelTag>,
    ): List<String> {
        val tagsToBeAdded =
            updateChannelTags.filter { it.action == UpdateChannelTagAction.ADD }.map { it.name.lowercase() }
        val tagsToBeRemoved =
            updateChannelTags.filter { it.action == UpdateChannelTagAction.REMOVE }.map { it.name.lowercase() }

        val newTags = currentTags.plus(tagsToBeAdded).minus(tagsToBeRemoved).distinct()

        return newTags
    }

    @FirestoreContextUsage
    private suspend fun sendStaffsChangedMessagesOnChannel(
        channelId: String,
        changedParticipants: MutableMap<String, String>,
        contentDescription: String,
        messageType: MessageType = MessageType.TEXT
    ) =
        changedParticipants.forEach { changedParticipant ->
            sendMessageToChannel(
                channelId = channelId,
                staffId = changedParticipant.key,
                content = "${changedParticipant.value} $contentDescription",
                type = messageType,
            )
        }

    suspend fun addGptag(request: AddGptagRequest): Result.Success<BackfillResponse> {
        request.channelAndInternalCodes.forEach {
            val event = ShouldAddGptTagEvent(it.channelId, it.internalCode)
            logger.info(
                "ChannelBackfillServiceImpl::addGptag: sending ShouldAddGptagEvent",
                "event" to event
            )
            kafkaProducerService.produce(event)
        }
        return BackfillResponse(request.channelAndInternalCodes.size, 0).success()
    }

    suspend fun addProtocolAns(request: ChannelGenerateProtocolANS): Result<BackfillResponse, Throwable> {
        val size = request.channelIds.size
        if (size < 1) return BackfillResponse(0, 0).success()
        val successCount = AtomicInteger(0)
        val errorsCount = AtomicInteger(0)

        catchResult {
            val protocols = getProtocols(size, request)

            internalChannelService.getChannelsById(request.channelIds).map { channels ->
                channels.sortedBy { it.createdAt }.mapIndexed { i, channel ->
                    kafkaProducerService.produce(
                        ShouldAddProtocolANS(
                            channelId = channel.id(),
                            protocol = protocols[i]
                        )
                    )
                    successCount.incrementAndGet()
                }
            }

        }.thenError {
            logger.error(
                "ChannelBackfillServiceImpl::addProtocolAns: error",
                it
            )
            errorsCount.incrementAndGet()
        }

        return BackfillResponse(
            successCount = successCount.get(),
            errorsCount = errorsCount.get()
        ).success()
    }

    private fun getProtocols(
        qtd: Int,
        request: ChannelGenerateProtocolANS
    ): List<String> = List(qtd) { request.date.generateProtocol() }.sorted()

    private fun LocalDate.generateProtocol(): String =
        Random.nextLong(1, 999999)
            .toString()
            .padStart(6, '0')
            .substring(0, 6)
            .let {
                StringBuilder()
                    .append("421928")
                    .append(this.year)
                    .append(String.format("%02d", this.monthValue))
                    .append(this.dayOfMonth)
                    .append(it)
                    .toString()
            }


}

data class BackfillResponse(
    val successCount: Int,
    val errorsCount: Int,
    val message: String? = null,
    val additionalInfo: Map<String, Any?>? = null,
)
