package br.com.alice.channel.core.ioc

import br.com.alice.akinator.ioc.AkinatorDomainClientModule
import br.com.alice.appointment.ioc.AppointmentDomainClientModule
import br.com.alice.business.ioc.BusinessDomainClientModule
import br.com.alice.channel.SERVICE_NAME
import br.com.alice.channel.client.*
import br.com.alice.channel.core.consumers.ArchiveChannelConsumer
import br.com.alice.channel.core.consumers.AutomaticFollowUpConsumer
import br.com.alice.channel.core.consumers.BackfillConsumer
import br.com.alice.channel.core.consumers.BudResultConsumer
import br.com.alice.channel.core.consumers.ChannelConsumer
import br.com.alice.channel.core.consumers.ChannelsAuditConsumer
import br.com.alice.channel.core.consumers.CloudFunctionEventsConsumer
import br.com.alice.channel.core.consumers.CsatConsumer
import br.com.alice.channel.core.consumers.FirstChannelOpenedCheckConsumer
import br.com.alice.channel.core.consumers.FirstChannelOpenedConsumer
import br.com.alice.channel.core.consumers.MoveMergedChannelsConsumer
import br.com.alice.channel.core.consumers.PersonCaseConsumer
import br.com.alice.channel.core.consumers.QuestionnaireCompletedConsumer
import br.com.alice.channel.core.consumers.RoutingConsumer
import br.com.alice.channel.core.consumers.StaffConsumer
import br.com.alice.channel.core.controllers.BackfillController
import br.com.alice.channel.core.controllers.DebugFupController
import br.com.alice.channel.core.controllers.InternalFeaturesController
import br.com.alice.channel.core.controllers.InternalReadController
import br.com.alice.channel.core.controllers.RecurringController
import br.com.alice.channel.core.services.AvailabilityServiceImpl
import br.com.alice.channel.core.services.ChannelDeIdentifiedServiceImpl
import br.com.alice.channel.core.services.ChannelDemandServiceImpl
import br.com.alice.channel.core.services.ChannelFupServiceImpl
import br.com.alice.channel.core.services.ChannelHistoryServiceImpl
import br.com.alice.channel.core.services.ChannelServiceImpl
import br.com.alice.channel.core.services.FollowUpServiceImpl
import br.com.alice.channel.core.services.MacroServiceImpl
import br.com.alice.channel.core.services.StaffChannelHistoryServiceImpl
import br.com.alice.channel.core.services.StaffServiceImpl
import br.com.alice.channel.core.services.TagServiceImpl
import br.com.alice.channel.core.services.WorkingHoursServiceImpl
import br.com.alice.channel.core.services.internal.AutomaticFollowUpService
import br.com.alice.channel.core.services.internal.BackfillChannelModelService
import br.com.alice.channel.core.services.internal.ChannelBackfillServiceImpl
import br.com.alice.channel.core.services.internal.ChannelBackupService
import br.com.alice.channel.core.services.internal.ChannelCreateService
import br.com.alice.channel.core.services.internal.ChannelNotificationService
import br.com.alice.channel.core.services.internal.ChatOutOfWorkingHoursService
import br.com.alice.channel.core.services.internal.FirestoreEventService
import br.com.alice.channel.core.services.internal.FollowUpHistoryService
import br.com.alice.channel.core.services.internal.RoutingService
import br.com.alice.channel.core.services.internal.StaffChannelWriteService
import br.com.alice.channel.core.services.internal.firestore.ChannelFirestoreService
import br.com.alice.channel.core.services.internal.firestore.MessageChannelMergedFirestoreService
import br.com.alice.channel.core.services.internal.firestore.MessageFirestoreService
import br.com.alice.channel.core.services.internal.firestore.StaffChannelFirestoreService
import br.com.alice.channel.core.services.internal.firestore.StaffFirestoreService
import br.com.alice.channel.core.services.internal.logics.ChannelMergeService
import br.com.alice.channel.core.services.internal.logics.ChannelMessageSanitizeService
import br.com.alice.channel.core.services.internal.processors.AppVersionProcessor
import br.com.alice.channel.core.services.internal.processors.ChannelHistoryProcessor
import br.com.alice.channel.core.services.internal.processors.ChannelProcessor
import br.com.alice.channel.core.services.internal.processors.LastMessageProcessor
import br.com.alice.channel.core.services.internal.processors.MemberImageMessageProcessor
import br.com.alice.channel.core.services.internal.processors.MemberMessageProcessor
import br.com.alice.channel.core.services.internal.processors.MessageCounterProcessor
import br.com.alice.channel.core.services.internal.processors.PrivateMessageChannelCategoryProcessor
import br.com.alice.channel.core.services.internal.processors.PrivateMessageCounterProcessor
import br.com.alice.channel.core.services.internal.processors.PrivateMessageProcessor
import br.com.alice.channel.core.services.internal.processors.PublicMessageChannelCategoryProcessor
import br.com.alice.channel.core.services.internal.processors.PublicMessageProcessor
import br.com.alice.channel.core.services.internal.processors.PushProcessor
import br.com.alice.channel.core.services.internal.processors.QuestionnaireProcessorV2
import br.com.alice.channel.core.services.internal.processors.RingProcessor
import br.com.alice.channel.core.services.internal.processors.StaffChannelProcessor
import br.com.alice.channel.core.services.internal.processors.StaffCountersProcessor
import br.com.alice.channel.core.services.internal.processors.WaitingSinceProcessor
import br.com.alice.clinicalaccount.ioc.ClinicalAccountDomainClientModule
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.common.service.data.layer.DataLayerClientConfiguration
import br.com.alice.common.service.serialization.simpleGson
import br.com.alice.communication.ioc.CommunicationModule
import br.com.alice.data.layer.services.*
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.filevault.ioc.FileVaultClientModule
import br.com.alice.healthcondition.ioc.HealthConditionDomainClientModule
import br.com.alice.healthlogic.ioc.HealthLogicDomainClientModule
import br.com.alice.healthplan.ioc.HealthPlanDomainClientModule
import br.com.alice.marauders.map.ioc.MaraudersMapDomainClientModule
import br.com.alice.membership.ioc.MembershipClientModule
import br.com.alice.moneyin.ioc.MoneyInClientModule
import br.com.alice.person.ioc.PersonDomainClientModule
import br.com.alice.questionnaire.ioc.QuestionnaireDomainClientModule
import br.com.alice.schedule.ioc.AppointmentScheduleDomainClientModule
import br.com.alice.sortinghat.ioc.SortingHatDomainClientModule
import br.com.alice.staff.ioc.StaffDomainClientModule
import br.com.alice.wanda.ioc.WandaDomainClientModule
import com.typesafe.config.ConfigFactory
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.config.HoconApplicationConfig
import org.koin.core.qualifier.named
import org.koin.dsl.module
import br.com.alice.channel.core.services.internal.ChannelService as InternalChannelService

val ChannelCoreModules = listOf(
    CommunicationModule,
    FeatureConfigDomainClientModule,
    MembershipClientModule,
    KafkaProducerModule,
    AppointmentScheduleDomainClientModule,
    WandaDomainClientModule,
    StaffDomainClientModule,
    PersonDomainClientModule,
    QuestionnaireDomainClientModule,
    HealthConditionDomainClientModule,
    SortingHatDomainClientModule,
    HealthPlanDomainClientModule,
    ClinicalAccountDomainClientModule,
    AppointmentDomainClientModule,
    MaraudersMapDomainClientModule,
    HealthLogicDomainClientModule,
    FileVaultClientModule,
    AkinatorDomainClientModule,
    MoneyInClientModule,
    BusinessDomainClientModule,

    module(createdAtStart = true) {

        // Configuration
        val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))
        single { config }
        single {
            DefaultHttpClient({
                install(ContentNegotiation) {
                    simpleGson()
                }
            }, timeoutInMillis = 15_000)
        }

        // Internal Services
        single { AutomaticFollowUpService(get(), get(), get(), get()) }
        single { ChannelBackupService(get(), get()) }
        single { ChannelBackfillServiceImpl(get(), get(), get(), get(), get()) }
        single { ChatOutOfWorkingHoursService(get(), get()) }
        single { InternalChannelService(get(), get(), get(), get(), get()) }
        single { FollowUpHistoryService(get()) }
        single { RecurringController(get<StaffService>() as StaffServiceImpl, get()) }
        single { RoutingService(get(), get(), get()) }
        single { StaffChannelWriteService(get()) }
        single { ChannelNotificationService(get()) }
        single { ChannelCreateService(get(), get(), get(), get(), get(), get(), get()) }

        // Internal Logics Services
        single { ChannelMergeService(get(), get(), get(), get()) }
        single { ChannelMessageSanitizeService(get(), get(), get(), get()) }

        // Internal Firestore Services
        single { ChannelFirestoreService() }
        single { MessageFirestoreService() }
        single { StaffChannelFirestoreService() }
        single { StaffFirestoreService() }
        single { MessageChannelMergedFirestoreService() }
        single { FirestoreEventService(get(), get(), get(), get()) }

        // Processors
        single { QuestionnaireProcessorV2(get(), get(), get(), get(), get()) }

        single<PublicMessageProcessor>(named("AppVersionProcessor")) { AppVersionProcessor(get()) }
        single<PublicMessageProcessor>(named("ChannelHistoryProcessor")) { ChannelHistoryProcessor(get()) }
        single<PublicMessageProcessor>(named("LastMessageProcessor")) { LastMessageProcessor(get()) }
        single<PublicMessageProcessor>(named("MemberImageMessageProcessor")) { MemberImageMessageProcessor(get()) }
        single<PublicMessageProcessor>(named("MemberMessageProcessor")) { MemberMessageProcessor(get()) }
        single<PublicMessageProcessor>(named("MessageCounterProcessor")) { MessageCounterProcessor(get(), get()) }
        single<PublicMessageProcessor>(named("PublicMessageChannelCategoryProcessor")) {
            PublicMessageChannelCategoryProcessor(
                get()
            )
        }
        single<PublicMessageProcessor>(named("PushProcessor")) { PushProcessor(get(), get()) }
        single<PublicMessageProcessor>(named("QuestionnaireProcessorV2")) { get() as QuestionnaireProcessorV2 }
        single<PublicMessageProcessor>(named("RingProcessor")) { RingProcessor(get()) }
        single<PublicMessageProcessor>(named("WaitingSinceProcessor")) { WaitingSinceProcessor(get()) }
        single<PrivateMessageProcessor>(named("PrivateMessageCounterProcessor")) { PrivateMessageCounterProcessor(get()) }
        single<PrivateMessageProcessor>(named("PrivateMessageChannelCategoryProcessor")) {
            PrivateMessageChannelCategoryProcessor(
                get()
            )
        }
        single<ChannelProcessor>(named("StaffCountersProcessor")) { StaffCountersProcessor(get()) }
        single<ChannelProcessor>(named("StaffChannelProcessor")) { StaffChannelProcessor(get(), get()) }

        // Temp Services
        single { BackfillChannelModelService(get()) }

        // Exposed Services
        single<AvailabilityService> { AvailabilityServiceImpl(get()) }
        single<ChannelDemandService> { ChannelDemandServiceImpl(get(), get(), get(), get(), get(), get()) }
        single<ChannelService> {
            ChannelServiceImpl(get(), get(), get(), get(), get(), get(), get(), get(), get(), get())
        }
        single<ChannelDeIdentifiedService> { ChannelDeIdentifiedServiceImpl(get(), get(), get(), get()) }
        single<ChannelHistoryService> { ChannelHistoryServiceImpl(get()) }
        single<ChannelFupService> { ChannelFupServiceImpl(get()) }
        single<FollowUpService> { FollowUpServiceImpl(get(), get(), get(), get(), get(), get()) }
        single<MacroService> { MacroServiceImpl(get()) }
        single<StaffService> { StaffServiceImpl(get(), get(), get()) }
        single<StaffChannelHistoryService> { StaffChannelHistoryServiceImpl(get()) }
        single<TagService> { TagServiceImpl(get()) }
        single<WorkingHoursService> { WorkingHoursServiceImpl(get()) }

        // Controllers
        single { HealthController(SERVICE_NAME) }
        single { BackfillController(get()) }
        single { DebugFupController(get(), get()) }
        single { InternalFeaturesController(get(), get(), get(), get(), get(), get()) }
        single { InternalReadController(get()) }

        // Data Services
        val invoker = DataLayerClientConfiguration.build()
        single<AliceAgoraWorkingHoursDataService> { AliceAgoraWorkingHoursDataServiceClient(invoker) }
        single<ChannelDataService> { ChannelDataServiceClient(invoker) }
        single<ChannelThemeDataService> { ChannelThemeDataServiceClient(invoker) }
        single<ChannelFupDataService> { ChannelFupDataServiceClient(invoker) }
        single<ChannelHealthConditionIgnoreDataService> { ChannelHealthConditionIgnoreDataServiceClient(invoker) }
        single<ChannelHistoryDataService> { ChannelHistoryDataServiceClient(invoker) }
        single<ChannelMacroDataService> { ChannelMacroDataServiceClient(invoker) }
        single<ChannelTagDataService> { ChannelTagDataServiceClient(invoker) }
        single<FollowUpHistoryDataService> { FollowUpHistoryDataServiceClient(invoker) }
        single<VideoCallDataService> { VideoCallDataServiceClient(invoker) }
        single<StaffChannelHistoryDataService> { StaffChannelHistoryDataServiceClient(invoker) }

        // Kafka Consumers
        single { ArchiveChannelConsumer(get()) }
        single { AutomaticFollowUpConsumer(get(), get(), get(), get(), get(), get(), get()) }
        single { BackfillConsumer(get()) }
        single { BudResultConsumer(get()) }
        single { ChannelConsumer(get(), get()) }
        single { ChannelsAuditConsumer(get(), get(), get()) }
        single { CloudFunctionEventsConsumer(getAll(), getAll(), getAll(), get(), get()) }
        single { CsatConsumer(get(), get()) }
        single { FirstChannelOpenedCheckConsumer(get(), get()) }
        single { FirstChannelOpenedConsumer(get(), get()) }
        single { MoveMergedChannelsConsumer(get()) }
        single { PersonCaseConsumer(get()) }
        single { QuestionnaireCompletedConsumer(get()) }
        single { RoutingConsumer(get(), get()) }
        single { StaffConsumer(get(), get(named("sync-staff-service")), get()) }
    }
)
