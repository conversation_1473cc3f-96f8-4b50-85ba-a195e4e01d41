package br.com.alice.channel.core.controllers

import br.com.alice.channel.core.services.internal.BackfillResponse
import br.com.alice.channel.core.services.internal.ChannelBackfillServiceImpl
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.models.ChannelStatus
import br.com.alice.data.layer.models.ChannelType
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import java.time.LocalDate
import kotlin.test.BeforeTest
import kotlin.test.Test

class BackfillControllerTest : RoutesTestHelper() {

    private val channelBackfillServiceImpl: ChannelBackfillServiceImpl = mockk()

    private val backfillController = BackfillController(channelBackfillServiceImpl)

    private val execute = true
    private val staffId: String = "staffId"
    private val channelId: String = "channelId"
    private val channelName: String = "Time de Saúde"

    private val updateChannel = UpdateChannel(
        id = channelId,
        type = ChannelType.HEALTH_PLAN.name,
        name = channelName,
        status = ChannelStatus.ACTIVE.name,
        staffs = null,
        tags = null,
        kind = null,
        category = null,
        subCategory = null,
        subCategoryClassifier = null
    )
    private val updateChannelRequest = UpdateChannelRequest(
        execute = execute,
        channelsToBeUpdated = listOf(updateChannel),
    )

    private val updateChannelNameByTypeRequest = UpdateChannelNameByTypeRequest(
        execute = execute,
        offset = 0,
        limit = 100,
        channelNewName = channelName,
        channelTypeToBeRenamed = ChannelType.HEALTH_PLAN.name,
        channelPersonIds = null,
    )

    private val messageToChannel = MessageToChannel(
        channelId = channelId,
        staffId = staffId,
        message = "Olá @nickname, como está se sentido hoje?",
    )
    private val sendMessageToChannelRequest = SendMessageToChannelRequest(
        execute = execute,
        channels = listOf(messageToChannel),
    )

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { backfillController }
    }

    @Test
    fun `#updateChannels should update channels`() {
        val expected =
            BackfillResponse(successCount = updateChannelRequest.channelsToBeUpdated?.size ?: 0, errorsCount = 0)
        coEvery { channelBackfillServiceImpl.updateChannels(updateChannelRequest) } returns expected.success()

        post("/backfill/update_channels", body = updateChannelRequest) {
            assertThat(it).isOKWithData(expected)
            coVerify(exactly = 1) { channelBackfillServiceImpl.updateChannels(updateChannelRequest) }
        }
    }

    @Test
    fun `#updateChannelNameByType should update channels by type`() {
        val expected = BackfillResponse(successCount = updateChannelNameByTypeRequest.limit, errorsCount = 0)
        coEvery { channelBackfillServiceImpl.updateChannelNameByType(updateChannelNameByTypeRequest) } returns expected.success()

        post("/backfill/update_channel_name_by_type", body = updateChannelNameByTypeRequest) {
            assertThat(it).isOKWithData(expected)
            coVerify(exactly = 1) { channelBackfillServiceImpl.updateChannelNameByType(updateChannelNameByTypeRequest) }
        }
    }

    @Test
    fun `#sendMessageToChannel should send message to channel`() {
        val expected = BackfillResponse(successCount = sendMessageToChannelRequest.channels?.size ?: 0, errorsCount = 0)
        coEvery { channelBackfillServiceImpl.sendMessageToChannel(sendMessageToChannelRequest) } returns expected.success()

        post("/backfill/send_message_to_channel", body = sendMessageToChannelRequest) {
            assertThat(it).isOKWithData(expected)
            coVerify(exactly = 1) { channelBackfillServiceImpl.sendMessageToChannel(sendMessageToChannelRequest) }
        }
    }

    @Test
    fun `#addProtocolAns - should add protocol ans`() {
        val request = ChannelGenerateProtocolANS(
            channelIds = listOf("channelId1", "channelId2"),
            date = LocalDate.now(),
        )
        val expectedResponse = BackfillResponse(successCount = 1, errorsCount = 0)

        coEvery {
            channelBackfillServiceImpl.addProtocolAns(request)
        } returns expectedResponse.success()

        post("/backfill/add_protocol_ans",request) {
            assertThat(it).isOKWithData(expectedResponse)
            coVerifyOnce { channelBackfillServiceImpl.addProtocolAns(request) }
        }
    }

}
