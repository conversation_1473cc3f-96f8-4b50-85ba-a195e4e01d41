package br.com.alice.api.healthcareops.converters.business

import br.com.alice.api.healthcareops.converters.business.CompanyConverter.toBillingAccountableParty
import br.com.alice.api.healthcareops.converters.business.CompanyConverter.toCompany
import br.com.alice.api.healthcareops.converters.business.CompanyConverter.toCompanyInfoResponse
import br.com.alice.api.healthcareops.converters.ProductConverter
import br.com.alice.api.healthcareops.converters.business.CompanyConverter.toCompanyInvoiceResponseV2
import br.com.alice.api.healthcareops.models.CompanyBillingAccountableParty
import br.com.alice.api.healthcareops.models.CompanyInfoResponse
import br.com.alice.api.healthcareops.models.CompanyInvoiceInfo
import br.com.alice.api.healthcareops.models.CompanyInvoicePayment
import br.com.alice.api.healthcareops.models.CompanyInvoiceResponseV2
import br.com.alice.api.healthcareops.models.CreateCompanyRequest
import br.com.alice.common.RangeUUID
import br.com.alice.common.convertTo
import br.com.alice.common.core.extensions.atBeginningOfTheMonth
import br.com.alice.common.core.extensions.atEndOfTheMonth
import br.com.alice.common.core.extensions.money
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Address
import br.com.alice.data.layer.models.BillingAccountableParty
import br.com.alice.data.layer.models.BillingAccountablePartyType
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanyAddress
import br.com.alice.moneyin.models.BankSlipDetail
import br.com.alice.moneyin.models.CompanyInvoiceModel
import br.com.alice.moneyin.models.CompanyInvoicePaymentMethod as MoneyInCompanyInvoicePaymentMethod
import br.com.alice.moneyin.models.CompanyInvoicePaymentStatus
import br.com.alice.moneyin.models.CompanyInvoiceStatus
import br.com.alice.moneyin.models.CompanyInvoiceType
import br.com.alice.moneyin.models.CompanyInvoiceValidityPeriod
import br.com.alice.moneyin.models.InvoicePaymentWithPortalUrl
import br.com.alice.api.healthcareops.models.CompanyInvoicePaymentMethod
import com.github.kittinunf.result.success
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.Test
import br.com.alice.moneyin.models.CompanyInvoicePayment as MoneyInCompanyInvoicePayment
import br.com.alice.moneyin.models.CompanyInvoiceResponse as MoneyInCompanyInvoiceResponse

class CompanyConverterTest {
    private val request = CreateCompanyRequest(
        name = "Acme",
        legalName = "Acme LTDA",
        cnpj = "01234567890001-01",
        email = "<EMAIL>",
        phoneNumber = "(11)*********",
        addressPostalCode = "12345-123",
        addressStreet = "Acme street",
        addressNumber = 500,
        addressCity = "Sao Paulo",
        addressState = "SP",
    )

    @Test
    fun `#toCompanyInfoResponse should convert expected`() {
        val product = TestModelFactory.buildProduct()
        val company =
            TestModelFactory.buildCompany(availableProducts = listOf(product.id), defaultProductId = product.id)

        val expected = CompanyInfoResponse(
            id = company.id,
            name = company.name,
            legalName = company.legalName,
            cnpj = company.cnpj,
            email = company.email,
            phoneNumber = company.phoneNumber,
            address = company.address,
            billingAccountablePartyId = company.billingAccountablePartyId,
            availableProducts = listOf(ProductConverter.convert(product)),
            defaultProductId = ProductConverter.convert(product),
            status = company.status,
        )

        val result = company.toCompanyInfoResponse(listOf(product)).success()

        assertThat(result).isSuccessWithData(expected)
    }

    @Test
    fun `#toCompanyInvoiceResponseV2 should convert expected`() {
        val company = TestModelFactory.buildCompany()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val invoiceId = RangeUUID.generate()
        val billingAccountablePartyId = billingAccountableParty.id
        val referenceDate = LocalDate.now()
        val companySubContractId = RangeUUID.generate()
        val companyId = company.id
        val invoicePaymentId = RangeUUID.generate()

        val companyInvoiceResponse = listOf(
            MoneyInCompanyInvoiceResponse(
                id = invoiceId,
                externalId = null,
                status = CompanyInvoiceStatus.PROCESSING,
                billingAccountablePartyId = billingAccountablePartyId,
                referenceDate = referenceDate,
                multipleValidityPeriods = listOf(
                    CompanyInvoiceValidityPeriod(
                        start = referenceDate.atBeginningOfTheMonth(),
                        end = referenceDate.atEndOfTheMonth(),
                    )
                ),
                validityPeriod = CompanyInvoiceValidityPeriod(
                    start = referenceDate.atBeginningOfTheMonth(),
                    end = referenceDate.atEndOfTheMonth()
                ),
                companySubContractIds = listOf(companySubContractId),
                companyIds = listOf(companyId),
                type = CompanyInvoiceType.RECURRENT,
                model = CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
                totalAmount = 100.money,
                discount = 0.money,
                addition = 0.money,
                dueDate = LocalDate.now().plusDays(5),
                quantityMemberInvoices = 1,
                memberInvoiceIds = emptyList(),
                relationships = emptyList(),
                globalItems = emptyList(),
                payments = listOf(
                    MoneyInCompanyInvoicePayment(
                        id = invoicePaymentId,
                        amount = 100.money,
                        status = CompanyInvoicePaymentStatus.APPROVED,
                        approvedAt = LocalDateTime.now(),
                        createdAt = LocalDateTime.now(),
                        method = MoneyInCompanyInvoicePaymentMethod.BOLETO,
                        externalId = "123456",
                        reason = null,
                        pix = null,
                        bankSlip = BankSlipDetail(
                            paymentUrl = "http://url",
                            dueDate = LocalDateTime.now().plusDays(5),
                            barcode = "123456",
                            externalId = "123456",
                        ),
                        creditCard = null,
                        invoiceId = RangeUUID.generate(),
                        updatedAt = LocalDateTime.now(),
                    )
                ),
                installments = 0,
                totalInstallments = 0,
                version = 1,
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now(),
            )
        )
        val invoicePaymentsWithPortalUrl = listOf(
            InvoicePaymentWithPortalUrl(
                invoicePaymentId,
                "portalUrl"
            )
        )

        val expected = CompanyInvoiceResponseV2(
            id = company.id,
            name = company.name,
            legalName = company.legalName,
            cnpj = company.cnpj,
            email = company.email,
            phoneNumber = company.phoneNumber,
            address = company.address,
            billingAccountableParty = CompanyBillingAccountableParty(
                id = billingAccountableParty.id,
                firstName = billingAccountableParty.firstName,
                lastName = billingAccountableParty.lastName,
                type = billingAccountableParty.type,
                nationalId = billingAccountableParty.nationalId,
                email = billingAccountableParty.email,
            ),
            invoices = companyInvoiceResponse.map {
                CompanyInvoiceInfo(
                    id = it.id,
                    externalId = it.externalId ?: "-",
                    billingAccountablePartyId = it.billingAccountablePartyId,
                    referenceDate = it.referenceDate,
                    dueDate = it.dueDate,
                    status = it.status,
                    type = it.type,
                    model = it.model,
                    payments = it.payments.map { payment ->
                        CompanyInvoicePayment(
                            id = payment.id,
                            totalAmount = payment.amount,
                            status = payment.status,
                            invoiceId = it.id,
                            invoiceModel = it.model,
                            externalId = payment.externalId,
                            approvedAt = payment.approvedAt,
                            createdAt = payment.createdAt,
                            method = payment.method.convertTo(CompanyInvoicePaymentMethod::class),
                            portalUrl = "portalUrl",
                            pix = payment.pix,
                            bankSlip = payment.bankSlip,
                            creditCard = payment.creditCard,
                        )
                    }
                )
            }
        )

        val result = company.toCompanyInvoiceResponseV2(
            billingAccountableParty,
            companyInvoiceResponse,
            invoicePaymentsWithPortalUrl
        ).success()

        assertThat(result).isSuccessWithData(expected)
    }


    @Test
    fun `#CreateCompanyRequest_toBillingAccountableParty - convert correctly`() {
        val result = request.toBillingAccountableParty()
        val expectedResult = BillingAccountableParty(
            firstName = request.name,
            lastName = request.legalName,
            type = BillingAccountablePartyType.LEGAL_PERSON,
            nationalId = request.cnpj,
            email = request.email,
            address = Address(
                state = State.SP,
                city = request.addressCity,
                street = request.addressStreet,
                number = request.addressNumber.toString(),
                complement = request.addressComplement,
                postalCode = request.addressPostalCode
            )
        )

        assertThat(result)
            .usingRecursiveComparison()
            .ignoringFields("id", "createdAt", "updatedAt")
            .isEqualTo(expectedResult)
    }

    @Test
    fun `#CreateCompanyRequest_toCompany - convert correctly`() {
        val result = request.toCompany()

        val expectedAddress = CompanyAddress(
            State = request.addressState,
            city = request.addressCity,
            street = request.addressStreet,
            number = request.addressNumber,
            complement = request.addressComplement,
            postalCode = request.addressPostalCode
        )

        val expectedResult = Company(
            parentId = request.parentId,
            externalCode = request.externalCode,
            name = request.name,
            legalName = request.legalName,
            cnpj = request.cnpj,
            email = request.email,
            phoneNumber = request.phoneNumber,
            address = expectedAddress,
        )

        assertThat(result)
            .usingRecursiveComparison()
            .ignoringFields("id", "createdAt", "updatedAt")
            .isEqualTo(expectedResult)
    }

}
