package br.com.alice.api.healthcareops.controllers.business

import br.com.alice.api.healthcareops.api.RoutesTestHelper
import br.com.alice.api.healthcareops.models.BillingAccountablePartyRequest
import br.com.alice.api.healthcareops.models.BillingAccountablePartyResponse
import br.com.alice.api.healthcareops.models.CompanyBillingAccountableParty
import br.com.alice.api.healthcareops.models.CompanyInvoiceResponse
import br.com.alice.api.healthcareops.models.CompanyMemberInvoiceGroupInfo
import br.com.alice.api.healthcareops.models.CompanyMemberInvoiceGroupPayment
import br.com.alice.api.healthcareops.models.CompanyProductPriceListingRequest
import br.com.alice.api.healthcareops.models.CompanySubContractRequest
import br.com.alice.api.healthcareops.models.CompanySubContractResponse
import br.com.alice.api.healthcareops.usecases.GetCompanyInvoicesUseCase
import br.com.alice.business.client.CompanyProductPriceListingService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.RangeUUID
import br.com.alice.common.convertTo
import br.com.alice.common.core.exceptions.RequiredFieldException
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BillingAccountablePartyType
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.product.client.PriceListingService
import br.com.alice.product.client.ProductService
import io.mockk.coEvery
import io.mockk.mockk
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.LocalDate
import kotlin.test.BeforeTest
import kotlin.test.Ignore

class CompanySubContractControllerTest : RoutesTestHelper() {
    private val companySubContractService: CompanySubContractService = mockk()
    private val billingAccountablePartyService: BillingAccountablePartyService = mockk()
    private val companyProductPriceListingService: CompanyProductPriceListingService = mockk()
    private val getCompanyInvoicesUseCase: GetCompanyInvoicesUseCase = mockk()
    private val productService: ProductService = mockk()
    private val priceListingService: PriceListingService = mockk()

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single {
            CompanySubContractController(
                companySubContractService,
                billingAccountablePartyService,
                companyProductPriceListingService,
                getCompanyInvoicesUseCase,
                productService,
            )
        }
    }

    @Nested
    inner class CreateCompanySubContract {
        private val contractId = RangeUUID.generate()
        private val companyId = RangeUUID.generate()
        private val defaultProductId = RangeUUID.generate()
        private val availableProducts = listOf(defaultProductId)

        val request = CompanySubContractRequest(
            title = "sub contrato abc",
            defaultProductId = defaultProductId,
            availableProducts = availableProducts,
        )

        private val companyContract = TestModelFactory.buildCompanyContract(
            id = contractId,
            startedAt = LocalDate.now()
        )
        private val companySubContract = TestModelFactory.buildCompanySubContract(
            externalId = null,
            defaultProductId = defaultProductId,
            availableProducts = availableProducts,
            nature = "20009",
            billingGroup = "0004",
            contractId = contractId
        )

        @Test
        fun `should create a CompanySubContract`() {
            coEvery {
                companySubContractService.add(match {
                    it.title == request.title
                })
            } returns companySubContract

            val expected =
                CompanySubContractResponse(
                    id = companySubContract.id,
                    companyId = companySubContract.companyId,
                    contractId = companySubContract.contractId,
                    title = companySubContract.title,
                    billingAccountablePartyId = companySubContract.billingAccountablePartyId,
                    isProRata = companySubContract.isProRata,
                    flexBenefit = companySubContract.flexBenefit,
                    hasEmployeesAbroad = companySubContract.hasEmployeesAbroad,
                    defaultFlowType = companySubContract.defaultFlowType,
                    defaultProductId = companySubContract.defaultProductId,
                    availableProducts = companySubContract.availableProducts,
                    dueDate = companySubContract.dueDate,
                    isBillingLevel = companySubContract.isBillingLevel,
                    paymentType = companySubContract.paymentType,
                    nature = companySubContract.nature,
                    billingGroup = companySubContract.billingGroup,
                    availableCompanyProductPriceListing = emptyList()
                )

            authenticatedAs(token, staffToBeAuthenticated) {
                post("/company/$companyId/contract/$contractId/subcontract", request) { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()

                    val content: CompanySubContractResponse = response.bodyAsJson()
                    Assertions.assertThat(content).isEqualTo(expected)
                }
            }
        }

        @Test
        fun `should create a CompanySubContract with companyProductPriceListing`() {
            val product1 = TestModelFactory.buildProduct(ansNumber = "123")
            val product2 = TestModelFactory.buildProduct(ansNumber = "1234")
            val companyProductPriceListing1 =
                TestModelFactory.buildCompanyProductPriceListing(productId = product1.id, ansNumber = "123")
            val companyProductPriceListing2 =
                TestModelFactory.buildCompanyProductPriceListing(productId = product2.id, ansNumber = "1234")
            val request = request.copy(
                companyProductPriceListing = listOf(
                    CompanyProductPriceListingRequest(
                        productId = companyProductPriceListing1.productId,
                        ansNumber = product1.ansNumber!!
                    ),
                    CompanyProductPriceListingRequest(
                        productId = companyProductPriceListing2.productId,
                        ansNumber = product2.ansNumber!!
                    ),
                )
            )
            val updatedSubContract = companySubContract.copy(
                avaliableCompanyProductPriceListing = listOf(
                    companyProductPriceListing1.id, companyProductPriceListing2.id
                )
            )
            coEvery {
                companySubContractService.add(match { it.title == request.title })
            } returns companySubContract
            coEvery { productService.findByIds(listOf(product1.id, product2.id)) } returns listOf(product1, product2)
            coEvery {
                companyProductPriceListingService.addListToSubContract(match {
                            it[0].productId == companyProductPriceListing1.productId
                            && it[1].productId == companyProductPriceListing2.productId
                }, companySubContract)
            } returns listOf(companyProductPriceListing1, companyProductPriceListing2)

            coEvery {
                companySubContractService.update(any())
            } returns updatedSubContract


            val expected = CompanySubContractResponse(
                id = companySubContract.id,
                companyId = companySubContract.companyId,
                contractId = companySubContract.contractId,
                title = companySubContract.title,
                billingAccountablePartyId = companySubContract.billingAccountablePartyId,
                isProRata = companySubContract.isProRata,
                flexBenefit = companySubContract.flexBenefit,
                hasEmployeesAbroad = companySubContract.hasEmployeesAbroad,
                defaultFlowType = companySubContract.defaultFlowType,
                defaultProductId = companySubContract.defaultProductId,
                availableProducts = companySubContract.availableProducts,
                dueDate = companySubContract.dueDate,
                isBillingLevel = companySubContract.isBillingLevel,
                paymentType = companySubContract.paymentType,
                nature = companySubContract.nature,
                billingGroup = companySubContract.billingGroup,
                availableCompanyProductPriceListing = updatedSubContract.avaliableCompanyProductPriceListing,
            )

            authenticatedAs(token, staffToBeAuthenticated) {
                post("/company/$companyId/contract/$contractId/subcontract", request) { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()

                    val content: CompanySubContractResponse = response.bodyAsJson()
                    Assertions.assertThat(content).isEqualTo(expected)
                }
            }
        }

        @Test
        fun `should create a CompanySubContract and create the billing accountable party`() {
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

            val companySubContract = companySubContract.copy(billingAccountablePartyId = billingAccountableParty.id)

            val request = CompanySubContractRequest(
                title = "sub contrato abc",
                defaultProductId = defaultProductId,
                availableProducts = availableProducts,
                billingAccountableParty = BillingAccountablePartyRequest(
                    nationalId = "00000",
                    type = BillingAccountablePartyType.LEGAL_PERSON,
                    address = billingAccountableParty.address!!,
                    email = "<EMAIL>",
                    firstName = "Company",
                    lastName = "Company"
                ),
                nature = "20009",
                billingGroup = "0004",
            )

            coEvery {
                companySubContractService.add(match {
                    it.title == request.title
                })
            } returns companySubContract
            coEvery { billingAccountablePartyService.add(match { it.nationalId == request.billingAccountableParty?.nationalId }) } returns billingAccountableParty

            val expected =
                CompanySubContractResponse(
                    id = companySubContract.id,
                    companyId = companySubContract.companyId,
                    contractId = companySubContract.contractId,
                    title = companySubContract.title,
                    billingAccountablePartyId = billingAccountableParty.id,
                    isProRata = companySubContract.isProRata,
                    flexBenefit = companySubContract.flexBenefit,
                    hasEmployeesAbroad = companySubContract.hasEmployeesAbroad,
                    defaultFlowType = companySubContract.defaultFlowType,
                    defaultProductId = companySubContract.defaultProductId,
                    availableProducts = companySubContract.availableProducts,
                    dueDate = companySubContract.dueDate,
                    isBillingLevel = companySubContract.isBillingLevel,
                    paymentType = companySubContract.paymentType,
                    billingAccountableParty = billingAccountableParty.convertTo(BillingAccountablePartyResponse::class),
                    nature = companySubContract.nature,
                    billingGroup = companySubContract.billingGroup,
                    availableCompanyProductPriceListing = emptyList()
                )

            authenticatedAs(token, staffToBeAuthenticated) {
                post("/company/$companyId/contract/$contractId/subcontract", request) { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()

                    val content: CompanySubContractResponse = response.bodyAsJson()
                    Assertions.assertThat(content).isEqualTo(expected)
                }
            }
        }

        @Test
        fun `should create a CompanySubContract and get the billing accountable party`() {
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

            val companySubContract = companySubContract.copy(billingAccountablePartyId = billingAccountableParty.id)

            val request = CompanySubContractRequest(
                title = "sub contrato abc",
                defaultProductId = defaultProductId,
                availableProducts = availableProducts,
                billingAccountablePartyId = billingAccountableParty.id,
            )

            coEvery {
                companySubContractService.add(match {
                    it.title == request.title
                })
            } returns companySubContract
            coEvery { billingAccountablePartyService.get(billingAccountableParty.id) } returns billingAccountableParty

            val expected =
                CompanySubContractResponse(
                    id = companySubContract.id,
                    companyId = companySubContract.companyId,
                    contractId = companySubContract.contractId,
                    title = companySubContract.title,
                    billingAccountablePartyId = billingAccountableParty.id,
                    isProRata = companySubContract.isProRata,
                    flexBenefit = companySubContract.flexBenefit,
                    hasEmployeesAbroad = companySubContract.hasEmployeesAbroad,
                    defaultFlowType = companySubContract.defaultFlowType,
                    defaultProductId = companySubContract.defaultProductId,
                    availableProducts = companySubContract.availableProducts,
                    dueDate = companySubContract.dueDate,
                    isBillingLevel = companySubContract.isBillingLevel,
                    paymentType = companySubContract.paymentType,
                    billingAccountableParty = billingAccountableParty.convertTo(BillingAccountablePartyResponse::class),
                    nature = companySubContract.nature,
                    billingGroup = companySubContract.billingGroup,
                    availableCompanyProductPriceListing = emptyList()
                )

            authenticatedAs(token, staffToBeAuthenticated) {
                post("/company/$companyId/contract/$contractId/subcontract", request) { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()

                    val content: CompanySubContractResponse = response.bodyAsJson()
                    Assertions.assertThat(content).isEqualTo(expected)
                }
            }
        }

        @Test
        fun `should throw exception when title is empty`() {
            val request = request.copy(title = "")
            val expected = RequiredFieldException("title")

            authenticatedAs(token, staffToBeAuthenticated) {
                post("/company/$companyId/contract/$contractId/subcontract", request) { response ->
                    ResponseAssert.assertThat(response).withErrorCode(expected.code)
                    ResponseAssert.assertThat(response).withFailMessage(expected.message)
                }
            }
        }

        @Test
        fun `should throw exception when company sub contract service returns an exception`() {
            coEvery {
                companySubContractService.add(match {
                    it.title == request.title
                })
            } returns Exception("Some error")


            authenticatedAs(token, staffToBeAuthenticated) {
                post("/company/$companyId/contract/$contractId/subcontract", request) { response ->
                    ResponseAssert.assertThat(response).isInternalServerError()
                }
            }
        }
    }

    @Nested
    inner class UpdateCompanySubContract {
        private val defaultProductId = RangeUUID.generate()
        private val availableProducts = listOf(defaultProductId)

        val request = CompanySubContractRequest(
            title = "New sub contract title",
            defaultProductId = defaultProductId,
            availableProducts = availableProducts,
        )

        private val companyContract = TestModelFactory.buildCompanyContract(
            startedAt = LocalDate.now()
        )

        private val companySubContract = TestModelFactory.buildCompanySubContract(
            defaultProductId = defaultProductId,
            availableProducts = availableProducts,
            contractId = companyContract.id
        )

        @Test
        fun `should update a CompanySubContract`() {
            coEvery { companySubContractService.get(companySubContract.id) } returns companySubContract
            coEvery {
                companySubContractService.update(match {
                    it.id == companySubContract.id &&
                            it.title == request.title
                })
            } returns companySubContract.copy(title = "New sub contract title")

            val expected =
                CompanySubContractResponse(
                    id = companySubContract.id,
                    companyId = companySubContract.companyId,
                    contractId = companySubContract.contractId,
                    externalId = companySubContract.externalId,
                    title = "New sub contract title",
                    billingAccountablePartyId = companySubContract.billingAccountablePartyId,
                    isProRata = companySubContract.isProRata,
                    flexBenefit = companySubContract.flexBenefit,
                    hasEmployeesAbroad = companySubContract.hasEmployeesAbroad,
                    defaultFlowType = companySubContract.defaultFlowType,
                    defaultProductId = companySubContract.defaultProductId,
                    availableProducts = companySubContract.availableProducts,
                    dueDate = companySubContract.dueDate,
                    isBillingLevel = companySubContract.isBillingLevel,
                    paymentType = companySubContract.paymentType,
                    nature = companySubContract.nature,
                    billingGroup = companySubContract.billingGroup,
                )

            authenticatedAs(token, staffToBeAuthenticated) {
                put("/company/subcontract/${companySubContract.id}", request) { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()

                    val content: CompanySubContractResponse = response.bodyAsJson()
                    Assertions.assertThat(content).isEqualTo(expected)
                }
            }
        }

        @Ignore
        @Test
        fun `should update a CompanySubContract with companyProductPriceListing`() {
            val product1 = TestModelFactory.buildProduct(ansNumber = "123")
            val product2 = TestModelFactory.buildProduct(ansNumber = "1234")
            val priceListing1 = TestModelFactory.buildPriceListing()
            val priceListing2 = TestModelFactory.buildPriceListing()
            val companyProductPriceListing1 =
                TestModelFactory.buildCompanyProductPriceListing(productId = product1.id, ansNumber = "123", priceListingId = priceListing1.id)
            val companyProductPriceListing2 =
                TestModelFactory.buildCompanyProductPriceListing(productId = product2.id, ansNumber = "1234", priceListingId = priceListing2.id)

            val request = request.copy(
                companyProductPriceListing = listOf(
                    CompanyProductPriceListingRequest(
                        productId = companyProductPriceListing1.productId,
                        ansNumber = product1.ansNumber!!
                    ),
                    CompanyProductPriceListingRequest(
                        productId = companyProductPriceListing2.productId,
                        ansNumber = product2.ansNumber!!
                    ),
                )
            )

            val updatedSubContract = companySubContract.copy(
                avaliableCompanyProductPriceListing = listOf(
                    companyProductPriceListing1.id, companyProductPriceListing2.id
                ),
                title = "New sub contract title"
            )
            coEvery { productService.findByIds(listOf(product1.id, product2.id)) } returns listOf(product1, product2)
            coEvery { companySubContractService.get(companySubContract.id) } returns companySubContract
//            coEvery {
//                companyProductPriceListingService.add(match {
//                    it.priceListingId == companyProductPriceListing1.priceListingId
//                            && it.productId == companyProductPriceListing1.productId
//                }, companySubContract)
//            } returns companyProductPriceListing1
//            coEvery {
//                companyProductPriceListingService.add(match {
//                    it.priceListingId == companyProductPriceListing2.priceListingId
//                            && it.productId == companyProductPriceListing2.productId
//                }, companySubContract)
//            } returns companyProductPriceListing2

            coEvery {
                companyProductPriceListingService.addListToSubContract(match {
                            it[0].productId == companyProductPriceListing1.productId
                            && it[1].productId == companyProductPriceListing2.productId
                }, companySubContract)
            } returns listOf(companyProductPriceListing1, companyProductPriceListing2)
            coEvery {
                companySubContractService.update(match {
                    it.id == companySubContract.id &&
                            it.title == request.title
                })
            } returns updatedSubContract

            coEvery { priceListingService.getList(any()) } returns listOf(priceListing1, priceListing2)

            val expected =
                CompanySubContractResponse(
                    id = companySubContract.id,
                    companyId = companySubContract.companyId,
                    contractId = companySubContract.contractId,
                    externalId = companySubContract.externalId,
                    title = "New sub contract title",
                    billingAccountablePartyId = companySubContract.billingAccountablePartyId,
                    isProRata = companySubContract.isProRata,
                    flexBenefit = companySubContract.flexBenefit,
                    hasEmployeesAbroad = companySubContract.hasEmployeesAbroad,
                    defaultFlowType = companySubContract.defaultFlowType,
                    defaultProductId = companySubContract.defaultProductId,
                    availableProducts = companySubContract.availableProducts,
                    dueDate = companySubContract.dueDate,
                    isBillingLevel = companySubContract.isBillingLevel,
                    paymentType = companySubContract.paymentType,
                    nature = companySubContract.nature,
                    billingGroup = companySubContract.billingGroup,
                    availableCompanyProductPriceListing = updatedSubContract.avaliableCompanyProductPriceListing,
                )

            authenticatedAs(token, staffToBeAuthenticated) {
                put("/company/subcontract/${companySubContract.id}", request) { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()

                    val content: CompanySubContractResponse = response.bodyAsJson()
                    Assertions.assertThat(content).isEqualTo(expected)
                }
            }
        }

        @Test
        fun `should update a CompanySubContract and create the billing accountable party`() {
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

            val companySubContract = companySubContract.copy(billingAccountablePartyId = billingAccountableParty.id)

            val request = request.copy(
                billingAccountableParty = BillingAccountablePartyRequest(
                    nationalId = "00000",
                    type = BillingAccountablePartyType.LEGAL_PERSON,
                    address = billingAccountableParty.address!!,
                    email = "<EMAIL>",
                    firstName = "Company",
                    lastName = "Company"
                )
            )
            coEvery { companySubContractService.get(companySubContract.id) } returns companySubContract
            coEvery {
                companySubContractService.update(match {
                    it.id == companySubContract.id &&
                            it.title == request.title
                })
            } returns companySubContract.copy(
                title = "New sub contract title",
                billingAccountablePartyId = billingAccountableParty.id
            )

            coEvery {
                billingAccountablePartyService.add(match {
                    it.nationalId == request.billingAccountableParty?.nationalId && it.type == BillingAccountablePartyType.LEGAL_PERSON
                })
            } returns billingAccountableParty

            val expected =
                CompanySubContractResponse(
                    id = companySubContract.id,
                    companyId = companySubContract.companyId,
                    contractId = companySubContract.contractId,
                    externalId = companySubContract.externalId,
                    title = "New sub contract title",
                    billingAccountablePartyId = billingAccountableParty.id,
                    isProRata = companySubContract.isProRata,
                    flexBenefit = companySubContract.flexBenefit,
                    hasEmployeesAbroad = companySubContract.hasEmployeesAbroad,
                    defaultFlowType = companySubContract.defaultFlowType,
                    defaultProductId = companySubContract.defaultProductId,
                    availableProducts = companySubContract.availableProducts,
                    dueDate = companySubContract.dueDate,
                    isBillingLevel = companySubContract.isBillingLevel,
                    paymentType = companySubContract.paymentType,
                    billingAccountableParty = billingAccountableParty.convertTo(BillingAccountablePartyResponse::class),
                    nature = companySubContract.nature,
                    billingGroup = companySubContract.billingGroup,
                )

            authenticatedAs(token, staffToBeAuthenticated) {
                put("/company/subcontract/${companySubContract.id}", request) { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()

                    val content: CompanySubContractResponse = response.bodyAsJson()
                    Assertions.assertThat(content).isEqualTo(expected)
                }
            }
        }

        @Test
        fun `should update a CompanySubContract and get the billing accountable party`() {
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

            val companySubContract = companySubContract.copy(billingAccountablePartyId = billingAccountableParty.id)

            val request = request.copy(
                billingAccountablePartyId = billingAccountableParty.id,
            )
            coEvery { companySubContractService.get(companySubContract.id) } returns companySubContract
            coEvery {
                companySubContractService.update(match {
                    it.id == companySubContract.id &&
                            it.title == request.title
                })
            } returns companySubContract.copy(
                title = "New sub contract title",
                billingAccountablePartyId = billingAccountableParty.id,
            )

            coEvery { billingAccountablePartyService.get(billingAccountableParty.id) } returns billingAccountableParty

            val expected =
                CompanySubContractResponse(
                    id = companySubContract.id,
                    externalId = companySubContract.externalId,
                    companyId = companySubContract.companyId,
                    contractId = companySubContract.contractId,
                    title = "New sub contract title",
                    billingAccountablePartyId = billingAccountableParty.id,
                    isProRata = companySubContract.isProRata,
                    flexBenefit = companySubContract.flexBenefit,
                    hasEmployeesAbroad = companySubContract.hasEmployeesAbroad,
                    defaultFlowType = companySubContract.defaultFlowType,
                    defaultProductId = companySubContract.defaultProductId,
                    availableProducts = companySubContract.availableProducts,
                    dueDate = companySubContract.dueDate,
                    isBillingLevel = companySubContract.isBillingLevel,
                    paymentType = companySubContract.paymentType,
                    billingAccountableParty = billingAccountableParty.convertTo(BillingAccountablePartyResponse::class),
                    nature = companySubContract.nature,
                    billingGroup = companySubContract.billingGroup,
                )

            authenticatedAs(token, staffToBeAuthenticated) {
                put("/company/subcontract/${companySubContract.id}", request) { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()

                    val content: CompanySubContractResponse = response.bodyAsJson()
                    Assertions.assertThat(content).isEqualTo(expected)
                }
            }
        }

        @Test
        fun `should throw exception when title is empty`() {
            val request = request.copy(title = "")
            val expected = RequiredFieldException("title")

            authenticatedAs(token, staffToBeAuthenticated) {
                put("/company/subcontract/${companySubContract.id}", request) { response ->
                    ResponseAssert.assertThat(response).withErrorCode(expected.code)
                    ResponseAssert.assertThat(response).withFailMessage(expected.message)
                }
            }
        }
    }

    @Nested
    inner class ListByCompanyId {
        @Test
        fun `list subcontracts by company id`() {
            val defaultProductId = RangeUUID.generate()
            val availableProducts = listOf(defaultProductId)
            val companyId = RangeUUID.generate()

            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

            val companySubContract = TestModelFactory.buildCompanySubContract(
                companyId = companyId,
                defaultProductId = defaultProductId,
                availableProducts = availableProducts,
                billingAccountablePartyId = billingAccountableParty.id,
            )
            val companyProductPriceListing =
                TestModelFactory.buildCompanyProductPriceListing(companySubContractId = companySubContract.id)

            val expected =
                listOf(
                    CompanySubContractResponse(
                        id = companySubContract.id,
                        companyId = companySubContract.companyId,
                        contractId = companySubContract.contractId,
                        title = companySubContract.title,
                        billingAccountablePartyId = billingAccountableParty.id,
                        isProRata = companySubContract.isProRata,
                        flexBenefit = companySubContract.flexBenefit,
                        hasEmployeesAbroad = companySubContract.hasEmployeesAbroad,
                        defaultFlowType = companySubContract.defaultFlowType,
                        defaultProductId = companySubContract.defaultProductId,
                        availableProducts = companySubContract.availableProducts,
                        dueDate = companySubContract.dueDate,
                        isBillingLevel = companySubContract.isBillingLevel,
                        paymentType = companySubContract.paymentType,
                        externalId = companySubContract.externalId,
                        billingAccountableParty = billingAccountableParty.convertTo(BillingAccountablePartyResponse::class),
                        nature = companySubContract.nature,
                        billingGroup = companySubContract.billingGroup,
                        companyProductPriceListing = listOf(companyProductPriceListing)
                    )
                )

            coEvery {
                companySubContractService.findByCompanyId(companySubContract.companyId)
            } returns listOf(companySubContract)

            coEvery {
                companyProductPriceListingService.findCurrentBySubContractIds(
                    listOf(companySubContract.id),
                    any()
                )
            } returns mapOf(
                companySubContract.id.toString() to listOf(companyProductPriceListing)
            )


            coEvery { billingAccountablePartyService.findById(listOf(billingAccountableParty.id)) } returns listOf(
                billingAccountableParty
            )

            authenticatedAs(token, staffToBeAuthenticated) {
                get("/company/$companyId/subcontract") { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()

                    val content: List<CompanySubContractResponse> = response.bodyAsJson()
                    Assertions.assertThat(content).isEqualTo(expected)
                }
            }
        }

        @Test
        fun `list subcontracts by company id without billing accountable party when it is not associated with it`() {
            val defaultProductId = RangeUUID.generate()
            val availableProducts = listOf(defaultProductId)
            val companyId = RangeUUID.generate()

            val companySubContract = TestModelFactory.buildCompanySubContract(
                companyId = companyId,
                defaultProductId = defaultProductId,
                availableProducts = availableProducts,
                billingAccountablePartyId = null,
            )

            val companyProductPriceListing =
                TestModelFactory.buildCompanyProductPriceListing(companySubContractId = companySubContract.id)

            val expected =
                listOf(
                    CompanySubContractResponse(
                        id = companySubContract.id,
                        companyId = companySubContract.companyId,
                        contractId = companySubContract.contractId,
                        title = companySubContract.title,
                        billingAccountablePartyId = null,
                        isProRata = companySubContract.isProRata,
                        flexBenefit = companySubContract.flexBenefit,
                        hasEmployeesAbroad = companySubContract.hasEmployeesAbroad,
                        defaultFlowType = companySubContract.defaultFlowType,
                        defaultProductId = companySubContract.defaultProductId,
                        availableProducts = companySubContract.availableProducts,
                        dueDate = companySubContract.dueDate,
                        isBillingLevel = companySubContract.isBillingLevel,
                        paymentType = companySubContract.paymentType,
                        externalId = companySubContract.externalId,
                        billingAccountableParty = null,
                        nature = companySubContract.nature,
                        billingGroup = companySubContract.billingGroup,
                        companyProductPriceListing = listOf(companyProductPriceListing)
                    )
                )

            coEvery {
                companySubContractService.findByCompanyId(companySubContract.companyId)
            } returns listOf(companySubContract)

            coEvery {
                companyProductPriceListingService.findCurrentBySubContractIds(
                    listOf(companySubContract.id),
                    any()
                )
            } returns mapOf(
                companySubContract.id.toString() to listOf(companyProductPriceListing)
            )

            authenticatedAs(token, staffToBeAuthenticated) {
                get("/company/$companyId/subcontract") { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()

                    val content: List<CompanySubContractResponse> = response.bodyAsJson()
                    Assertions.assertThat(content).isEqualTo(expected)
                }
            }

            coVerifyNone { billingAccountablePartyService.findById(any()) }
        }
    }

    @Nested
    inner class GetById {
        @Test
        fun `get by id`() {
            val defaultProductId = RangeUUID.generate()
            val availableProducts = listOf(defaultProductId)
            val companyId = RangeUUID.generate()

            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

            val companySubContract = TestModelFactory.buildCompanySubContract(
                companyId = companyId,
                defaultProductId = defaultProductId,
                availableProducts = availableProducts,
                billingAccountablePartyId = billingAccountableParty.id,
            )

            val companyProductPriceListing =
                TestModelFactory.buildCompanyProductPriceListing(companySubContractId = companySubContract.id)

            val expected =
                CompanySubContractResponse(
                    id = companySubContract.id,
                    companyId = companySubContract.companyId,
                    contractId = companySubContract.contractId,
                    title = companySubContract.title,
                    billingAccountablePartyId = billingAccountableParty.id,
                    isProRata = companySubContract.isProRata,
                    flexBenefit = companySubContract.flexBenefit,
                    hasEmployeesAbroad = companySubContract.hasEmployeesAbroad,
                    defaultFlowType = companySubContract.defaultFlowType,
                    defaultProductId = companySubContract.defaultProductId,
                    availableProducts = companySubContract.availableProducts,
                    dueDate = companySubContract.dueDate,
                    isBillingLevel = companySubContract.isBillingLevel,
                    paymentType = companySubContract.paymentType,
                    externalId = companySubContract.externalId,
                    billingAccountableParty = billingAccountableParty.convertTo(BillingAccountablePartyResponse::class),
                    nature = companySubContract.nature,
                    billingGroup = companySubContract.billingGroup,
                    companyProductPriceListing = listOf(companyProductPriceListing)
                )

            coEvery {
                companySubContractService.get(companySubContract.id)
            } returns companySubContract

            coEvery { billingAccountablePartyService.findById(listOf(billingAccountableParty.id)) } returns listOf(
                billingAccountableParty
            )
            coEvery {
                companyProductPriceListingService.findCurrentBySubContractIds(
                    listOf(companySubContract.id),
                    any()
                )
            } returns mapOf(
                companySubContract.id.toString() to listOf(companyProductPriceListing)
            )

            authenticatedAs(token, staffToBeAuthenticated) {
                get("/company/subcontract/${companySubContract.id}") { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()

                    val content: CompanySubContractResponse = response.bodyAsJson()
                    Assertions.assertThat(content).isEqualTo(expected)
                }
            }
        }

        @Test
        fun `get by id without billing accountable party when it is not associated with it`() {
            val defaultProductId = RangeUUID.generate()
            val availableProducts = listOf(defaultProductId)
            val companyId = RangeUUID.generate()

            val companySubContract = TestModelFactory.buildCompanySubContract(
                companyId = companyId,
                defaultProductId = defaultProductId,
                availableProducts = availableProducts,
                billingAccountablePartyId = null,
            )

            val companyProductPriceListing =
                TestModelFactory.buildCompanyProductPriceListing(companySubContractId = companySubContract.id)

            val expected =
                CompanySubContractResponse(
                    id = companySubContract.id,
                    companyId = companySubContract.companyId,
                    contractId = companySubContract.contractId,
                    title = companySubContract.title,
                    billingAccountablePartyId = null,
                    isProRata = companySubContract.isProRata,
                    flexBenefit = companySubContract.flexBenefit,
                    hasEmployeesAbroad = companySubContract.hasEmployeesAbroad,
                    defaultFlowType = companySubContract.defaultFlowType,
                    defaultProductId = companySubContract.defaultProductId,
                    availableProducts = companySubContract.availableProducts,
                    dueDate = companySubContract.dueDate,
                    isBillingLevel = companySubContract.isBillingLevel,
                    paymentType = companySubContract.paymentType,
                    externalId = companySubContract.externalId,
                    billingAccountableParty = null,
                    nature = companySubContract.nature,
                    billingGroup = companySubContract.billingGroup,
                    companyProductPriceListing = listOf(companyProductPriceListing)
                )

            coEvery {
                companySubContractService.get(companySubContract.id)
            } returns companySubContract

            coEvery {
                companyProductPriceListingService.findCurrentBySubContractIds(
                    listOf(companySubContract.id),
                    any()
                )
            } returns mapOf(
                companySubContract.id.toString() to listOf(companyProductPriceListing)
            )


            authenticatedAs(token, staffToBeAuthenticated) {
                get("/company/subcontract/${companySubContract.id}") { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()

                    val content: CompanySubContractResponse = response.bodyAsJson()
                    Assertions.assertThat(content).isEqualTo(expected)
                }
            }

            coVerifyNone { billingAccountablePartyService.findById(any()) }
        }
    }

    @Nested
    inner class ListCompanySubcontractInvoices {

        @Test
        fun `#listCompanySubContractInvoices should list invoices by subContract billing accountable party`() {
            val product = TestModelFactory.buildProduct()
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val company = TestModelFactory.buildCompany(
                availableProducts = listOf(product.id),
                defaultProductId = product.id,
                billingAccountablePartyId = billingAccountableParty.id
            )
            val companySubContract = TestModelFactory.buildCompanySubContract(
                companyId = company.id,
                defaultProductId = product.id,
                billingAccountablePartyId = billingAccountableParty.id,
            )

            val memberInvoiceGroup1 =
                TestModelFactory.buildMemberInvoiceGroup(billingAccountablePartyId = company.billingAccountablePartyId!!)
            val memberInvoiceGroup2 =
                TestModelFactory.buildMemberInvoiceGroup(billingAccountablePartyId = company.billingAccountablePartyId!!)

            val invoicePayment1 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
            val invoicePayment2 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
            val invoicePayment3 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup2.id)

            val expectedResponse = CompanyInvoiceResponse(
                id = company.id,
                name = company.name,
                legalName = company.legalName,
                cnpj = company.cnpj,
                email = company.email,
                phoneNumber = company.phoneNumber,
                address = company.address,
                billingAccountableParty = CompanyBillingAccountableParty(
                    id = billingAccountableParty.id,
                    firstName = billingAccountableParty.firstName,
                    lastName = billingAccountableParty.lastName,
                    type = billingAccountableParty.type,
                    nationalId = billingAccountableParty.nationalId,
                    email = billingAccountableParty.email,
                ),
                memberInvoiceGroup = listOf(
                    CompanyMemberInvoiceGroupInfo(
                        id = memberInvoiceGroup1.id,
                        externalId = memberInvoiceGroup1.externalId!!,
                        billingAccountablePartyId = memberInvoiceGroup1.billingAccountablePartyId,
                        referenceDate = memberInvoiceGroup1.referenceDate,
                        dueDate = memberInvoiceGroup1.dueDate,
                        status = memberInvoiceGroup1.status,
                        payments = listOf(
                            CompanyMemberInvoiceGroupPayment(
                                id = invoicePayment1.id,
                                totalAmount = invoicePayment1.amount,
                                status = invoicePayment1.status,
                                invoiceGroupId = invoicePayment1.invoiceGroupId,
                                paymentDetail = invoicePayment1.paymentDetail,
                                createdAt = invoicePayment1.createdAt,
                                canceledReason = invoicePayment1.canceledReason,
                                approvedAt = invoicePayment1.approvedAt,
                                reason = invoicePayment1.reason,
                                method = invoicePayment1.method,
                                externalId = invoicePayment1.externalId
                            ),
                            CompanyMemberInvoiceGroupPayment(
                                id = invoicePayment2.id,
                                totalAmount = invoicePayment2.amount,
                                status = invoicePayment2.status,
                                invoiceGroupId = invoicePayment2.invoiceGroupId,
                                paymentDetail = invoicePayment2.paymentDetail,
                                createdAt = invoicePayment2.createdAt,
                                canceledReason = invoicePayment2.canceledReason,
                                approvedAt = invoicePayment2.approvedAt,
                                reason = invoicePayment2.reason,
                                method = invoicePayment2.method,
                                externalId = invoicePayment2.externalId
                            ),
                        )
                    ),
                    CompanyMemberInvoiceGroupInfo(
                        id = memberInvoiceGroup2.id,
                        externalId = memberInvoiceGroup2.externalId!!,
                        billingAccountablePartyId = memberInvoiceGroup2.billingAccountablePartyId,
                        referenceDate = memberInvoiceGroup2.referenceDate,
                        dueDate = memberInvoiceGroup2.dueDate,
                        status = memberInvoiceGroup2.status,
                        payments = listOf(
                            CompanyMemberInvoiceGroupPayment(
                                id = invoicePayment3.id,
                                totalAmount = invoicePayment3.amount,
                                status = invoicePayment3.status,
                                invoiceGroupId = invoicePayment3.invoiceGroupId,
                                paymentDetail = invoicePayment3.paymentDetail,
                                createdAt = invoicePayment3.createdAt,
                                canceledReason = invoicePayment3.canceledReason,
                                approvedAt = invoicePayment3.approvedAt,
                                reason = invoicePayment3.reason,
                                method = invoicePayment3.method,
                                externalId = invoicePayment3.externalId
                            ),
                        )
                    )
                )
            )

            coEvery {
                getCompanyInvoicesUseCase.getBySubcontract(companySubContract.id)
            } returns expectedResponse

            authenticatedAs(token, staffToBeAuthenticated) {
                get("/company/subcontract/${companySubContract.id}/invoice_info") { response ->
                    ResponseAssert.assertThat(response).isSuccessfulJson()

                    val content: CompanyInvoiceResponse = response.bodyAsJson()
                    Assertions.assertThat(content).isEqualTo(expectedResponse)
                }
            }
        }
    }

    @Test
    fun `#deleteCompanyProductPriceListingBySubcontractId should work`() {
        val subContract = TestModelFactory.buildCompanySubContract()

        coEvery {
            companyProductPriceListingService.clearFromSubContract(subContract.id)
        } returns listOf(true)

        authenticatedAs(token, staffToBeAuthenticated) {
            delete("/company/${subContract.companyId}/subcontract/${subContract.id}/company_product_price_listing") { response ->
                ResponseAssert.assertThat(response).isOKWithData(listOf(true))
            }
        }

        coVerifyOnce {
            companyProductPriceListingService.clearFromSubContract(any())
        }
    }

    @Test
    fun `#deleteCompanyProductPriceListingById should work`() {
        val subContract = TestModelFactory.buildCompanySubContract()
        val cppl = TestModelFactory.buildCompanyProductPriceListing()

        coEvery {
            companyProductPriceListingService.get(cppl.id)
        } returns cppl

        coEvery {
            companyProductPriceListingService.delete(cppl)
        } returns true

        authenticatedAs(token, staffToBeAuthenticated) {
            delete("/company/${subContract.companyId}/subcontract/${subContract.id}/company_product_price_listing/${cppl.id}") { response ->
                ResponseAssert.assertThat(response).isOKWithData(true)
            }
        }

        coVerifyOnce {
            companyProductPriceListingService.get(any())
            companyProductPriceListingService.delete(any())
        }

    }

}
