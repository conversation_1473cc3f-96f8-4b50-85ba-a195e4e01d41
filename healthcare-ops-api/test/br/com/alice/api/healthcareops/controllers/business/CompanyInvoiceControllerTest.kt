package br.com.alice.api.healthcareops.controllers.business

import br.com.alice.api.healthcareops.api.RoutesTestHelper
import br.com.alice.api.healthcareops.models.CompanyBillingAccountableParty
import br.com.alice.api.healthcareops.models.CompanyInvoiceResponse
import br.com.alice.api.healthcareops.models.CompanyMemberInvoiceGroupInfo
import br.com.alice.api.healthcareops.models.CompanyMemberInvoiceGroupPayment
import br.com.alice.api.healthcareops.models.CreateFirstInvoicePaymentRequest
import br.com.alice.api.healthcareops.models.CreateInvoicePaymentRequest
import br.com.alice.api.healthcareops.usecases.CancelInvoicePayload
import br.com.alice.api.healthcareops.usecases.CreateInvoicePaymentPaylod
import br.com.alice.api.healthcareops.usecases.GetCompanyInvoicesUseCase
import br.com.alice.api.healthcareops.usecases.PreActivationCompanyInvoiceUseCase
import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.PaymentMethod
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CancellationReason
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.MemberInvoiceGroup
import br.com.alice.moneyin.client.CancelInvoiceResponse
import br.com.alice.moneyin.client.CreateInvoicePaymentResponse
import br.com.alice.moneyin.client.CreateInvoiceResponse
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.PreActivationCompanyInvoiceType
import io.mockk.coEvery
import io.mockk.mockk
import org.assertj.core.api.Assertions
import java.time.LocalDate
import kotlin.test.BeforeTest
import kotlin.test.Test

class CompanyInvoiceControllerTest : RoutesTestHelper() {
    private val invoicePaymentService: InvoicePaymentService = mockk()
    private val companyContractService: CompanyContractService = mockk()
    private val companySubcontractService: CompanySubContractService = mockk()
    private val getCompanyInvoicesUseCase: GetCompanyInvoicesUseCase = mockk()
    private val preActivationCompanyInvoiceUseCase: PreActivationCompanyInvoiceUseCase = mockk()

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single {
            CompanyInvoiceController(
                invoicePaymentService,
                getCompanyInvoicesUseCase,
                preActivationCompanyInvoiceUseCase,
                companySubcontractService,
                companyContractService,
            )
        }
    }

    @Test
    fun `#getInvoiceInfoByCompanyId - should return CompanyInvoiceResponse by BillingAccountableParty`() {

        val product = TestModelFactory.buildProduct()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val company = TestModelFactory.buildCompany(
            availableProducts = listOf(product.id),
            defaultProductId = product.id,
            billingAccountablePartyId = billingAccountableParty.id
        )

        val memberInvoiceGroup1 =
            TestModelFactory.buildMemberInvoiceGroup(billingAccountablePartyId = company.billingAccountablePartyId!!)
        val memberInvoiceGroup2 =
            TestModelFactory.buildMemberInvoiceGroup(billingAccountablePartyId = company.billingAccountablePartyId!!)

        val invoicePayment1 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
        val invoicePayment2 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
        val invoicePayment3 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup2.id)

        val expected = CompanyInvoiceResponse(
            id = company.id,
            name = company.name,
            legalName = company.legalName,
            cnpj = company.cnpj,
            email = company.email,
            phoneNumber = company.phoneNumber,
            address = company.address,
            billingAccountableParty = CompanyBillingAccountableParty(
                id = billingAccountableParty.id,
                firstName = billingAccountableParty.firstName,
                lastName = billingAccountableParty.lastName,
                type = billingAccountableParty.type,
                nationalId = billingAccountableParty.nationalId,
                email = billingAccountableParty.email,
            ),
            memberInvoiceGroup = listOf(
                CompanyMemberInvoiceGroupInfo(
                    id = memberInvoiceGroup1.id,
                    externalId = memberInvoiceGroup1.externalId!!,
                    billingAccountablePartyId = memberInvoiceGroup1.billingAccountablePartyId,
                    referenceDate = memberInvoiceGroup1.referenceDate,
                    dueDate = memberInvoiceGroup1.dueDate,
                    status = memberInvoiceGroup1.status,
                    payments = listOf(
                        CompanyMemberInvoiceGroupPayment(
                            id = invoicePayment1.id,
                            totalAmount = invoicePayment1.amount,
                            status = invoicePayment1.status,
                            invoiceGroupId = invoicePayment1.invoiceGroupId,
                            paymentDetail = invoicePayment1.paymentDetail,
                            createdAt = invoicePayment1.createdAt,
                            canceledReason = invoicePayment1.canceledReason,
                            approvedAt = invoicePayment1.approvedAt,
                            reason = invoicePayment1.reason,
                            method = invoicePayment1.method,
                            externalId = invoicePayment1.externalId
                        ),
                        CompanyMemberInvoiceGroupPayment(
                            id = invoicePayment2.id,
                            totalAmount = invoicePayment2.amount,
                            status = invoicePayment2.status,
                            invoiceGroupId = invoicePayment2.invoiceGroupId,
                            paymentDetail = invoicePayment2.paymentDetail,
                            createdAt = invoicePayment2.createdAt,
                            canceledReason = invoicePayment2.canceledReason,
                            approvedAt = invoicePayment2.approvedAt,
                            reason = invoicePayment2.reason,
                            method = invoicePayment2.method,
                            externalId = invoicePayment2.externalId
                        ),
                    )
                ),
                CompanyMemberInvoiceGroupInfo(
                    id = memberInvoiceGroup2.id,
                    externalId = memberInvoiceGroup2.externalId!!,
                    billingAccountablePartyId = memberInvoiceGroup2.billingAccountablePartyId,
                    referenceDate = memberInvoiceGroup2.referenceDate,
                    dueDate = memberInvoiceGroup2.dueDate,
                    status = memberInvoiceGroup2.status,
                    payments = listOf(
                        CompanyMemberInvoiceGroupPayment(
                            id = invoicePayment3.id,
                            totalAmount = invoicePayment3.amount,
                            status = invoicePayment3.status,
                            invoiceGroupId = invoicePayment3.invoiceGroupId,
                            paymentDetail = invoicePayment3.paymentDetail,
                            createdAt = invoicePayment3.createdAt,
                            canceledReason = invoicePayment3.canceledReason,
                            approvedAt = invoicePayment3.approvedAt,
                            reason = invoicePayment3.reason,
                            method = invoicePayment3.method,
                            externalId = invoicePayment3.externalId
                        ),
                    )
                )
            )
        )

        coEvery {
            getCompanyInvoicesUseCase.getByBillingAccountablePartyFromCompany(
                company.id
            )
        } returns expected


        authenticatedAs(token, staffToBeAuthenticated) {
            get("/company/${company.id}/invoice_info") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: CompanyInvoiceResponse = response.bodyAsJson()
                Assertions.assertThat(content).isEqualTo(expected)
            }
        }

        coVerifyOnce {
            getCompanyInvoicesUseCase.getByBillingAccountablePartyFromCompany(company.id)
        }
    }

    @Test
    fun `#approveInvoicePayment - should approve invoicePayment`() {
        val companyId = RangeUUID.generate()
        val invoicePayment = TestModelFactory.buildInvoicePayment()

        coEvery { invoicePaymentService.approve(any()) } returns invoicePayment

        authenticatedAs(token, staffToBeAuthenticated) {
            put("/company/$companyId/invoice_payment/${invoicePayment.id}/approve") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: InvoicePayment = response.bodyAsJson()
                Assertions.assertThat(content.id).isEqualTo(invoicePayment.id)
            }
        }

        coVerifyOnce { invoicePaymentService.approve(invoicePayment.id) }
    }

    @Test
    fun `#cancelInvoicePayment - should cancel invoicePayment`() {
        val companyId = RangeUUID.generate()
        val invoicePayment = TestModelFactory.buildInvoicePayment()

        coEvery { invoicePaymentService.cancel(any(), any()) } returns invoicePayment

        authenticatedAs(token, staffToBeAuthenticated) {
            put("/company/$companyId/invoice_payment/${invoicePayment.id}/cancel") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: InvoicePayment = response.bodyAsJson()
                Assertions.assertThat(content.id).isEqualTo(invoicePayment.id)
            }
        }

        coVerifyOnce { invoicePaymentService.cancel(invoicePayment.id, CancellationReason.INVALID) }
    }

    @Test
    fun `#createInvoicePayment - should create invoicePayment`() {
        val companyId = RangeUUID.generate()
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()
        val invoicePayment = TestModelFactory.buildInvoicePayment()
        val request = CreateInvoicePaymentRequest(paymentMethod = PaymentMethod.BOLEPIX, dueDate = "2023-07-14")
        val companyContract = TestModelFactory.buildCompanyContract()
        val companySubContract = TestModelFactory.buildCompanySubContract(contractId = companyContract.id)

        coEvery { companySubcontractService.get(companySubContract.id) } returns companySubContract
        coEvery { companyContractService.get(companyContract.id) } returns companyContract

        coEvery {
            preActivationCompanyInvoiceUseCase.createInvoicePayment(
                CreateInvoicePaymentPaylod(
                    preActivationInvoiceId = memberInvoiceGroup.id,
                    paymentMethod = request.paymentMethod,
                    dueDate = request.dueDate?.let { LocalDate.parse(it) },
                    groupCompany = null,
                )
            )
        } returns CreateInvoicePaymentResponse(
            id = invoicePayment.id,
            preActivationCompanyInvoiceId = memberInvoiceGroup.id,
            PreActivationCompanyInvoiceType.MEMBER_INVOICE_GROUP,
        )

        authenticatedAs(token, staffToBeAuthenticated) {
            post("/company/$companyId/invoice_group/${memberInvoiceGroup.id}/payment", request) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: InvoicePayment = response.bodyAsJson()
                Assertions.assertThat(content.id).isEqualTo(invoicePayment.id)
            }
        }

        coVerifyOnce {
            preActivationCompanyInvoiceUseCase.createInvoicePayment(
                CreateInvoicePaymentPaylod(
                    preActivationInvoiceId = memberInvoiceGroup.id,
                    paymentMethod = request.paymentMethod,
                    dueDate = request.dueDate?.let { LocalDate.parse(it) },
                    groupCompany = null,
                )
            )
        }
    }

    @Test
    fun `#createInvoicePayment - should create invoicePayment with group company`() {
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()
        val invoicePayment = TestModelFactory.buildInvoicePayment()
        val request = CreateInvoicePaymentRequest(paymentMethod = PaymentMethod.BOLEPIX, dueDate = "2023-07-14")
        val companyContract = TestModelFactory.buildCompanyContract(groupCompany = "0001")
        val companySubContract = TestModelFactory.buildCompanySubContract(contractId = companyContract.id)

        coEvery { companySubcontractService.get(companySubContract.id) } returns companySubContract
        coEvery { companyContractService.get(companyContract.id) } returns companyContract

        coEvery {
            preActivationCompanyInvoiceUseCase.createInvoicePayment(
                CreateInvoicePaymentPaylod(
                    preActivationInvoiceId = memberInvoiceGroup.id,
                    paymentMethod = request.paymentMethod,
                    dueDate = request.dueDate?.let { LocalDate.parse(it) },
                    groupCompany = "0001",
                )
            )
        } returns CreateInvoicePaymentResponse(
            id = invoicePayment.id,
            preActivationCompanyInvoiceId = memberInvoiceGroup.id,
            PreActivationCompanyInvoiceType.MEMBER_INVOICE_GROUP,
        )

        authenticatedAs(token, staffToBeAuthenticated) {
            post(
                "/company/subcontract/${companySubContract.id}/pre_activation_invoice/${memberInvoiceGroup.id}/payment",
                request
            ) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: InvoicePayment = response.bodyAsJson()
                Assertions.assertThat(content.id).isEqualTo(invoicePayment.id)
            }
        }

        coVerifyOnce {
            companySubcontractService.get(companySubContract.id)
            companyContractService.get(companyContract.id)
            preActivationCompanyInvoiceUseCase.createInvoicePayment(
                CreateInvoicePaymentPaylod(
                    preActivationInvoiceId = memberInvoiceGroup.id,
                    paymentMethod = request.paymentMethod,
                    dueDate = request.dueDate?.let { LocalDate.parse(it) },
                    groupCompany = "0001",
                )
            )
        }
    }

    @Test
    fun `#createFirstInvoicePayment - should create first invoicePayment`() {
        val subContractId = RangeUUID.generate()
        val memberInvoiceGroupId = RangeUUID.generate()
        val companyContract = TestModelFactory.buildCompanyContract()
        val companySubContract =
            TestModelFactory.buildCompanySubContract(
                subContractId,
                contractId = companyContract.id,
                billingAccountablePartyId = RangeUUID.generate(),
            )

        val request = CreateFirstInvoicePaymentRequest("2024-01", PaymentMethod.BOLETO, "2024-02-21")

        coEvery { companySubcontractService.get(subContractId) } returns companySubContract
        coEvery { companyContractService.get(companySubContract.contractId) } returns companyContract
        coEvery { preActivationCompanyInvoiceUseCase.create(any()) } returns CreateInvoiceResponse(
            memberInvoiceGroupId,
            PreActivationCompanyInvoiceType.MEMBER_INVOICE_GROUP
        )

        authenticatedAs(token, staffToBeAuthenticated) {
            post("/company/subcontract/${subContractId}/first_payment", request) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: MemberInvoiceGroup = response.bodyAsJson()
                Assertions.assertThat(content.id).isEqualTo(memberInvoiceGroupId)
            }
        }
    }

    @Test
    fun `#cancelFirstInvoicePayment - should cancel pre activation payment`() {
        val memberInvoiceGroupId = RangeUUID.generate()
        val companyContract = TestModelFactory.buildCompanyContract()
        val companySubContract = TestModelFactory.buildCompanySubContract(contractId = companyContract.id)

        coEvery { companySubcontractService.get(companySubContract.id) } returns companySubContract
        coEvery { companyContractService.get(companyContract.id) } returns companyContract

        coEvery {
            preActivationCompanyInvoiceUseCase.cancel(
                CancelInvoicePayload(
                    memberInvoiceGroupId,
                    null
                )
            )
        } returns CancelInvoiceResponse(memberInvoiceGroupId, PreActivationCompanyInvoiceType.MEMBER_INVOICE_GROUP)

        authenticatedAs(token, staffToBeAuthenticated) {
            delete("company/subcontract/${companySubContract.id}/pre_activation_invoice/${memberInvoiceGroupId}") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val content: CancelInvoiceResponse = response.bodyAsJson()
                Assertions.assertThat(content.id).isEqualTo(memberInvoiceGroupId)
            }
        }
    }

    @Test
    fun `#cancelFirstInvoicePayment - should not cancel first memberInvoiceGroup when it is paid`() {
        val memberInvoiceGroupId = RangeUUID.generate()

        coEvery {
            preActivationCompanyInvoiceUseCase.cancel(
                CancelInvoicePayload(
                    memberInvoiceGroupId,
                    null
                )
            )
        } returns IllegalStateException("MemberInvoiceGroup is already canceled or paid")

        authenticatedAs(token, staffToBeAuthenticated) {
            put("/company/invoice_group/${memberInvoiceGroupId}/cancel_first_payment") { response ->
                ResponseAssert.assertThat(response).isInternalServerError()
            }
        }
    }
}
