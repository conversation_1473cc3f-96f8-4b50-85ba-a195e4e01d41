package br.com.alice.api.healthcareops.controllers.moneyin.v2.company

import br.com.alice.api.healthcareops.api.RoutesTestHelper
import br.com.alice.api.healthcareops.models.CompanyInvoiceResponseV2
import br.com.alice.api.healthcareops.usecases.GetCompanyInvoicesUseCase
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.BeforeTest
import kotlin.test.Test

class CompanyInvoiceControllerV2Test : RoutesTestHelper() {

    private val getCompanyInvoicesUseCase: GetCompanyInvoicesUseCase = mockk()

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single {
            CompanyInvoicesControllerV2(
                getCompanyInvoicesUseCase,
            )
        }
    }

    private val company = TestModelFactory.buildCompany()

    @Test
    fun `#listByBillingAccountableParty should return expected`() = runBlocking {
        coEvery {
            getCompanyInvoicesUseCase.getByBillingAccountablePartyFromCompanyV2(company.id)
        } returns mockk<CompanyInvoiceResponseV2>()

        authenticatedAs(token, staffToBeAuthenticated) {
            get("/v2/invoices/company/${company.id}") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
            }
        }

        coVerifyOnce {
            getCompanyInvoicesUseCase.getByBillingAccountablePartyFromCompanyV2(company.id)
        }
    }

    @Test
    fun `#listBySubContractId should return expected`() = runBlocking {
        val subContractId = RangeUUID.generate()
        coEvery {
            getCompanyInvoicesUseCase.getBySubcontractV2(subContractId)
        } returns mockk<CompanyInvoiceResponseV2>()

        authenticatedAs(token, staffToBeAuthenticated) {
            get("/v2/invoices/company/${company.id}/subcontract/$subContractId") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
            }
        }

        coVerifyOnce {
            getCompanyInvoicesUseCase.getBySubcontractV2(subContractId)
        }
    }
}
