package br.com.alice.api.healthcareops.usecases

import br.com.alice.api.healthcareops.converters.business.CompanyConverter.toCompanyInvoiceResponseV2
import br.com.alice.api.healthcareops.models.CompanyBillingAccountableParty
import br.com.alice.api.healthcareops.models.CompanyInvoiceResponse
import br.com.alice.api.healthcareops.models.CompanyMemberInvoiceGroupInfo
import br.com.alice.api.healthcareops.models.CompanyMemberInvoiceGroupPayment
import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atBeginningOfTheMonth
import br.com.alice.common.core.extensions.atEndOfTheMonth
import br.com.alice.common.core.extensions.money
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.InvoicePaymentSource
import br.com.alice.data.layer.models.MemberInvoiceType
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.moneyin.client.CompanyInvoiceService
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.client.PortalUrlGeneratorService
import br.com.alice.moneyin.models.BankSlipDetail
import br.com.alice.moneyin.models.CompanyInvoiceModel
import br.com.alice.moneyin.models.CompanyInvoicePaymentMethod
import br.com.alice.moneyin.models.CompanyInvoicePaymentStatus
import br.com.alice.moneyin.models.CompanyInvoiceStatus
import br.com.alice.moneyin.models.CompanyInvoiceType
import br.com.alice.moneyin.models.CompanyInvoiceValidityPeriod
import br.com.alice.moneyin.models.InvoicePaymentWithPortalUrl
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith
import br.com.alice.moneyin.models.CompanyInvoicePayment as MoneyInCompanyInvoicePayment
import br.com.alice.moneyin.models.CompanyInvoiceResponse as MoneyInCompanyInvoiceResponse

class GetCompanyInvoicesUseCaseTest {

    private val companyService: CompanyService = mockk()
    private val memberInvoiceGroupService: MemberInvoiceGroupService = mockk()
    private val invoicePaymentService: InvoicePaymentService = mockk()
    private val billingAccountablePartyService: BillingAccountablePartyService = mockk()
    private val companySubcontractService: CompanySubContractService = mockk()
    private val companyContractService: CompanyContractService = mockk()
    private val portalUrlGeneratorService: PortalUrlGeneratorService = mockk()
    private val companyInvoiceService: CompanyInvoiceService = mockk()

    private val getCompanyInvoicesUseCase = GetCompanyInvoicesUseCase(
        companyService,
        memberInvoiceGroupService,
        invoicePaymentService,
        billingAccountablePartyService,
        companySubcontractService,
        companyContractService,
        portalUrlGeneratorService,
        companyInvoiceService,
    )

    private val invoiceId = RangeUUID.generate()
    private val billingAccountablePartyId = RangeUUID.generate()
    private val referenceDate = LocalDate.now()
    private val companyId = RangeUUID.generate()
    private val companySubContractId = RangeUUID.generate()

    private val invoicePaymentId = RangeUUID.generate()

    private val invoicePaymentsWithPortalUrl = listOf(
        InvoicePaymentWithPortalUrl(
            invoicePaymentId,
            "portalUrl"
        )
    )

    private val invoices = listOf(
        MoneyInCompanyInvoiceResponse(
            id = invoiceId,
            externalId = null,
            status = CompanyInvoiceStatus.PROCESSING,
            billingAccountablePartyId = billingAccountablePartyId,
            referenceDate = referenceDate,
            multipleValidityPeriods = listOf(
                CompanyInvoiceValidityPeriod(
                    start = referenceDate.atBeginningOfTheMonth(),
                    end = referenceDate.atEndOfTheMonth(),
                )
            ),
            validityPeriod = CompanyInvoiceValidityPeriod(
                start = referenceDate.atBeginningOfTheMonth(),
                end = referenceDate.atEndOfTheMonth()
            ),
            companySubContractIds = listOf(companySubContractId),
            companyIds = listOf(companyId),
            type = CompanyInvoiceType.RECURRENT,
            model = CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
            totalAmount = 100.money,
            discount = 0.money,
            addition = 0.money,
            dueDate = LocalDate.now().plusDays(5),
            quantityMemberInvoices = 1,
            memberInvoiceIds = emptyList(),
            relationships = emptyList(),
            globalItems = emptyList(),
            installments = 0,
            totalInstallments = 0,
            version = 1,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
        ),
        MoneyInCompanyInvoiceResponse(
            id = RangeUUID.generate(),
            externalId = "PLS|000001|01|DP",
            status = CompanyInvoiceStatus.PROCESSED,
            referenceDate = referenceDate,
            billingAccountablePartyId = billingAccountablePartyId,
            validityPeriod = CompanyInvoiceValidityPeriod(
                start = referenceDate.atBeginningOfTheMonth(),
                end = referenceDate.atEndOfTheMonth()
            ),
            multipleValidityPeriods = listOf(
                CompanyInvoiceValidityPeriod(
                    start = referenceDate.atBeginningOfTheMonth(),
                    end = referenceDate.atEndOfTheMonth(),
                )
            ),
            companySubContractIds = listOf(companySubContractId),
            companyIds = listOf(companyId),
            type = CompanyInvoiceType.RECURRENT,
            model = CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
            payments = listOf(
                MoneyInCompanyInvoicePayment(
                    id = invoicePaymentId,
                    amount = 100.money,
                    status = CompanyInvoicePaymentStatus.APPROVED,
                    approvedAt = LocalDateTime.now(),
                    createdAt = LocalDateTime.now(),
                    method = CompanyInvoicePaymentMethod.BOLETO,
                    externalId = "123456",
                    reason = null,
                    pix = null,
                    bankSlip = BankSlipDetail(
                        paymentUrl = "http://url",
                        dueDate = LocalDateTime.now().plusDays(5),
                        barcode = "123456",
                        externalId = "123456",
                    ),
                    creditCard = null,
                    invoiceId = RangeUUID.generate(),
                    updatedAt = LocalDateTime.now(),
                )
            ),
            totalAmount = 100.money,
            discount = 0.money,
            addition = 0.money,
            dueDate = LocalDate.now().plusDays(5),
            quantityMemberInvoices = 1,
            memberInvoiceIds = emptyList(),
            relationships = emptyList(),
            globalItems = emptyList(),
            installments = 0,
            totalInstallments = 0,
            version = 1,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
        )
    )

    @Nested
    inner class GetByBillingAccountablePartyFromCompanyV2 {

        @Test
        fun `#should return companyInvoices by billing accountable party id`() =
            runBlocking {
                val product = TestModelFactory.buildProduct()
                val billingAccountableParty =
                    TestModelFactory.buildBillingAccountableParty().copy(id = billingAccountablePartyId)
                val company = TestModelFactory.buildCompany(
                    id = companyId,
                    availableProducts = listOf(product.id),
                    defaultProductId = product.id,
                    billingAccountablePartyId = billingAccountableParty.id
                )

                coEvery { companyService.get(companyId) } returns company
                coEvery { billingAccountablePartyService.get(billingAccountablePartyId) } returns billingAccountableParty
                coEvery {
                    companyInvoiceService.listByBillingAccountablePartyIdAndModelsWithPayments(
                        billingAccountableParty.id, listOf(
                            CompanyInvoiceModel.MEMBER_INVOICE_GROUP, CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT
                        )
                    )
                } returns invoices

                coEvery { portalUrlGeneratorService.mountPortalUrlForInvoicePaymentIds(listOf(invoicePaymentId)) } returns invoicePaymentsWithPortalUrl

                val expected =
                    company.toCompanyInvoiceResponseV2(billingAccountableParty, invoices, invoicePaymentsWithPortalUrl)

                val result =
                    getCompanyInvoicesUseCase.getByBillingAccountablePartyFromCompanyV2(
                        company.id,
                    )

                assertEquals(expected.id, result.id)
                assertEquals(expected.name, result.name)
                assertEquals(expected.legalName, result.legalName)
                assertEquals(expected.cnpj, result.cnpj)
                assertEquals(expected.email, result.email)
                assertEquals(expected.phoneNumber, result.phoneNumber)
                assertEquals(expected.address, result.address)
                assertEquals(expected.billingAccountableParty.id, result.billingAccountableParty.id)
                assertEquals(expected.billingAccountableParty.firstName, result.billingAccountableParty.firstName)
                assertEquals(expected.billingAccountableParty.lastName, result.billingAccountableParty.lastName)
                assertEquals(expected.billingAccountableParty.type, result.billingAccountableParty.type)
                assertEquals(expected.billingAccountableParty.nationalId, result.billingAccountableParty.nationalId)
                assertEquals(expected.billingAccountableParty.email, result.billingAccountableParty.email)
                assertEquals(expected.invoices, result.invoices)

                coVerifyOnce {
                    companyInvoiceService.listByBillingAccountablePartyIdAndModelsWithPayments(
                        billingAccountableParty.id, listOf(
                            CompanyInvoiceModel.MEMBER_INVOICE_GROUP, CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT
                        )
                    )
                    portalUrlGeneratorService.mountPortalUrlForInvoicePaymentIds(listOf(invoicePaymentId))
                    companyService.get(company.id)
                    billingAccountablePartyService.get(company.billingAccountablePartyId!!)
                }
            }

        @Test
        fun `#should throw not found exception when company does not have a billing accountable party`() =
            runBlocking {
                val product = TestModelFactory.buildProduct()
                val company = TestModelFactory.buildCompany(
                    id = companyId,
                    availableProducts = listOf(product.id),
                    defaultProductId = product.id,
                    billingAccountablePartyId = null
                )

                coEvery { companyService.get(companyId) } returns company

                assertFailsWith<NotFoundException> {
                    getCompanyInvoicesUseCase.getByBillingAccountablePartyFromCompanyV2(
                        companyId,
                    )
                }

                coVerifyOnce { companyService.get(company.id) }
                coVerifyNone { billingAccountablePartyService.get(any()) }
            }
    }

    @Nested
    inner class GetBySubcontractV2 {

        @Test
        fun `#should return companyInvoices by subcontract id`() =
            runBlocking {
                val product = TestModelFactory.buildProduct()
                val billingAccountableParty =
                    TestModelFactory.buildBillingAccountableParty().copy(id = billingAccountablePartyId)
                val company = TestModelFactory.buildCompany(
                    id = companyId,
                    availableProducts = listOf(product.id),
                    defaultProductId = product.id,
                    billingAccountablePartyId = billingAccountableParty.id
                )

                val subContract = TestModelFactory.buildCompanySubContract(
                    companyId = company.id,
                    billingAccountablePartyId = billingAccountableParty.id
                )

                coEvery { companySubcontractService.get(subContract.id) } returns subContract
                coEvery { companyService.get(companyId) } returns company
                coEvery { billingAccountablePartyService.get(billingAccountablePartyId) } returns billingAccountableParty
                coEvery {
                    companyInvoiceService.listBySubContractIdAndModelsWithPayments(
                        subContract.id, listOf(
                            CompanyInvoiceModel.MEMBER_INVOICE_GROUP, CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT
                        )
                    )
                } returns invoices

                coEvery { portalUrlGeneratorService.mountPortalUrlForInvoicePaymentIds(listOf(invoicePaymentId)) } returns invoicePaymentsWithPortalUrl

                val expected =
                    company.toCompanyInvoiceResponseV2(billingAccountableParty, invoices, invoicePaymentsWithPortalUrl)

                val result =
                    getCompanyInvoicesUseCase.getBySubcontractV2(subcontractId = subContract.id)

                assertEquals(expected.id, result.id)
                assertEquals(expected.name, result.name)
                assertEquals(expected.legalName, result.legalName)
                assertEquals(expected.cnpj, result.cnpj)
                assertEquals(expected.email, result.email)
                assertEquals(expected.phoneNumber, result.phoneNumber)
                assertEquals(expected.address, result.address)
                assertEquals(expected.billingAccountableParty.id, result.billingAccountableParty.id)
                assertEquals(expected.billingAccountableParty.firstName, result.billingAccountableParty.firstName)
                assertEquals(expected.billingAccountableParty.lastName, result.billingAccountableParty.lastName)
                assertEquals(expected.billingAccountableParty.type, result.billingAccountableParty.type)
                assertEquals(expected.billingAccountableParty.nationalId, result.billingAccountableParty.nationalId)
                assertEquals(expected.billingAccountableParty.email, result.billingAccountableParty.email)
                assertEquals(expected.invoices, result.invoices)

                coVerifyOnce {
                    companySubcontractService.get(subContract.id)
                    companyInvoiceService.listBySubContractIdAndModelsWithPayments(
                        subContract.id, listOf(
                            CompanyInvoiceModel.MEMBER_INVOICE_GROUP, CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT
                        )
                    )
                    portalUrlGeneratorService.mountPortalUrlForInvoicePaymentIds(listOf(invoicePaymentId))
                    companyService.get(company.id)
                    billingAccountablePartyService.get(billingAccountablePartyId)
                }
            }

        @Test
        fun `#should return companyInvoices by subcontract id with billing accountable party from contract`() =
            runBlocking {
                val product = TestModelFactory.buildProduct()
                val billingAccountableParty =
                    TestModelFactory.buildBillingAccountableParty().copy(id = billingAccountablePartyId)
                val companyContract = TestModelFactory.buildCompanyContract(
                    billingAccountablePartyId = billingAccountableParty.id
                )

                val company = TestModelFactory.buildCompany(
                    id = companyId,
                    availableProducts = listOf(product.id),
                    defaultProductId = product.id,
                    contractIds = listOf(companyContract.id),
                )

                val subContract = TestModelFactory.buildCompanySubContract(
                    companyId = company.id,
                    billingAccountablePartyId = null,
                    contractId = companyContract.id,
                )

                coEvery { companyContractService.get(companyContract.id) } returns companyContract
                coEvery { companySubcontractService.get(subContract.id) } returns subContract
                coEvery { companyService.get(companyId) } returns company
                coEvery { billingAccountablePartyService.get(billingAccountablePartyId) } returns billingAccountableParty
                coEvery {
                    companyInvoiceService.listBySubContractIdAndModelsWithPayments(
                        subContract.id, listOf(
                            CompanyInvoiceModel.MEMBER_INVOICE_GROUP, CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT
                        )
                    )
                } returns invoices

                coEvery { portalUrlGeneratorService.mountPortalUrlForInvoicePaymentIds(listOf(invoicePaymentId)) } returns invoicePaymentsWithPortalUrl

                val expected =
                    company.toCompanyInvoiceResponseV2(billingAccountableParty, invoices, invoicePaymentsWithPortalUrl)

                val result =
                    getCompanyInvoicesUseCase.getBySubcontractV2(subcontractId = subContract.id)

                assertEquals(expected.id, result.id)
                assertEquals(expected.name, result.name)
                assertEquals(expected.legalName, result.legalName)
                assertEquals(expected.cnpj, result.cnpj)
                assertEquals(expected.email, result.email)
                assertEquals(expected.phoneNumber, result.phoneNumber)
                assertEquals(expected.address, result.address)
                assertEquals(expected.billingAccountableParty.id, result.billingAccountableParty.id)
                assertEquals(expected.billingAccountableParty.firstName, result.billingAccountableParty.firstName)
                assertEquals(expected.billingAccountableParty.lastName, result.billingAccountableParty.lastName)
                assertEquals(expected.billingAccountableParty.type, result.billingAccountableParty.type)
                assertEquals(expected.billingAccountableParty.nationalId, result.billingAccountableParty.nationalId)
                assertEquals(expected.billingAccountableParty.email, result.billingAccountableParty.email)
                assertEquals(expected.invoices, result.invoices)

                coVerifyOnce {
                    companySubcontractService.get(subContract.id)
                    companyContractService.get(companyContract.id)
                    companyInvoiceService.listBySubContractIdAndModelsWithPayments(
                        subContract.id, listOf(
                            CompanyInvoiceModel.MEMBER_INVOICE_GROUP, CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT
                        )
                    )
                    portalUrlGeneratorService.mountPortalUrlForInvoicePaymentIds(listOf(invoicePaymentId))
                    companyService.get(company.id)
                    billingAccountablePartyService.get(billingAccountablePartyId)
                }
            }

        @Test
        fun `#should throw not found exception when billing accountable party is not found`() = runBlocking {
            val product = TestModelFactory.buildProduct()

            val companyContract = TestModelFactory.buildCompanyContract(
                billingAccountablePartyId = null,
            )

            val company = TestModelFactory.buildCompany(
                id = companyId,
                contractIds = listOf(companyContract.id),
                availableProducts = listOf(product.id),
                defaultProductId = product.id,
            )

            val subContract = TestModelFactory.buildCompanySubContract(
                companyId = company.id,
                billingAccountablePartyId = null,
                contractId = companyContract.id,
            )

            coEvery { companyContractService.get(companyContract.id) } returns companyContract
            coEvery { companySubcontractService.get(subContract.id) } returns subContract
            coEvery { companyService.get(companyId) } returns company
            coEvery {
                companyInvoiceService.listBySubContractIdAndModelsWithPayments(
                    subContract.id, listOf(
                        CompanyInvoiceModel.MEMBER_INVOICE_GROUP, CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT
                    )
                )
            } returns invoices

            assertFailsWith<NotFoundException> {
                getCompanyInvoicesUseCase.getBySubcontractV2(subcontractId = subContract.id)
            }

            coVerifyOnce {
                companyContractService.get(companyContract.id)
                companySubcontractService.get(subContract.id)
                companyService.get(company.id)
                companyInvoiceService.listBySubContractIdAndModelsWithPayments(
                    subContract.id, listOf(
                        CompanyInvoiceModel.MEMBER_INVOICE_GROUP, CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT
                    )
                )
            }
            coVerifyNone {
                billingAccountablePartyService.get(any())
                portalUrlGeneratorService.mountPortalUrlForInvoicePaymentIds(any())
            }
        }
    }

    @Test
    fun `#should return companyInvoices by billing Accountable Party`() = runBlocking {
        val product = TestModelFactory.buildProduct()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val company = TestModelFactory.buildCompany(
            availableProducts = listOf(product.id),
            defaultProductId = product.id,
            billingAccountablePartyId = billingAccountableParty.id
        )

        val memberInvoiceGroup1 =
            TestModelFactory.buildMemberInvoiceGroup(billingAccountablePartyId = company.billingAccountablePartyId!!)
        val memberInvoiceGroup2 =
            TestModelFactory.buildMemberInvoiceGroup(billingAccountablePartyId = company.billingAccountablePartyId!!)

        val invoicePayment1 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
        val invoicePayment2 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
        val invoicePayment3 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup2.id)

        val expected = CompanyInvoiceResponse(
            id = company.id,
            name = company.name,
            legalName = company.legalName,
            cnpj = company.cnpj,
            email = company.email,
            phoneNumber = company.phoneNumber,
            address = company.address,
            billingAccountableParty = CompanyBillingAccountableParty(
                id = billingAccountableParty.id,
                firstName = billingAccountableParty.firstName,
                lastName = billingAccountableParty.lastName,
                type = billingAccountableParty.type,
                nationalId = billingAccountableParty.nationalId,
                email = billingAccountableParty.email,
            ),
            memberInvoiceGroup = listOf(
                CompanyMemberInvoiceGroupInfo(
                    id = memberInvoiceGroup1.id,
                    externalId = memberInvoiceGroup1.externalId!!,
                    billingAccountablePartyId = memberInvoiceGroup1.billingAccountablePartyId,
                    referenceDate = memberInvoiceGroup1.referenceDate,
                    dueDate = memberInvoiceGroup1.dueDate,
                    status = memberInvoiceGroup1.status,
                    payments = listOf(
                        CompanyMemberInvoiceGroupPayment(
                            id = invoicePayment1.id,
                            totalAmount = invoicePayment1.amount,
                            status = invoicePayment1.status,
                            invoiceGroupId = invoicePayment1.invoiceGroupId,
                            paymentDetail = invoicePayment1.paymentDetail,
                            createdAt = invoicePayment1.createdAt,
                            canceledReason = invoicePayment1.canceledReason,
                            approvedAt = invoicePayment1.approvedAt,
                            reason = invoicePayment1.reason,
                            method = invoicePayment1.method,
                            externalId = invoicePayment1.externalId
                        ),
                        CompanyMemberInvoiceGroupPayment(
                            id = invoicePayment2.id,
                            totalAmount = invoicePayment2.amount,
                            status = invoicePayment2.status,
                            invoiceGroupId = invoicePayment2.invoiceGroupId,
                            paymentDetail = invoicePayment2.paymentDetail,
                            createdAt = invoicePayment2.createdAt,
                            canceledReason = invoicePayment2.canceledReason,
                            approvedAt = invoicePayment2.approvedAt,
                            reason = invoicePayment2.reason,
                            method = invoicePayment2.method,
                            externalId = invoicePayment2.externalId
                        ),
                    )
                ),
                CompanyMemberInvoiceGroupInfo(
                    id = memberInvoiceGroup2.id,
                    externalId = memberInvoiceGroup2.externalId!!,
                    billingAccountablePartyId = memberInvoiceGroup2.billingAccountablePartyId,
                    referenceDate = memberInvoiceGroup2.referenceDate,
                    dueDate = memberInvoiceGroup2.dueDate,
                    status = memberInvoiceGroup2.status,
                    payments = listOf(
                        CompanyMemberInvoiceGroupPayment(
                            id = invoicePayment3.id,
                            totalAmount = invoicePayment3.amount,
                            status = invoicePayment3.status,
                            invoiceGroupId = invoicePayment3.invoiceGroupId,
                            paymentDetail = invoicePayment3.paymentDetail,
                            createdAt = invoicePayment3.createdAt,
                            canceledReason = invoicePayment3.canceledReason,
                            approvedAt = invoicePayment3.approvedAt,
                            reason = invoicePayment3.reason,
                            method = invoicePayment3.method,
                            externalId = invoicePayment3.externalId
                        ),
                    )
                )
            )
        )

        val invoicePayments = listOf(
            invoicePayment1, invoicePayment2, invoicePayment3
        )

        coEvery { companyService.get(any()) } returns company
        coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
        coEvery { memberInvoiceGroupService.getByBillingAccountablePartyId(any()) } returns listOf(
            memberInvoiceGroup1,
            memberInvoiceGroup2
        )
        coEvery {
            invoicePaymentService.listInvoicePaymentsByBillingAccountablePartyId(
                any(),
                any()
            )
        } returns listOf(
            invoicePayment1, invoicePayment2, invoicePayment3
        )
        coEvery { portalUrlGeneratorService.mountPortalUrlForInvoicePayments(invoicePayments) } returns emptyList()

        val result =
            getCompanyInvoicesUseCase.getByBillingAccountablePartyFromCompany(company.id).get()

        assertEquals(expected.billingAccountableParty.id, result.billingAccountableParty.id)
        assertEquals(expected.cnpj, result.cnpj)
        assertEquals(expected.email, result.email)
        assertEquals(
            expected.memberInvoiceGroup.first().payments.first().totalAmount,
            result.memberInvoiceGroup.first().payments.first().totalAmount
        )

        coVerifyOnce { companyService.get(company.id) }
        coVerifyOnce { billingAccountablePartyService.get(company.billingAccountablePartyId!!) }
        coVerifyOnce { memberInvoiceGroupService.getByBillingAccountablePartyId(company.billingAccountablePartyId!!) }
        coVerifyOnce {
            invoicePaymentService.listInvoicePaymentsByBillingAccountablePartyId(
                company.billingAccountablePartyId!!,
                true
            )
        }
    }

    @Test
    fun `#should return companyInvoices by billing Accountable Party with portalUrl for source Itau`() =
        runBlocking {
            val product = TestModelFactory.buildProduct()
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val company = TestModelFactory.buildCompany(
                availableProducts = listOf(product.id),
                defaultProductId = product.id,
                billingAccountablePartyId = billingAccountableParty.id
            )

            val memberInvoiceGroup1 =
                TestModelFactory.buildMemberInvoiceGroup(billingAccountablePartyId = company.billingAccountablePartyId!!)
            val memberInvoiceGroup2 =
                TestModelFactory.buildMemberInvoiceGroup(billingAccountablePartyId = company.billingAccountablePartyId!!)

            val invoicePayment1 = TestModelFactory.buildInvoicePayment(
                invoiceGroupId = memberInvoiceGroup1.id,
                source = InvoicePaymentSource.ITAU
            )
            val invoicePayment2 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
            val invoicePayment3 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup2.id)

            val expected = CompanyInvoiceResponse(
                id = company.id,
                name = company.name,
                legalName = company.legalName,
                cnpj = company.cnpj,
                email = company.email,
                phoneNumber = company.phoneNumber,
                address = company.address,
                billingAccountableParty = CompanyBillingAccountableParty(
                    id = billingAccountableParty.id,
                    firstName = billingAccountableParty.firstName,
                    lastName = billingAccountableParty.lastName,
                    type = billingAccountableParty.type,
                    nationalId = billingAccountableParty.nationalId,
                    email = billingAccountableParty.email,
                ),
                memberInvoiceGroup = listOf(
                    CompanyMemberInvoiceGroupInfo(
                        id = memberInvoiceGroup1.id,
                        externalId = memberInvoiceGroup1.externalId!!,
                        billingAccountablePartyId = memberInvoiceGroup1.billingAccountablePartyId,
                        referenceDate = memberInvoiceGroup1.referenceDate,
                        dueDate = memberInvoiceGroup1.dueDate,
                        status = memberInvoiceGroup1.status,
                        payments = listOf(
                            CompanyMemberInvoiceGroupPayment(
                                id = invoicePayment1.id,
                                totalAmount = invoicePayment1.amount,
                                status = invoicePayment1.status,
                                invoiceGroupId = invoicePayment1.invoiceGroupId,
                                paymentDetail = invoicePayment1.paymentDetail,
                                createdAt = invoicePayment1.createdAt,
                                canceledReason = invoicePayment1.canceledReason,
                                approvedAt = invoicePayment1.approvedAt,
                                reason = invoicePayment1.reason,
                                method = invoicePayment1.method,
                                externalId = invoicePayment1.externalId,
                                portalUrl = "portalUrl"
                            ),
                            CompanyMemberInvoiceGroupPayment(
                                id = invoicePayment2.id,
                                totalAmount = invoicePayment2.amount,
                                status = invoicePayment2.status,
                                invoiceGroupId = invoicePayment2.invoiceGroupId,
                                paymentDetail = invoicePayment2.paymentDetail,
                                createdAt = invoicePayment2.createdAt,
                                canceledReason = invoicePayment2.canceledReason,
                                approvedAt = invoicePayment2.approvedAt,
                                reason = invoicePayment2.reason,
                                method = invoicePayment2.method,
                                externalId = invoicePayment2.externalId
                            ),
                        ),
                    ),
                    CompanyMemberInvoiceGroupInfo(
                        id = memberInvoiceGroup2.id,
                        externalId = memberInvoiceGroup2.externalId!!,
                        billingAccountablePartyId = memberInvoiceGroup2.billingAccountablePartyId,
                        referenceDate = memberInvoiceGroup2.referenceDate,
                        dueDate = memberInvoiceGroup2.dueDate,
                        status = memberInvoiceGroup2.status,
                        payments = listOf(
                            CompanyMemberInvoiceGroupPayment(
                                id = invoicePayment3.id,
                                totalAmount = invoicePayment3.amount,
                                status = invoicePayment3.status,
                                invoiceGroupId = invoicePayment3.invoiceGroupId,
                                paymentDetail = invoicePayment3.paymentDetail,
                                createdAt = invoicePayment3.createdAt,
                                canceledReason = invoicePayment3.canceledReason,
                                approvedAt = invoicePayment3.approvedAt,
                                reason = invoicePayment3.reason,
                                method = invoicePayment3.method,
                                externalId = invoicePayment3.externalId
                            ),
                        )
                    )
                )
            )

            val invoicePayments = listOf(
                invoicePayment1, invoicePayment2, invoicePayment3
            )
            val invoicePaymentWithPortalUrl = listOf(
                InvoicePaymentWithPortalUrl(
                    invoicePayment1.id,
                    "portalUrl"
                )
            )

            coEvery { companyService.get(any()) } returns company
            coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
            coEvery { memberInvoiceGroupService.getByBillingAccountablePartyId(any()) } returns listOf(
                memberInvoiceGroup1,
                memberInvoiceGroup2
            )
            coEvery {
                invoicePaymentService.listInvoicePaymentsByBillingAccountablePartyId(
                    any(),
                    any()
                )
            } returns listOf(
                invoicePayment1, invoicePayment2, invoicePayment3
            )
            coEvery { portalUrlGeneratorService.mountPortalUrlForInvoicePayments(invoicePayments) } returns invoicePaymentWithPortalUrl

            val result =
                getCompanyInvoicesUseCase.getByBillingAccountablePartyFromCompany(
                    company.id,
                ).get()

            assertEquals(expected.billingAccountableParty.id, result.billingAccountableParty.id)
            assertEquals(expected.cnpj, result.cnpj)
            assertEquals(expected.email, result.email)
            assertEquals(
                expected.memberInvoiceGroup.first().payments.first().totalAmount,
                result.memberInvoiceGroup.first().payments.first().totalAmount
            )
            assertEquals(expected.memberInvoiceGroup.first().payments.first().portalUrl, "portalUrl")

            coVerifyOnce { companyService.get(company.id) }
            coVerifyOnce { billingAccountablePartyService.get(company.billingAccountablePartyId!!) }
            coVerifyOnce { memberInvoiceGroupService.getByBillingAccountablePartyId(company.billingAccountablePartyId!!) }
            coVerifyOnce {
                invoicePaymentService.listInvoicePaymentsByBillingAccountablePartyId(
                    company.billingAccountablePartyId!!,
                    true
                )
            }
            coVerifyOnce {
                portalUrlGeneratorService.mountPortalUrlForInvoicePayments(any())
            }
        }

    @Test
    fun `#getByCompanySubcontract - should return CompanyInvoiceResponse by a subcontract`() = runBlocking {
        val product = TestModelFactory.buildProduct()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val company = TestModelFactory.buildCompany(
            availableProducts = listOf(product.id),
            defaultProductId = product.id,
            billingAccountablePartyId = billingAccountableParty.id
        )
        val companySubcontract = TestModelFactory.buildCompanySubContract(
            companyId = company.id,
            billingAccountablePartyId = billingAccountableParty.id
        )

        val memberInvoiceGroup1 =
            TestModelFactory.buildMemberInvoiceGroup(
                billingAccountablePartyId = company.billingAccountablePartyId!!,
                type = MemberInvoiceType.B2B_FIRST_PAYMENT,
            )
        val memberInvoiceGroup2 =
            TestModelFactory.buildMemberInvoiceGroup(
                billingAccountablePartyId = company.billingAccountablePartyId!!,
                type = MemberInvoiceType.B2B_REGULAR_PAYMENT,
            )

        val invoicePayment1 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
        val invoicePayment2 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
        val invoicePayment3 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup2.id)

        val expected = CompanyInvoiceResponse(
            id = company.id,
            name = company.name,
            legalName = company.legalName,
            cnpj = company.cnpj,
            email = company.email,
            phoneNumber = company.phoneNumber,
            address = company.address,
            billingAccountableParty = CompanyBillingAccountableParty(
                id = billingAccountableParty.id,
                firstName = billingAccountableParty.firstName,
                lastName = billingAccountableParty.lastName,
                type = billingAccountableParty.type,
                nationalId = billingAccountableParty.nationalId,
                email = billingAccountableParty.email,
            ),
            memberInvoiceGroup = listOf(
                CompanyMemberInvoiceGroupInfo(
                    id = memberInvoiceGroup1.id,
                    externalId = memberInvoiceGroup1.externalId!!,
                    billingAccountablePartyId = memberInvoiceGroup1.billingAccountablePartyId,
                    referenceDate = memberInvoiceGroup1.referenceDate,
                    dueDate = memberInvoiceGroup1.dueDate,
                    status = memberInvoiceGroup1.status,
                    type = memberInvoiceGroup1.type,
                    payments = listOf(
                        CompanyMemberInvoiceGroupPayment(
                            id = invoicePayment1.id,
                            totalAmount = invoicePayment1.amount,
                            status = invoicePayment1.status,
                            invoiceGroupId = invoicePayment1.invoiceGroupId,
                            paymentDetail = invoicePayment1.paymentDetail,
                            createdAt = invoicePayment1.createdAt,
                            canceledReason = invoicePayment1.canceledReason,
                            approvedAt = invoicePayment1.approvedAt,
                            reason = invoicePayment1.reason,
                            method = invoicePayment1.method,
                            externalId = invoicePayment1.externalId
                        ),
                        CompanyMemberInvoiceGroupPayment(
                            id = invoicePayment2.id,
                            totalAmount = invoicePayment2.amount,
                            status = invoicePayment2.status,
                            invoiceGroupId = invoicePayment2.invoiceGroupId,
                            paymentDetail = invoicePayment2.paymentDetail,
                            createdAt = invoicePayment2.createdAt,
                            canceledReason = invoicePayment2.canceledReason,
                            approvedAt = invoicePayment2.approvedAt,
                            reason = invoicePayment2.reason,
                            method = invoicePayment2.method,
                            externalId = invoicePayment2.externalId
                        ),
                    )
                ),
                CompanyMemberInvoiceGroupInfo(
                    id = memberInvoiceGroup2.id,
                    externalId = memberInvoiceGroup2.externalId!!,
                    billingAccountablePartyId = memberInvoiceGroup2.billingAccountablePartyId,
                    referenceDate = memberInvoiceGroup2.referenceDate,
                    dueDate = memberInvoiceGroup2.dueDate,
                    status = memberInvoiceGroup2.status,
                    type = memberInvoiceGroup2.type,
                    payments = listOf(
                        CompanyMemberInvoiceGroupPayment(
                            id = invoicePayment3.id,
                            totalAmount = invoicePayment3.amount,
                            status = invoicePayment3.status,
                            invoiceGroupId = invoicePayment3.invoiceGroupId,
                            paymentDetail = invoicePayment3.paymentDetail,
                            createdAt = invoicePayment3.createdAt,
                            canceledReason = invoicePayment3.canceledReason,
                            approvedAt = invoicePayment3.approvedAt,
                            reason = invoicePayment3.reason,
                            method = invoicePayment3.method,
                            externalId = invoicePayment3.externalId
                        ),
                    )
                )
            )
        )

        val invoicePayments = listOf(
            invoicePayment1, invoicePayment2, invoicePayment3
        )

        coEvery { companyService.get(any()) } returns company
        coEvery { companySubcontractService.get(any()) } returns companySubcontract
        coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
        coEvery { memberInvoiceGroupService.getBySubcontractId(any()) } returns listOf(
            memberInvoiceGroup1,
            memberInvoiceGroup2
        )
        coEvery { invoicePaymentService.getByInvoiceGroupIds(any(), any()) } returns listOf(
            invoicePayment1, invoicePayment2, invoicePayment3
        )
        coEvery { portalUrlGeneratorService.mountPortalUrlForInvoicePayments(invoicePayments) } returns emptyList()

        val result = getCompanyInvoicesUseCase.getBySubcontract(companySubcontract.id).get()

        assertEquals(expected.billingAccountableParty.id, result.billingAccountableParty.id)
        assertEquals(expected.cnpj, result.cnpj)
        assertEquals(expected.email, result.email)
        assertEquals(
            expected.memberInvoiceGroup.first().payments.first().totalAmount,
            result.memberInvoiceGroup.first().payments.first().totalAmount
        )

        coVerifyOnce { companyService.get(company.id) }
        coVerifyOnce { companySubcontractService.get(companySubcontract.id) }
        coVerifyOnce { billingAccountablePartyService.get(companySubcontract.billingAccountablePartyId!!) }
        coVerifyOnce { memberInvoiceGroupService.getBySubcontractId(companySubcontract.id) }
        coVerifyOnce {
            invoicePaymentService.getByInvoiceGroupIds(
                listOf(memberInvoiceGroup1.id, memberInvoiceGroup2.id),
                true
            )
        }
        coVerifyOnce { portalUrlGeneratorService.mountPortalUrlForInvoicePayments(any()) }

        coVerifyNone { companyContractService.get(any()) }
    }

    @Test
    fun `#getByCompanySubcontract - should return CompanyInvoiceResponse by a subcontract and return portalUrl when source is ITAU`() =
        runBlocking {
            val product = TestModelFactory.buildProduct()
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val company = TestModelFactory.buildCompany(
                availableProducts = listOf(product.id),
                defaultProductId = product.id,
                billingAccountablePartyId = billingAccountableParty.id
            )
            val companySubcontract = TestModelFactory.buildCompanySubContract(
                companyId = company.id,
                billingAccountablePartyId = billingAccountableParty.id
            )

            val memberInvoiceGroup1 =
                TestModelFactory.buildMemberInvoiceGroup(
                    billingAccountablePartyId = company.billingAccountablePartyId!!,
                    type = MemberInvoiceType.B2B_FIRST_PAYMENT,
                )
            val memberInvoiceGroup2 =
                TestModelFactory.buildMemberInvoiceGroup(
                    billingAccountablePartyId = company.billingAccountablePartyId!!,
                    type = MemberInvoiceType.B2B_REGULAR_PAYMENT,
                )

            val invoicePayment1 = TestModelFactory.buildInvoicePayment(
                invoiceGroupId = memberInvoiceGroup1.id,
                source = InvoicePaymentSource.ITAU
            )
            val invoicePayment2 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
            val invoicePayment3 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup2.id)

            val expected = CompanyInvoiceResponse(
                id = company.id,
                name = company.name,
                legalName = company.legalName,
                cnpj = company.cnpj,
                email = company.email,
                phoneNumber = company.phoneNumber,
                address = company.address,
                billingAccountableParty = CompanyBillingAccountableParty(
                    id = billingAccountableParty.id,
                    firstName = billingAccountableParty.firstName,
                    lastName = billingAccountableParty.lastName,
                    type = billingAccountableParty.type,
                    nationalId = billingAccountableParty.nationalId,
                    email = billingAccountableParty.email,
                ),
                memberInvoiceGroup = listOf(
                    CompanyMemberInvoiceGroupInfo(
                        id = memberInvoiceGroup1.id,
                        externalId = memberInvoiceGroup1.externalId!!,
                        billingAccountablePartyId = memberInvoiceGroup1.billingAccountablePartyId,
                        referenceDate = memberInvoiceGroup1.referenceDate,
                        dueDate = memberInvoiceGroup1.dueDate,
                        status = memberInvoiceGroup1.status,
                        type = memberInvoiceGroup1.type,
                        payments = listOf(
                            CompanyMemberInvoiceGroupPayment(
                                id = invoicePayment1.id,
                                totalAmount = invoicePayment1.amount,
                                status = invoicePayment1.status,
                                invoiceGroupId = invoicePayment1.invoiceGroupId,
                                paymentDetail = invoicePayment1.paymentDetail,
                                createdAt = invoicePayment1.createdAt,
                                canceledReason = invoicePayment1.canceledReason,
                                approvedAt = invoicePayment1.approvedAt,
                                reason = invoicePayment1.reason,
                                method = invoicePayment1.method,
                                externalId = invoicePayment1.externalId,
                                portalUrl = "portalUrl"
                            ),
                            CompanyMemberInvoiceGroupPayment(
                                id = invoicePayment2.id,
                                totalAmount = invoicePayment2.amount,
                                status = invoicePayment2.status,
                                invoiceGroupId = invoicePayment2.invoiceGroupId,
                                paymentDetail = invoicePayment2.paymentDetail,
                                createdAt = invoicePayment2.createdAt,
                                canceledReason = invoicePayment2.canceledReason,
                                approvedAt = invoicePayment2.approvedAt,
                                reason = invoicePayment2.reason,
                                method = invoicePayment2.method,
                                externalId = invoicePayment2.externalId
                            ),
                        )
                    ),
                    CompanyMemberInvoiceGroupInfo(
                        id = memberInvoiceGroup2.id,
                        externalId = memberInvoiceGroup2.externalId!!,
                        billingAccountablePartyId = memberInvoiceGroup2.billingAccountablePartyId,
                        referenceDate = memberInvoiceGroup2.referenceDate,
                        dueDate = memberInvoiceGroup2.dueDate,
                        status = memberInvoiceGroup2.status,
                        type = memberInvoiceGroup2.type,
                        payments = listOf(
                            CompanyMemberInvoiceGroupPayment(
                                id = invoicePayment3.id,
                                totalAmount = invoicePayment3.amount,
                                status = invoicePayment3.status,
                                invoiceGroupId = invoicePayment3.invoiceGroupId,
                                paymentDetail = invoicePayment3.paymentDetail,
                                createdAt = invoicePayment3.createdAt,
                                canceledReason = invoicePayment3.canceledReason,
                                approvedAt = invoicePayment3.approvedAt,
                                reason = invoicePayment3.reason,
                                method = invoicePayment3.method,
                                externalId = invoicePayment3.externalId
                            ),
                        )
                    )
                )
            )

            val invoicePayments = listOf(
                invoicePayment1, invoicePayment2, invoicePayment3
            )
            val invoicePaymentsWithPortalUrl = listOf(
                InvoicePaymentWithPortalUrl(
                    invoicePayment1.id,
                    "portalUrl"
                )
            )

            coEvery { companyService.get(any()) } returns company
            coEvery { companySubcontractService.get(any()) } returns companySubcontract
            coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
            coEvery { memberInvoiceGroupService.getBySubcontractId(any()) } returns listOf(
                memberInvoiceGroup1,
                memberInvoiceGroup2
            )
            coEvery { invoicePaymentService.getByInvoiceGroupIds(any(), any()) } returns listOf(
                invoicePayment1, invoicePayment2, invoicePayment3
            )
            coEvery { portalUrlGeneratorService.mountPortalUrlForInvoicePayments(invoicePayments) } returns invoicePaymentsWithPortalUrl

            val result = getCompanyInvoicesUseCase.getBySubcontract(companySubcontract.id).get()

            assertEquals(expected.billingAccountableParty.id, result.billingAccountableParty.id)
            assertEquals(expected.cnpj, result.cnpj)
            assertEquals(expected.email, result.email)
            assertEquals(
                expected.memberInvoiceGroup.first().payments.first().totalAmount,
                result.memberInvoiceGroup.first().payments.first().totalAmount
            )

            coVerifyOnce { companyService.get(company.id) }
            coVerifyOnce { companySubcontractService.get(companySubcontract.id) }
            coVerifyOnce { billingAccountablePartyService.get(companySubcontract.billingAccountablePartyId!!) }
            coVerifyOnce { memberInvoiceGroupService.getBySubcontractId(companySubcontract.id) }
            coVerifyOnce {
                invoicePaymentService.getByInvoiceGroupIds(
                    listOf(memberInvoiceGroup1.id, memberInvoiceGroup2.id),
                    true
                )
            }
            coVerifyOnce {
                portalUrlGeneratorService.mountPortalUrlForInvoicePayments(any())
            }

            coVerifyNone { companyContractService.get(any()) }
        }

    @Test
    fun `#getByCompanySubcontract - should return CompanyInvoiceResponse by a subcontract getting billingAccountableParty by contract`() =
        runBlocking {
            val product = TestModelFactory.buildProduct()
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val company = TestModelFactory.buildCompany(
                availableProducts = listOf(product.id),
                defaultProductId = product.id,
                billingAccountablePartyId = billingAccountableParty.id
            )
            val companySubcontract =
                TestModelFactory.buildCompanySubContract(companyId = company.id, billingAccountablePartyId = null)
            val companyContract = TestModelFactory.buildCompanyContract(
                id = companySubcontract.contractId,
                billingAccountablePartyId = billingAccountableParty.id
            )
            val memberInvoiceGroup1 =
                TestModelFactory.buildMemberInvoiceGroup(billingAccountablePartyId = company.billingAccountablePartyId!!)
            val memberInvoiceGroup2 =
                TestModelFactory.buildMemberInvoiceGroup(billingAccountablePartyId = company.billingAccountablePartyId!!)

            val invoicePayment1 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
            val invoicePayment2 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
            val invoicePayment3 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup2.id)

            val expected = CompanyInvoiceResponse(
                id = company.id,
                name = company.name,
                legalName = company.legalName,
                cnpj = company.cnpj,
                email = company.email,
                phoneNumber = company.phoneNumber,
                address = company.address,
                billingAccountableParty = CompanyBillingAccountableParty(
                    id = billingAccountableParty.id,
                    firstName = billingAccountableParty.firstName,
                    lastName = billingAccountableParty.lastName,
                    type = billingAccountableParty.type,
                    nationalId = billingAccountableParty.nationalId,
                    email = billingAccountableParty.email,
                ),
                memberInvoiceGroup = listOf(
                    CompanyMemberInvoiceGroupInfo(
                        id = memberInvoiceGroup1.id,
                        externalId = memberInvoiceGroup1.externalId!!,
                        billingAccountablePartyId = memberInvoiceGroup1.billingAccountablePartyId,
                        referenceDate = memberInvoiceGroup1.referenceDate,
                        dueDate = memberInvoiceGroup1.dueDate,
                        status = memberInvoiceGroup1.status,
                        payments = listOf(
                            CompanyMemberInvoiceGroupPayment(
                                id = invoicePayment1.id,
                                totalAmount = invoicePayment1.amount,
                                status = invoicePayment1.status,
                                invoiceGroupId = invoicePayment1.invoiceGroupId,
                                paymentDetail = invoicePayment1.paymentDetail,
                                createdAt = invoicePayment1.createdAt,
                                canceledReason = invoicePayment1.canceledReason,
                                approvedAt = invoicePayment1.approvedAt,
                                reason = invoicePayment1.reason,
                                method = invoicePayment1.method,
                                externalId = invoicePayment1.externalId
                            ),
                            CompanyMemberInvoiceGroupPayment(
                                id = invoicePayment2.id,
                                totalAmount = invoicePayment2.amount,
                                status = invoicePayment2.status,
                                invoiceGroupId = invoicePayment2.invoiceGroupId,
                                paymentDetail = invoicePayment2.paymentDetail,
                                createdAt = invoicePayment2.createdAt,
                                canceledReason = invoicePayment2.canceledReason,
                                approvedAt = invoicePayment2.approvedAt,
                                reason = invoicePayment2.reason,
                                method = invoicePayment2.method,
                                externalId = invoicePayment2.externalId
                            ),
                        )
                    ),
                    CompanyMemberInvoiceGroupInfo(
                        id = memberInvoiceGroup2.id,
                        externalId = memberInvoiceGroup2.externalId!!,
                        billingAccountablePartyId = memberInvoiceGroup2.billingAccountablePartyId,
                        referenceDate = memberInvoiceGroup2.referenceDate,
                        dueDate = memberInvoiceGroup2.dueDate,
                        status = memberInvoiceGroup2.status,
                        payments = listOf(
                            CompanyMemberInvoiceGroupPayment(
                                id = invoicePayment3.id,
                                totalAmount = invoicePayment3.amount,
                                status = invoicePayment3.status,
                                invoiceGroupId = invoicePayment3.invoiceGroupId,
                                paymentDetail = invoicePayment3.paymentDetail,
                                createdAt = invoicePayment3.createdAt,
                                canceledReason = invoicePayment3.canceledReason,
                                approvedAt = invoicePayment3.approvedAt,
                                reason = invoicePayment3.reason,
                                method = invoicePayment3.method,
                                externalId = invoicePayment3.externalId
                            ),
                        )
                    )
                )
            )

            val invoicePayments = listOf(
                invoicePayment1, invoicePayment2, invoicePayment3
            )

            coEvery { companyService.get(any()) } returns company
            coEvery { companySubcontractService.get(any()) } returns companySubcontract
            coEvery { companyContractService.get(companySubcontract.contractId) } returns companyContract
            coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
            coEvery { memberInvoiceGroupService.getBySubcontractId(any()) } returns listOf(
                memberInvoiceGroup1,
                memberInvoiceGroup2
            )
            coEvery { invoicePaymentService.getByInvoiceGroupIds(any(), any()) } returns listOf(
                invoicePayment1, invoicePayment2, invoicePayment3
            )
            coEvery { portalUrlGeneratorService.mountPortalUrlForInvoicePayments(invoicePayments) } returns emptyList()

            val result = getCompanyInvoicesUseCase.getBySubcontract(companySubcontract.id).get()

            assertEquals(expected.billingAccountableParty.id, result.billingAccountableParty.id)
            assertEquals(expected.cnpj, result.cnpj)
            assertEquals(expected.email, result.email)
            assertEquals(
                expected.memberInvoiceGroup.first().payments.first().totalAmount,
                result.memberInvoiceGroup.first().payments.first().totalAmount
            )

            coVerifyOnce { companyService.get(company.id) }
            coVerifyOnce { companySubcontractService.get(companySubcontract.id) }
            coVerifyOnce { billingAccountablePartyService.get(companyContract.billingAccountablePartyId!!) }
            coVerifyOnce { memberInvoiceGroupService.getBySubcontractId(companySubcontract.id) }
            coVerifyOnce {
                invoicePaymentService.getByInvoiceGroupIds(
                    listOf(memberInvoiceGroup1.id, memberInvoiceGroup2.id),
                    true
                )
            }
            coVerifyOnce { companyContractService.get(companySubcontract.contractId) }
            coVerifyOnce { portalUrlGeneratorService.mountPortalUrlForInvoicePayments(any()) }
        }

    @Test
    fun `#getByCompanySubcontract - should fail if any BillingAccountParty is found`() = runBlocking {
        val product = TestModelFactory.buildProduct()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val company = TestModelFactory.buildCompany(
            availableProducts = listOf(product.id),
            defaultProductId = product.id,
            billingAccountablePartyId = billingAccountableParty.id
        )
        val companySubcontract =
            TestModelFactory.buildCompanySubContract(companyId = company.id, billingAccountablePartyId = null)
        val companyContract =
            TestModelFactory.buildCompanyContract(
                id = companySubcontract.contractId,
                billingAccountablePartyId = null
            )
        val memberInvoiceGroup1 =
            TestModelFactory.buildMemberInvoiceGroup(billingAccountablePartyId = company.billingAccountablePartyId!!)
        val memberInvoiceGroup2 =
            TestModelFactory.buildMemberInvoiceGroup(billingAccountablePartyId = company.billingAccountablePartyId!!)

        val invoicePayment1 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
        val invoicePayment2 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup1.id)
        val invoicePayment3 = TestModelFactory.buildInvoicePayment(invoiceGroupId = memberInvoiceGroup2.id)


        coEvery { companyService.get(any()) } returns company
        coEvery { companySubcontractService.get(any()) } returns companySubcontract
        coEvery { companyContractService.get(companySubcontract.contractId) } returns companyContract
        coEvery { billingAccountablePartyService.get(any()) } returns billingAccountableParty
        coEvery { memberInvoiceGroupService.getBySubcontractId(any()) } returns listOf(
            memberInvoiceGroup1,
            memberInvoiceGroup2
        )
        coEvery { invoicePaymentService.getByInvoiceGroupIds(any(), any()) } returns listOf(
            invoicePayment1, invoicePayment2, invoicePayment3
        )


        val result = getCompanyInvoicesUseCase.getBySubcontract(companySubcontract.id)

        ResultAssert.assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { companyService.get(company.id) }
        coVerifyOnce { companySubcontractService.get(companySubcontract.id) }
        coVerifyOnce { memberInvoiceGroupService.getBySubcontractId(companySubcontract.id) }
        coVerifyOnce {
            invoicePaymentService.getByInvoiceGroupIds(
                listOf(memberInvoiceGroup1.id, memberInvoiceGroup2.id),
                true
            )
        }
        coVerifyOnce { companyContractService.get(companySubcontract.contractId) }
        coVerifyNone { billingAccountablePartyService.get(any()) }
    }


}
