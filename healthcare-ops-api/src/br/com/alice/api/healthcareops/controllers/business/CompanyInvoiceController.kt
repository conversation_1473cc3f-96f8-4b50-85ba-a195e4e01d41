package br.com.alice.api.healthcareops.controllers.business

import br.com.alice.api.healthcareops.models.CompanyInvoiceResponse
import br.com.alice.api.healthcareops.models.CreateFirstInvoicePaymentRequest
import br.com.alice.api.healthcareops.models.CreateInvoicePaymentRequest
import br.com.alice.api.healthcareops.usecases.CancelInvoicePayload
import br.com.alice.api.healthcareops.usecases.CreateInvoicePayload
import br.com.alice.api.healthcareops.usecases.CreateInvoicePaymentPaylod
import br.com.alice.api.healthcareops.usecases.GetCompanyInvoicesUseCase
import br.com.alice.api.healthcareops.usecases.PreActivationCompanyInvoiceUseCase
import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.yearMonthFormatter
import br.com.alice.common.currentUserId
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.foldNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.CancellationReason
import br.com.alice.moneyin.client.InvoicePaymentService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success
import kotlinx.coroutines.coroutineScope
import java.time.LocalDate
import java.util.UUID

class CompanyInvoiceController(
    private val invoicePaymentService: InvoicePaymentService,
    private val getCompanyInvoicesUseCase: GetCompanyInvoicesUseCase,
    private val preActivationCompanyInvoiceUseCase: PreActivationCompanyInvoiceUseCase,
    private val companySubcontractService: CompanySubContractService,
    private val companyContractService: CompanyContractService,
) : Controller() {
    suspend fun getInvoiceInfoByCompanyId(companyId: UUID): Response = coroutineScope {
        getCompanyInvoicesUseCase.getByBillingAccountablePartyFromCompany(companyId)
            .foldNotFound { emptyList<CompanyInvoiceResponse>().success() }
    }.foldResponse()

    suspend fun approveInvoicePayment(invoicePaymentId: UUID): Response =
        logger.info(
            "Approving InvoicePayment",
            "invoice_payment_id" to invoicePaymentId,
            "current_staff_id" to currentUserId(),
        )
            .let { invoicePaymentService.approve(invoicePaymentId) }
            .then { logger.info("Invoice Approved", "invoice_payment_id" to invoicePaymentId) }
            .thenError {
                logger.error(
                    "Invoice Approving failed",
                    "invoice_payment_id" to invoicePaymentId,
                    "error" to it,
                )
            }
            .foldResponse()


    suspend fun cancelInvoicePayment(invoicePaymentId: UUID): Response =
        logger.info(
            "Canceling InvoicePayment",
            "invoice_payment_id" to invoicePaymentId,
            "current_staff_id" to currentUserId(),
        )
            .let { invoicePaymentService.cancel(invoicePaymentId, CancellationReason.INVALID) }
            .then { logger.info("Invoice canceled", "invoice_payment_id" to invoicePaymentId) }
            .thenError {
                logger.error(
                    "Invoice canceled failed",
                    "invoice_payment_id" to invoicePaymentId,
                    "error" to it
                )
            }
            .foldResponse()

    suspend fun createPaymentForPreActivation(
        subContractId: UUID,
        preActivationInvoiceId: UUID,
        request: CreateInvoicePaymentRequest
    ): Response =
        logger.info(
            "Creating PreActivationCompanyInvoice for MemberInvoiceGroup",
            "member_invoice_group_id" to preActivationInvoiceId,
            "request" to request,
            "current_staff_id" to currentUserId(),
        )
            .let {
                companySubcontractService.get(subContractId)
                    .flatMap { companyContractService.get(it.contractId) }
            }
            .flatMap { contract ->
                preActivationCompanyInvoiceUseCase.createInvoicePayment(
                    CreateInvoicePaymentPaylod(
                        preActivationInvoiceId = preActivationInvoiceId,
                        paymentMethod = request.paymentMethod,
                        dueDate = request.dueDate?.let { LocalDate.parse(it) },
                        groupCompany = contract.groupCompany,
                    )
                )
            }
            .then { logger.info("InvoicePayment creation", "member_invoice_group_id" to preActivationInvoiceId) }
            .thenError {
                logger.error(
                    "InvoicePayment creation failed",
                    "member_invoice_group_id" to preActivationInvoiceId,
                    "error" to it,
                )
            }
            .foldResponse()

    suspend fun cancelPreActivationInvoice(subContractId: UUID, preActivationInvoiceId: UUID): Response =
        logger.info(
            "Canceling PreActivationCompanyInvoice for subcontract",
            "sub_contract_id" to subContractId,
            "pre_activation_invoice_id" to preActivationInvoiceId,
            "current_staff_id" to currentUserId(),
        )
            .let {
                companySubcontractService.get(subContractId)
                    .flatMap { companyContractService.get(it.contractId) }
            }.flatMap { companyContract ->
                preActivationCompanyInvoiceUseCase.cancel(
                    CancelInvoicePayload(
                        preActivationInvoiceId,
                        companyContract.groupCompany
                    )
                )
            }.thenError { error ->
                logger.error(
                    "Error canceling firstInvoicePayment for subcontract",
                    "error" to error,
                )
            }
            .foldResponse()

    suspend fun createPreActivationInvoice(subContractId: UUID, request: CreateFirstInvoicePaymentRequest): Response =
        logger.info(
            "Creating PreActivationCompanyInvoice for subcontract",
            "sub_contract_id" to subContractId,
            "current_staff_id" to currentUserId(),
            "request" to request
        )
            .let {
                companySubcontractService.get(subContractId)
                    .flatMapPair { companyContractService.get(it.contractId) }
            }
            .flatMap { (companyContract, companySubContract) ->
                preActivationCompanyInvoiceUseCase.create(
                    CreateInvoicePayload(
                        companyContract = companyContract,
                        companySubContract = companySubContract,
                        referenceDate = LocalDate.parse(request.referenceDate, yearMonthFormatter),
                        dueDate = LocalDate.parse(request.dueDate),
                        paymentMethod = request.paymentMethod,
                    )
                )
            }
            .thenError { error ->
                logger.error(
                    "Error canceling PreActivationCompanyInvoice for subcontract",
                    "error" to error,
                )
            }
            .foldResponse()

    suspend fun cancelFirstInvoicePayment(memberInvoiceGroupId: UUID): Response =
        logger.info(
            "Canceling firstInvoicePayment for MemberInvoiceGroup",
            "member_invoice_group_id" to memberInvoiceGroupId,
            "current_staff_id" to currentUserId(),
        )
            .let {
                preActivationCompanyInvoiceUseCase.cancel(
                    CancelInvoicePayload(
                        memberInvoiceGroupId,
                        null
                    )
                )
            }
            .then { logger.info("FirstInvoicePayment canceled", "member_invoice_group_id" to memberInvoiceGroupId) }
            .thenError {
                logger.error(
                    "FirstInvoicePayment canceled failed",
                    "member_invoice_group_id" to memberInvoiceGroupId,
                    "error" to it,
                )
            }
            .foldResponse()

    suspend fun createInvoicePayment(memberInvoiceGroupId: UUID, request: CreateInvoicePaymentRequest): Response =
        logger.info(
            "Creating invoicePayment for MemberInvoiceGroup",
            "member_invoice_group_id" to memberInvoiceGroupId,
            "request" to request,
            "current_staff_id" to currentUserId(),
        ).let {

            preActivationCompanyInvoiceUseCase.createInvoicePayment(
                CreateInvoicePaymentPaylod(
                    preActivationInvoiceId = memberInvoiceGroupId,
                    paymentMethod = request.paymentMethod,
                    dueDate = request.dueDate?.let { LocalDate.parse(it) },
                    groupCompany = null,
                )
            )
        }
            .then { logger.info("InvoicePayment creation", "member_invoice_group_id" to memberInvoiceGroupId) }
            .thenError {
                logger.error(
                    "InvoicePayment creation failed",
                    "member_invoice_group_id" to memberInvoiceGroupId,
                    "error" to it,
                )
            }
            .foldResponse()
}
