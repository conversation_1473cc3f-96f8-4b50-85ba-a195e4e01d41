package br.com.alice.api.healthcareops.controllers.moneyin.v2.company

import br.com.alice.api.healthcareops.usecases.GetCompanyInvoicesUseCase
import br.com.alice.common.Response
import br.com.alice.common.toResponse
import java.util.UUID

class CompanyInvoicesControllerV2(
    private val getCompanyInvoicesUseCase: GetCompanyInvoicesUseCase,
) {
    suspend fun listByBillingAccountableParty(companyId: UUID,): Response {
        return getCompanyInvoicesUseCase.getByBillingAccountablePartyFromCompanyV2(companyId)
            .toResponse()
    }

    suspend fun listBySubContractId(companyId: UUID, subContractId: UUID): Response {
        return getCompanyInvoicesUseCase.getBySubcontractV2(subContractId)
            .toResponse()
    }
}
