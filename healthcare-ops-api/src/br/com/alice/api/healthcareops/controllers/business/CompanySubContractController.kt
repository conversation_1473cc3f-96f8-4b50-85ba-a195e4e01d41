package br.com.alice.api.healthcareops.controllers.business

import br.com.alice.api.healthcareops.converters.business.CompanySubContractConverter.toCompanySubContract
import br.com.alice.api.healthcareops.converters.business.CompanySubContractConverter.toResponse
import br.com.alice.api.healthcareops.converters.business.CompanySubContractConverter.updateConverter
import br.com.alice.api.healthcareops.models.CompanyProductPriceListingRequest
import br.com.alice.api.healthcareops.models.CompanySubContractRequest
import br.com.alice.api.healthcareops.models.CompanySubContractResponse
import br.com.alice.api.healthcareops.usecases.GetCompanyInvoicesUseCase
import br.com.alice.business.client.CompanyProductPriceListingService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.RequiredFieldException
import br.com.alice.common.core.extensions.isNotNullOrBlank
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.currentUserId
import br.com.alice.common.extensions.coFoldDuplicated
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.BillingAccountableParty
import br.com.alice.data.layer.models.CompanyProductPriceListing
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Product
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDate
import java.util.UUID

class CompanySubContractController(
    private val companySubContractService: CompanySubContractService,
    private val billingAccountablePartyService: BillingAccountablePartyService,
    private val companyProductPriceListingService: CompanyProductPriceListingService,
    private val getCompanyInvoicesUseCase: GetCompanyInvoicesUseCase,
    private val productService: ProductService,
) : Controller() {
    suspend fun createCompanySubContract(
        companyId: UUID,
        contractId: UUID,
        request: CompanySubContractRequest
    ): Response {
        logger.info(
            "Create company subcontract company route", "current_staff_id" to currentUserId(),
        )
        logger.info("#createCompanySubContract - validating request")
        val productsMap = getProductsMapForCompanyProductPriceListing(request.companyProductPriceListing)
        validateCompanySubContractRequest(request, productsMap)

        val billingAccountableParty = getOrCreateBillingAccountableParty(request)

        val request = request.updateBillingAccountablePartyId(billingAccountableParty)
        return companySubContractService.add(request.toCompanySubContract(companyId, contractId))
            .map {
                Pair(
                    it,
                    createCompanyProductPriceListingForSubContract(
                        request.companyProductPriceListing,
                        it,
                        productsMap,
                    )
                )
            }
            .map { (subContract, companyPriceListing) ->
                subContract.copy(avaliableCompanyProductPriceListing = companyPriceListing?.map { it.id })
                    .toResponse(billingAccountableParty)
            }
            .then { logger.info("CompanySubContract created", "company_sub_contract" to it) }
            .thenError { logger.error("createCompanySubContract process failed", "ex" to it, "request" to request) }
            .foldResponse()
    }

    private suspend fun getProductsMapForCompanyProductPriceListing(request: List<CompanyProductPriceListingRequest>?) =
        request?.let { req -> productService.findByIds(req.map { it.productId }.distinct()) }
            ?.map { products -> products.associateBy { it.id } }
            ?.get()


    private suspend fun createCompanyProductPriceListingForSubContract(
        companyProductPriceListing: List<CompanyProductPriceListingRequest>?,
        companySubContract: CompanySubContract,
        productsMap: Map<UUID, Product>?,
    ): List<CompanyProductPriceListing> {
        if (companyProductPriceListing.isNullOrEmpty()) return emptyList()
        return companyProductPriceListing.map {
            val product = productsMap!![it.productId]!!
            CompanyProductPriceListing(
                companySubContractId = companySubContract.id,
                productId = it.productId,
                companyId = companySubContract.companyId,
                priceListingId = null,
                startDate = LocalDate.now(),
                ansNumber = product.ansNumber!!,
                priceListItems = emptyList()
            )
        }.let {
            companyProductPriceListingService.addListToSubContract(
                it,
                companySubContract
            ).get()
        }
    }

    private fun CompanySubContract.toNewProductPriceListing(companyProductPriceListing: List<CompanyProductPriceListing>? = null) =
        if (companyProductPriceListing != null) ((this.avaliableCompanyProductPriceListing
            ?: emptyList()) + companyProductPriceListing.map { it.id }).distinct()
        else this.avaliableCompanyProductPriceListing

    private suspend fun getOrCreateBillingAccountableParty(request: CompanySubContractRequest) = when {
        (request.billingAccountablePartyId != null) ->
            billingAccountablePartyService.get(request.billingAccountablePartyId)
                .get()

        (request.billingAccountableParty != null && request.billingAccountableParty.nationalId.isNotNullOrBlank()) -> {
            val billingAccountableParty = BillingAccountableParty(
                firstName = request.billingAccountableParty.firstName!!,
                lastName = request.billingAccountableParty.lastName!!,
                type = request.billingAccountableParty.type,
                nationalId = request.billingAccountableParty.nationalId,
                address = request.billingAccountableParty.address,
                email = request.billingAccountableParty.email,
            )

            billingAccountablePartyService.add(billingAccountableParty)
                .coFoldDuplicated { billingAccountablePartyService.findNationalIdEq(request.billingAccountableParty.nationalId) }
                .get()
        }

        else -> null
    }

    private fun validateCompanySubContractRequest(
        request: CompanySubContractRequest,
        productsMap: Map<UUID, Product>?
    ) {
        validateTitle(request.title)
        productsMap?.let { validateCompanyProductPriceListing(it) }
    }

    private suspend fun canEdit() =
        FeatureService.inList(
            FeatureNamespace.HEALTHCARE_OPS,
            "can_edit_contract",
            currentUserEmail() ?: "",
            false
        )

    suspend fun updateCompanySubContract(id: UUID, request: CompanySubContractRequest): Response {
        logger.info(
            "Update company subcontract route", "current_staff_id" to currentUserId(),
        )
        logger.info("#updateCompanySubContract - validating request")
        request.companyProductPriceListing?.let { cpplsRequest ->
            val hasDuplicated = cpplsRequest.groupBy { it.ansNumber }.any { it.value.size > 1 }
            if (hasDuplicated) {
                throw InvalidArgumentException("Duplicated ansNumber found")
            }
        }

        val productsMap = getProductsMapForCompanyProductPriceListing(request.companyProductPriceListing)
        validateCompanySubContractRequest(request, productsMap)

        val billingAccountableParty = getOrCreateBillingAccountableParty(request)
        val canEdit = canEdit()

        val request = request.updateBillingAccountablePartyId(billingAccountableParty)

        return companySubContractService.get(id)
            .map {
                val cpplsToCreate = request.companyProductPriceListing?.filter { cppl -> cppl.id == null }
                val subcontract =
                    if (cpplsToCreate.isNotNullOrEmpty()) it.copy(
                        version = it.version + (cpplsToCreate?.let { 1 } ?: 0)
                    ) else it
                subcontract to createCompanyProductPriceListingForSubContract(
                    cpplsToCreate,
                    it,
                    productsMap,
                )
            }
            .map { (subContract, productPriceListing) ->
                subContract.updateConverter(
                    request,
                    subContract.toNewProductPriceListing(productPriceListing),
                    canEdit,
                )
            }
            .flatMap { companySubContractService.update(it) }
            .then { logger.info("CompanySubContract updated", "company_sub_contract_id" to id) }
            .thenError { logger.error("CompanySubContract update failed", "ex" to it, "request" to request) }
            .map { it.toResponse(billingAccountableParty) }
            .foldResponse()
    }

    private fun CompanySubContractRequest.updateBillingAccountablePartyId(billingAccountableParty: BillingAccountableParty?) =
        this.copy(
            billingAccountablePartyId = this.billingAccountableParty?.let { billingAccountableParty?.id }
                ?: this.billingAccountablePartyId
        )

    private suspend fun enrichSubcontract(
        subcontracts: List<CompanySubContract>,
        withProduct: Boolean = false,
        withPriceListing: Boolean = false
    ): List<CompanySubContractResponse> =
        coroutineScope {
            val billingIds = subcontracts.mapNotNull { it.billingAccountablePartyId }.distinct()
            val subContractIds = subcontracts.map { it.id }

            val billingAccountablePartyMappedDeferred = async { getBillingAccountablePartiesByIds(billingIds) }
            val companyProductPriceListingMappedDeferred = async {
                companyProductPriceListingService.findCurrentBySubContractIds(
                    subContractIds,
                    CompanyProductPriceListingService.FindOptions(
                        withProduct = withProduct,
                        withPriceListing = withPriceListing
                    )
                ).get()
            }

            val billingAccountablePartyMapped = billingAccountablePartyMappedDeferred.await()
            val companyProductPriceListingMapped = companyProductPriceListingMappedDeferred.await()

            subcontracts.map {
                it.toResponse(
                    billingAccountablePartyMapped[it.billingAccountablePartyId],
                    companyProductPriceListingMapped[it.id.toString()] ?: emptyList()
                )
            }
        }

    private suspend fun getBillingAccountablePartiesByIds(ids: List<UUID>) =
        if (ids.isEmpty()) emptyMap() else billingAccountablePartyService.findById(ids)
            .map { it.associateBy { billingAccountableParty -> billingAccountableParty.id } }
            .get()


    private fun validateTitle(title: String) {
        if (title.isEmpty()) throw RequiredFieldException("title")
    }

    private fun validateCompanyProductPriceListing(productsMap: Map<UUID, Product>) {
        if (productsMap.map { it.value.ansNumber }.distinct().size != productsMap.size) {
            throw InvalidArgumentException("AnsNumber must be unique!")
        }
    }

    suspend fun listCompanySubContractByCompanyId(
        companyId: UUID
    ) = companySubContractService
        .findByCompanyId(companyId)
        .map { enrichSubcontract(it) }
        .foldResponse()

    suspend fun getById(companySubcontractId: UUID) =
        companySubContractService.get(companySubcontractId)
            .map { enrichSubcontract(listOf(it), withProduct = true, withPriceListing = true).first() }
            .foldResponse()

    suspend fun listCompanySubContractInvoices(companySubcontractId: UUID) =
        getCompanyInvoicesUseCase.getBySubcontract(companySubcontractId).foldResponse()


    suspend fun deleteCompanyProductPriceListingBySubcontractId(companySubContractId: UUID): Response {
        logger.info(
            "Deleting company product price listing by sub_contract id",
            "sub_contract_id" to companySubContractId,
            "current_staff_id" to currentUserId(),
        )

        return companyProductPriceListingService.clearFromSubContract(companySubContractId)
            .foldResponse()
    }

    suspend fun deleteCompanyProductPriceListingById(cpplId: UUID): Response {
        logger.info(
            "Deleting company product price listing by cppl id",
            "cppl_id" to cpplId,
            "current_staff_id" to currentUserId(),
        )

        return companyProductPriceListingService.get(cpplId)
            .flatMap { cppl -> companyProductPriceListingService.delete(cppl) }
            .foldResponse()
    }
}
