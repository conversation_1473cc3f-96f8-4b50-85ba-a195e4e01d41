package br.com.alice.api.healthcareops.routes

import br.com.alice.api.healthcareops.controllers.moneyin.InvoiceLiquidationController
import br.com.alice.api.healthcareops.controllers.moneyin.InvoicesController
import br.com.alice.api.healthcareops.controllers.moneyin.v2.company.CompanyInvoicesControllerV2
import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route

fun Routing.invoiceRouting() {
    val invoiceLiquidationController by inject<InvoiceLiquidationController>()
    val invoiceController by inject<InvoicesController>()
    authenticate {
        route("/invoices/") {
            route("liquidations") {
                route("/{invoiceLiquidationId}") {
                    post("payments") { coHandler("invoiceLiquidationId", invoiceLiquidationController::createPayment) }
                }
            }

            route("company/{companyId}/liquidations") {
                get("/payments") { coHandler("companyId", invoiceLiquidationController::listByCompanyId) }
            }

            route("people/{personId}/liquidations") {
                get("/payments") { coHandler("personId", invoiceLiquidationController::listByPersonId) }
            }

            route("bank_slip/{invoicePaymentId}") {
                get("/") { coHandler("invoicePaymentId", invoiceController::downloadInvoicePdf) }
                get("/secure") { coHandler("invoicePaymentId", invoiceController::downloadSecureInvoicePdf) }
            }

            route("payments/{paymentId}") {
                put("approve") { coHandler("paymentId", invoiceController::approvePayment) }
                put("/cancel") { coHandler("paymentId", invoiceController::cancelPayment) }
            }
        }

        route("v2/invoices/company/{company_id}") {
            val companyInvoiceController by inject<CompanyInvoicesControllerV2>()

            get {
                coHandler("company_id", companyInvoiceController::listByBillingAccountableParty)
            }
            get("subcontract/{sub_contract_id}") {
                coHandler("company_id", "sub_contract_id", companyInvoiceController::listBySubContractId)
            }
        }
    }
}
