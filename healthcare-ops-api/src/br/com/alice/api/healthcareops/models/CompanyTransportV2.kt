package br.com.alice.api.healthcareops.models

import br.com.alice.data.layer.models.CompanyAddress
import br.com.alice.moneyin.models.BankSlipDetail
import br.com.alice.moneyin.models.CompanyInvoiceModel
import br.com.alice.moneyin.models.CompanyInvoicePaymentStatus
import br.com.alice.moneyin.models.CompanyInvoiceStatus
import br.com.alice.moneyin.models.CompanyInvoiceType
import br.com.alice.moneyin.models.CreditCardDetail
import br.com.alice.moneyin.models.PixDetail
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class CompanyInvoiceResponseV2(
    val id: UUID,
    val name: String,
    val legalName: String,
    val cnpj: String,
    val email: String,
    val phoneNumber: String,
    val address: CompanyAddress,
    val billingAccountableParty: CompanyBillingAccountableParty,
    val invoices: List<CompanyInvoiceInfo>,
)

data class CompanyInvoiceInfo(
    val id: UUID,
    val externalId: String,
    val payments: List<CompanyInvoicePayment>,
    val billingAccountablePartyId: UUID,
    val referenceDate: LocalDate,
    val dueDate: LocalDate,
    val status: CompanyInvoiceStatus,
    val type: CompanyInvoiceType? = null,
    val model: CompanyInvoiceModel,
)

data class CompanyInvoicePayment(
    val id: UUID,
    val totalAmount: BigDecimal,
    val status: CompanyInvoicePaymentStatus,
    val invoiceId: UUID,
    val invoiceModel: CompanyInvoiceModel,
    val pix: PixDetail? = null,
    val bankSlip: BankSlipDetail?,
    val creditCard: CreditCardDetail?,
    val externalId: String?,
    val approvedAt: LocalDateTime?,
    val createdAt: LocalDateTime,
    val method: CompanyInvoicePaymentMethod,
    val portalUrl: String? = null
)

enum class CompanyInvoicePaymentMethod {
    BOLETO,
    SIMPLE_CREDIT_CARD,
    PIX,
    BOLEPIX;
}

data class PixDetail(
    val paymentUrl: String? = null,
    val dueDate: LocalDateTime? = null,
    val paymentCode: String? = null,
    val externalId: String? = null,
)

data class BankSlipDetail(
    val dueDate: LocalDateTime,
    val barcode: String? = null,
    val paymentUrl: String? = null,
    val externalId: String? = null,
)

data class CreditCardDetail(
    val paymentUrl: String,
)

