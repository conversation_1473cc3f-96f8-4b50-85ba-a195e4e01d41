package br.com.alice.bff.business.services

import br.com.alice.bff.business.models.v2.InvoiceFileAvailability
import br.com.alice.bff.business.models.v2.InvoiceFileType
import br.com.alice.business.client.InvoiceGroupTaxReceiptService
import br.com.alice.business.client.InvoiceLiquidationTaxReceiptService
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.service.serialization.simpleGson
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.InvoiceGroupTaxReceipt
import br.com.alice.data.layer.models.InvoiceLiquidationStatus
import br.com.alice.data.layer.models.InvoiceLiquidationTaxReceipt
import br.com.alice.data.layer.models.MemberInvoiceGroupStatus
import br.com.alice.common.PaymentMethod
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.atBeginningOfTheMonth
import br.com.alice.common.core.extensions.atEndOfTheMonth
import br.com.alice.common.core.extensions.money
import br.com.alice.data.layer.models.TaxReceiptStatus
import br.com.alice.moneyin.client.CompanyInvoiceDetailService
import br.com.alice.moneyin.client.InvoiceLiquidationService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.models.CompanyInvoiceModel
import br.com.alice.moneyin.models.CompanyInvoiceResponse
import br.com.alice.moneyin.models.CompanyInvoiceStatus
import br.com.alice.moneyin.models.CompanyInvoiceType
import br.com.alice.moneyin.models.CompanyInvoiceValidityPeriod
import com.github.kittinunf.result.Result
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Nested
import java.io.File
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertContains
import kotlin.test.assertFails
import kotlin.test.assertFailsWith
import kotlin.test.assertNotNull

class InvoiceFilesServiceTest {
    private val memberInvoiceGroupService: MemberInvoiceGroupService = mockk()
    private val invoiceGroupTaxReceiptService: InvoiceGroupTaxReceiptService = mockk()
    private val invoiceLiquidationTaxReceiptService: InvoiceLiquidationTaxReceiptService = mockk()
    private val paymentService: PaymentService = mockk()
    private val invoiceLiquidationService: InvoiceLiquidationService = mockk()
    private val companyInvoiceDetailService: CompanyInvoiceDetailService = mockk()

    private val pdfFile = File(javaClass.classLoader.getResource("test.pdf")!!.path)
    private val xlsxFile = File(javaClass.classLoader.getResource("test.xlsx")!!.path)

    private val invoiceFileAvailabilityService: InvoiceFileAvailabilityService = mockk()
    private val taxAvailabilityService: InvoiceFileAvailabilityService = mockk()

    private val defaultService = InvoiceFilesService(
        memberInvoiceGroupService,
        companyInvoiceDetailService,
        invoiceGroupTaxReceiptService,
        invoiceLiquidationTaxReceiptService,
        paymentService,
        invoiceLiquidationService,
        invoiceFileAvailabilityServices = listOf(invoiceFileAvailabilityService, taxAvailabilityService),
    )

    @Test
    fun `downloadInvoiceDetails should return file successfully`() = runBlocking {
        val invoiceId = UUID.randomUUID()
        val fileBytes = xlsxFile.readBytes()
        coEvery {
            companyInvoiceDetailService.getById(
                invoiceId,
                CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT
            )
        } returns Result.success(
            fileBytes
        )

        val file = defaultService.downloadInvoiceDetails(invoiceId, CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT)

        assertNotNull(file)
        assertContains(file.name, "detalhes-fatura")
    }

    @Test
    fun `downloadInvoiceDetails should throw exception on failure`(): Unit = runBlocking {
        val invoiceId = UUID.randomUUID()
        coEvery {
            companyInvoiceDetailService.getById(
                invoiceId,
                CompanyInvoiceModel.MEMBER_INVOICE_GROUP
            )
        } returns NotFoundException()

        assertFails {
            defaultService.downloadInvoiceDetails(invoiceId)
        }
    }

    @Test
    fun `downloadTaxReceipt should return file successfully`() = runBlocking {
        val invoiceId = UUID.randomUUID()
        val pdfUrl = "http://example.com/test.pdf"
        val receipt = InvoiceGroupTaxReceipt(
            issuedAt = LocalDateTime.now(),
            invoiceGroupId = invoiceId,
            pdfUrl = pdfUrl,
            id = UUID.randomUUID(),
            status = TaxReceiptStatus.ISSUED
        )

        coEvery {
            invoiceGroupTaxReceiptService.getIssuedByInvoiceGroupIds(
                listOf(invoiceId),
                any()
            )
        } returns Result.success(listOf(receipt))

        val client = httpClientMock(
            responseByteArray = pdfFile.readBytes(),
            url = receipt.pdfUrl,
            httpMethod = HttpMethod.Get
        )

        val service = InvoiceFilesService(
            memberInvoiceGroupService,
            companyInvoiceDetailService,
            invoiceGroupTaxReceiptService,
            invoiceLiquidationTaxReceiptService,
            paymentService,
            invoiceLiquidationService,
            client,
            listOf(invoiceFileAvailabilityService),
        )

        val file = service.downloadTaxReceipt(invoiceId)

        assertNotNull(file)
        assertContains(file.name, "nota-fiscal")
    }

    @Test
    fun `downloadTaxReceipt should throw exception when receipt not found`(): Unit = runBlocking {
        val invoiceId = UUID.randomUUID()
        coEvery {
            invoiceGroupTaxReceiptService.getIssuedByInvoiceGroupIds(
                listOf(invoiceId),
                any()
            )
        } returns NotFoundException()

        assertFailsWith<IllegalArgumentException> {
            defaultService.downloadTaxReceipt(invoiceId)
        }
    }

    @Test
    fun `downloadBoleto should return file successfully`() = runBlocking {
        val invoiceId = UUID.randomUUID()
        val pdfUrl = "http://example.com/invoices/$invoiceId/test.pdf"
        val invoice =
            TestModelFactory.buildMemberInvoiceGroup(id = invoiceId, status = MemberInvoiceGroupStatus.WAITING_PAYMENT)
        val payment = TestModelFactory.buildInvoicePayment(
            id = UUID.randomUUID(),
            memberInvoiceIds = listOf(invoiceId),
            paymentDetail = TestModelFactory.buildBoletoPaymentDetail(paymentUrl = pdfUrl),
            method = PaymentMethod.BOLEPIX
        )

        coEvery { memberInvoiceGroupService.get(invoiceId) } returns Result.success(invoice)
        coEvery { paymentService.getPendingOrCreatePayment(invoice) } returns Result.success(payment)

        val client = httpClientMock(
            responseByteArray = pdfFile.readBytes(),
            url = pdfUrl,
            httpMethod = HttpMethod.Get
        )

        val service = InvoiceFilesService(
            memberInvoiceGroupService,
            companyInvoiceDetailService,
            invoiceGroupTaxReceiptService,
            invoiceLiquidationTaxReceiptService,
            paymentService,
            invoiceLiquidationService,
            client,
            listOf(invoiceFileAvailabilityService),
        )

        val file = service.downloadBoleto(invoiceId)

        assertNotNull(file)
        assertContains(file.name, "boleto")
    }

    @Test
    fun `downloadBoleto should throw exception when invoice not found`(): Unit = runBlocking {
        val invoiceId = UUID.randomUUID()
        coEvery { memberInvoiceGroupService.get(invoiceId) } returns NotFoundException()

        assertFailsWith<IllegalArgumentException> {
            defaultService.downloadBoleto(invoiceId)
        }
    }

    @Test
    fun `downloadBoleto should throw exception when invoice is paid`(): Unit = runBlocking {
        val invoiceId = UUID.randomUUID()
        val invoice = TestModelFactory.buildMemberInvoiceGroup(id = invoiceId, status = MemberInvoiceGroupStatus.PAID)
        coEvery { memberInvoiceGroupService.get(invoiceId) } returns invoice

        assertFailsWith<IllegalArgumentException> {
            defaultService.downloadBoleto(invoiceId)
        }
    }

    @Nested
    inner class DownloadBoletoFromLiquidation {
        @Test
        fun `should return file successfully`() = runBlocking {
            val liquidationId = UUID.randomUUID()
            val pdfUrl = "http://example.com/invoices/$liquidationId/test.pdf"
            val liquidation =
                TestModelFactory.buildInvoiceLiquidation(
                    id = liquidationId,
                )
            val payment = TestModelFactory.buildInvoicePayment(
                id = UUID.randomUUID(),
                memberInvoiceIds = listOf(liquidationId),
                paymentDetail = TestModelFactory.buildBoletoPaymentDetail(paymentUrl = pdfUrl),
                method = PaymentMethod.BOLEPIX
            )

            coEvery { invoiceLiquidationService.get(liquidationId) } returns liquidation
            coEvery { paymentService.getPendingOrCreatePaymentFromLiquidation(liquidation) } returns payment

            val client = httpClientMock(
                responseByteArray = pdfFile.readBytes(),
                url = pdfUrl,
                httpMethod = HttpMethod.Get
            )

            val service = InvoiceFilesService(
                memberInvoiceGroupService,
                companyInvoiceDetailService,
                invoiceGroupTaxReceiptService,
                invoiceLiquidationTaxReceiptService,
                paymentService,
                invoiceLiquidationService,
                client,
                listOf(invoiceFileAvailabilityService),
            )

            val file = service.downloadBoletoFromLiquidation(liquidationId)

            assertNotNull(file)
            assertContains(file.name, "boleto")
            coVerifyOnce {
                invoiceLiquidationService.get(liquidationId)
                paymentService.getPendingOrCreatePaymentFromLiquidation(liquidation)
            }
        }

        @Test
        fun `should throw exception when invoice not found`(): Unit = runBlocking {
            val liquidationId = UUID.randomUUID()
            coEvery { invoiceLiquidationService.get(liquidationId) } returns NotFoundException()

            assertFailsWith<IllegalArgumentException> {
                defaultService.downloadBoletoFromLiquidation(liquidationId)
            }
        }

        @Test
        fun `should throw exception when invoice is paid`(): Unit = runBlocking {
            val liquidationId = UUID.randomUUID()
            val liquidation =
                TestModelFactory.buildInvoiceLiquidation(
                    id = liquidationId,
                    status = InvoiceLiquidationStatus.PAID,
                )
            coEvery { invoiceLiquidationService.get(liquidationId) } returns liquidation

            assertFailsWith<IllegalArgumentException> {
                defaultService.downloadBoletoFromLiquidation(liquidationId)
            }
        }
    }

    @Nested
    inner class DownloadTaxReceiptFromLiquidation {
        @Test
        fun `should return file successfully`() = runBlocking {
            val liquidationId = UUID.randomUUID()
            val pdfUrl = "http://example.com/test.pdf"
            val receipt = InvoiceLiquidationTaxReceipt(
                issuedAt = LocalDateTime.now(),
                invoiceLiquidationId = liquidationId,
                pdfUrl = pdfUrl,
                id = UUID.randomUUID(),
                status = TaxReceiptStatus.ISSUED
            )

            coEvery {
                invoiceLiquidationTaxReceiptService.getIssuedByInvoiceLiquidationIds(
                    listOf(liquidationId),
                    any()
                )
            } returns Result.success(listOf(receipt))

            val client = httpClientMock(
                responseByteArray = pdfFile.readBytes(),
                url = receipt.pdfUrl,
                httpMethod = HttpMethod.Get
            )

            val service = InvoiceFilesService(
                memberInvoiceGroupService,
                companyInvoiceDetailService,
                invoiceGroupTaxReceiptService,
                invoiceLiquidationTaxReceiptService,
                paymentService,
                invoiceLiquidationService,
                client,
                listOf(invoiceFileAvailabilityService),
            )

            val file = service.downloadTaxReceiptFromLiquidation(liquidationId)

            assertNotNull(file)
            assertContains(file.name, "nota-fiscal")
            coVerifyOnce {
                invoiceLiquidationTaxReceiptService.getIssuedByInvoiceLiquidationIds(
                    listOf(liquidationId),
                    any()
                )
            }
        }

        @Test
        fun `should throw exception when receipt not found`(): Unit = runBlocking {
            val liquidationId = UUID.randomUUID()
            coEvery {
                invoiceLiquidationTaxReceiptService.getIssuedByInvoiceLiquidationIds(
                    listOf(liquidationId),
                    any()
                )
            } returns NotFoundException()

            assertFailsWith<IllegalArgumentException> {
                defaultService.downloadTaxReceiptFromLiquidation(liquidationId)
            }

            coVerifyOnce {
                invoiceLiquidationTaxReceiptService.getIssuedByInvoiceLiquidationIds(
                    listOf(liquidationId),
                    any()
                )
            }
        }
    }

    @Test
    fun `createZipFromFiles should return zip file successfully`() = runBlocking {
        val files = listOf(
            InvoiceFileType.BOLETO to pdfFile.readBytes(),
            InvoiceFileType.TAX_RECEIPT to pdfFile.readBytes()
        )

        val zipFile = defaultService.createZipFromFiles(files)

        assertNotNull(zipFile)
        assertContains(zipFile.name, "invoice-files")
    }

    @Test
    fun `createZipFromFiles should throw exception when file list is empty`(): Unit = runBlocking {
        assertFailsWith<IllegalArgumentException> {
            defaultService.createZipFromFiles(emptyList())
        }
    }

    @Test
    fun `#should return a list the availability`() =
        runBlocking<Unit> {
            val companyId = RangeUUID.generate()
            val companySubContractId = RangeUUID.generate()
            val billingAccountablePartyId = RangeUUID.generate()
            val referenceDate = LocalDate.now()

            val invoice = CompanyInvoiceResponse(
                id = RangeUUID.generate(),
                externalId = null,
                status = CompanyInvoiceStatus.PROCESSED,
                referenceDate = referenceDate,
                billingAccountablePartyId = billingAccountablePartyId,
                validityPeriod = CompanyInvoiceValidityPeriod(
                    start = referenceDate.atBeginningOfTheMonth(),
                    end = referenceDate.atEndOfTheMonth()
                ),
                multipleValidityPeriods = listOf(
                    CompanyInvoiceValidityPeriod(
                        start = referenceDate.atBeginningOfTheMonth(),
                        end = referenceDate.atEndOfTheMonth(),
                    )
                ),
                companySubContractIds = listOf(companySubContractId),
                companyIds = listOf(companyId),
                type = CompanyInvoiceType.RECURRENT,
                model = CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
                totalAmount = 100.money,
                discount = 0.money,
                addition = 0.money,
                dueDate = LocalDate.now().plusDays(5),
                quantityMemberInvoices = 1,
                memberInvoiceIds = listOf(RangeUUID.generate()),
                relationships = emptyList(),
                globalItems = emptyList(),
                installments = 0,
                totalInstallments = 0,
                version = 1,
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now(),
            )

            coEvery { invoiceFileAvailabilityService.check(invoice) } returns InvoiceFileAvailability(
                InvoiceFileType.BOLETO,
                InvoiceFileType.BOLETO.friendlyName,
                true,
            )

            coEvery { taxAvailabilityService.check(invoice) } returns null

            val expected = listOf(
                InvoiceFileAvailability(
                    value = InvoiceFileType.BOLETO,
                    friendlyName = InvoiceFileType.BOLETO.friendlyName,
                    available = true
                ),
            )

            val result = defaultService.listAvailability(invoice)

            assertEquals(expected, result)

            coVerifyOnce {
                taxAvailabilityService.check(invoice)
                invoiceFileAvailabilityService.check(invoice)
            }
        }

    private fun httpClientMock(
        responseByteArray: ByteArray,
        statusCode: HttpStatusCode = HttpStatusCode.OK,
        url: String,
        httpMethod: HttpMethod,
    ): HttpClient {
        return HttpClient(MockEngine) {
            expectSuccess = true
            install(ContentNegotiation) { simpleGson() }
            engine {
                addHandler { request ->
                    if (request.method == httpMethod && request.url.toString() == url) {
                        respond(
                            responseByteArray,
                            statusCode,
                        )
                    } else {
                        respond(
                            responseByteArray,
                            statusCode,
                        )
                    }
                }
            }
        }
    }
}
