package br.com.alice.bff.business.services

import br.com.alice.bff.business.models.v2.InvoiceFileAvailability
import br.com.alice.bff.business.models.v2.InvoiceFileType
import br.com.alice.business.client.InvoiceGroupTaxReceiptService
import br.com.alice.business.client.InvoiceLiquidationTaxReceiptService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.atBeginningOfTheMonth
import br.com.alice.common.core.extensions.atEndOfTheMonth
import br.com.alice.common.core.extensions.money
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.models.InvoiceGroupTaxReceipt
import br.com.alice.data.layer.models.InvoiceLiquidationTaxReceipt
import br.com.alice.data.layer.models.TaxReceiptStatus
import br.com.alice.moneyin.models.CompanyInvoiceModel
import br.com.alice.moneyin.models.CompanyInvoiceRelationship
import br.com.alice.moneyin.models.CompanyInvoiceResponse
import br.com.alice.moneyin.models.CompanyInvoiceStatus
import br.com.alice.moneyin.models.CompanyInvoiceType
import br.com.alice.moneyin.models.CompanyInvoiceValidityPeriod
import io.mockk.Called
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class InvoiceFileAvailabilityServiceTest {

    private val companyId = RangeUUID.generate()
    private val companySubContractId = RangeUUID.generate()
    private val billingAccountablePartyId = RangeUUID.generate()
    private val referenceDate = LocalDate.now()

    private val invoice = CompanyInvoiceResponse(
        id = RangeUUID.generate(),
        externalId = null,
        status = CompanyInvoiceStatus.PROCESSED,
        referenceDate = referenceDate,
        billingAccountablePartyId = billingAccountablePartyId,
        validityPeriod = CompanyInvoiceValidityPeriod(
            start = referenceDate.atBeginningOfTheMonth(),
            end = referenceDate.atEndOfTheMonth()
        ),
        multipleValidityPeriods = listOf(
            CompanyInvoiceValidityPeriod(
                start = referenceDate.atBeginningOfTheMonth(),
                end = referenceDate.atEndOfTheMonth(),
            )
        ),
        companySubContractIds = listOf(companySubContractId),
        companyIds = listOf(companyId),
        type = CompanyInvoiceType.RECURRENT,
        model = CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
        totalAmount = 100.money,
        discount = 0.money,
        addition = 0.money,
        dueDate = LocalDate.now().plusDays(5),
        quantityMemberInvoices = 1,
        memberInvoiceIds = listOf(RangeUUID.generate()),
        relationships = emptyList(),
        globalItems = emptyList(),
        installments = 0,
        totalInstallments = 0,
        version = 1,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
    )

    companion object {
        @JvmStatic
        fun status() = listOf(
            arrayOf(
                listOf(
                    CompanyInvoiceStatus.PAID,
                    CompanyInvoiceStatus.CANCELED,
                    CompanyInvoiceStatus.CANCELED_BY_LIQUIDATION
                ), null
            ),
            arrayOf(
                listOf(
                    CompanyInvoiceStatus.WAITING_PAYMENT,
                    CompanyInvoiceStatus.PROCESSED,
                    CompanyInvoiceStatus.PARTIALLY_PAID
                ),
                true,
            ),
            arrayOf(
                listOf(CompanyInvoiceStatus.PROCESSING),
                false
            )
        )
    }

    @Nested
    inner class BoletoAvailabilityServiceTest {
        val service = BoletoAvailabilityService()

        @ParameterizedTest
        @MethodSource("br.com.alice.bff.business.services.InvoiceFileAvailabilityServiceTest#status")
        fun `should check the boleto availability by invoice status`(
            status: List<CompanyInvoiceStatus>,
            returnExpected: Boolean?
        ) =
            runBlocking<Unit> {
                status.map {
                    val invoice = invoice.copy(status = it)
                    val result = service.check(invoice)

                    Assertions.assertThat(result).isEqualTo(
                        InvoiceFileAvailability(
                            value = InvoiceFileType.BOLETO,
                            friendlyName = InvoiceFileType.BOLETO.friendlyName,
                            available = returnExpected,
                        )
                    )
                }
            }
    }

    @Nested
    inner class TaxReceiptAvailabilityServiceTest {
        private val invoiceGroupTaxReceiptService: InvoiceGroupTaxReceiptService = mockk()
        private val invoiceLiquidationTaxReceiptService: InvoiceLiquidationTaxReceiptService = mockk()

        private val service =
            TaxReceiptAvailabilityService(invoiceGroupTaxReceiptService, invoiceLiquidationTaxReceiptService)

        @Nested
        inner class InvoiceLiquidation {

            @Test
            fun `should return invoice file as available when a receipt is found`() = runBlocking {
                val invoice = invoice.copy(model = CompanyInvoiceModel.INVOICE_LIQUIDATION)

                val receipt = InvoiceLiquidationTaxReceipt(
                    issuedAt = LocalDateTime.now(),
                    invoiceLiquidationId = invoice.id,
                    pdfUrl = "http://example.com",
                    id = UUID.randomUUID(),
                    status = TaxReceiptStatus.ISSUED
                )

                coEvery { invoiceLiquidationTaxReceiptService.getIssuedByInvoiceLiquidationIds(listOf(invoice.id)) } returns listOf(
                    receipt
                )

                val result = service.check(invoice)

                Assertions.assertThat(result).isEqualTo(
                    InvoiceFileAvailability(
                        value = InvoiceFileType.TAX_RECEIPT,
                        friendlyName = InvoiceFileType.TAX_RECEIPT.friendlyName,
                        available = true,
                    )
                )

                coVerifyOnce {
                    invoiceLiquidationTaxReceiptService.getIssuedByInvoiceLiquidationIds(listOf(invoice.id))
                }

                coVerifyNone {
                    invoiceGroupTaxReceiptService wasNot Called
                }
            }

            @Test
            fun `should return invoice file as unavailable when any receipt is found`() = runBlocking {
                val invoice = invoice.copy(model = CompanyInvoiceModel.INVOICE_LIQUIDATION)

                coEvery { invoiceLiquidationTaxReceiptService.getIssuedByInvoiceLiquidationIds(listOf(invoice.id)) } returns emptyList()

                val result = service.check(invoice)

                Assertions.assertThat(result).isEqualTo(
                    InvoiceFileAvailability(
                        value = InvoiceFileType.TAX_RECEIPT,
                        friendlyName = InvoiceFileType.TAX_RECEIPT.friendlyName,
                        available = false,
                    )
                )

                coVerifyOnce {
                    invoiceLiquidationTaxReceiptService.getIssuedByInvoiceLiquidationIds(listOf(invoice.id))
                }

                coVerifyNone {
                    invoiceGroupTaxReceiptService wasNot Called
                }
            }
        }

        @Nested
        inner class MemberInvoiceGroup {

            @Test
            fun `should return invoice file as available when a receipt is found`() = runBlocking {
                val invoice = invoice.copy(model = CompanyInvoiceModel.MEMBER_INVOICE_GROUP)

                val receipt = InvoiceGroupTaxReceipt(
                    issuedAt = LocalDateTime.now(),
                    invoiceGroupId = invoice.id,
                    pdfUrl = "http://example.com",
                    id = UUID.randomUUID(),
                    status = TaxReceiptStatus.ISSUED
                )

                coEvery { invoiceGroupTaxReceiptService.getIssuedByInvoiceGroupIds(listOf(invoice.id)) } returns listOf(
                    receipt
                )

                val result = service.check(invoice)

                Assertions.assertThat(result).isEqualTo(
                    InvoiceFileAvailability(
                        value = InvoiceFileType.TAX_RECEIPT,
                        friendlyName = InvoiceFileType.TAX_RECEIPT.friendlyName,
                        available = true,
                    )
                )

                coVerifyOnce {
                    invoiceGroupTaxReceiptService.getIssuedByInvoiceGroupIds(listOf(invoice.id))
                }

                coVerifyNone {
                    invoiceLiquidationTaxReceiptService wasNot Called
                }
            }

            @Test
            fun `should return invoice file as unavailable when any receipt is found`() = runBlocking {
                val invoice = invoice.copy(model = CompanyInvoiceModel.MEMBER_INVOICE_GROUP)

                coEvery { invoiceGroupTaxReceiptService.getIssuedByInvoiceGroupIds(listOf(invoice.id)) } returns emptyList()

                val result = service.check(invoice)

                Assertions.assertThat(result).isEqualTo(
                    InvoiceFileAvailability(
                        value = InvoiceFileType.TAX_RECEIPT,
                        friendlyName = InvoiceFileType.TAX_RECEIPT.friendlyName,
                        available = false,
                    )
                )

                coVerifyOnce {
                    invoiceGroupTaxReceiptService.getIssuedByInvoiceGroupIds(listOf(invoice.id))
                }

                coVerifyNone {
                    invoiceLiquidationTaxReceiptService wasNot Called
                }
            }
        }

        @Nested
        inner class PreActivationPayment {

            @Test
            fun `should return invoice file as available when a receipt is found`() = runBlocking {
                val invoiceGroupId = RangeUUID.generate()
                val invoice = invoice.copy(
                    model = CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT, relationships = listOf(
                        CompanyInvoiceRelationship(
                            invoiceId = invoiceGroupId,
                            CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
                        )
                    )
                )

                val receipt = InvoiceGroupTaxReceipt(
                    issuedAt = LocalDateTime.now(),
                    invoiceGroupId = invoiceGroupId,
                    pdfUrl = "http://example.com",
                    id = UUID.randomUUID(),
                    status = TaxReceiptStatus.ISSUED
                )

                coEvery { invoiceGroupTaxReceiptService.getIssuedByInvoiceGroupIds(listOf(invoiceGroupId)) } returns listOf(
                    receipt
                )

                val result = service.check(invoice)

                Assertions.assertThat(result).isEqualTo(
                    InvoiceFileAvailability(
                        value = InvoiceFileType.TAX_RECEIPT,
                        friendlyName = InvoiceFileType.TAX_RECEIPT.friendlyName,
                        available = true,
                    )
                )

                coVerifyOnce {
                    invoiceGroupTaxReceiptService.getIssuedByInvoiceGroupIds(listOf(invoiceGroupId))
                }

                coVerifyNone {
                    invoiceLiquidationTaxReceiptService wasNot Called
                }
            }

            @Test
            fun `should return invoice file as unavailable when any receipt is found`() = runBlocking {
                val invoiceGroupId = RangeUUID.generate()
                val invoice = invoice.copy(
                    model = CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT, relationships = listOf(
                        CompanyInvoiceRelationship(
                            invoiceId = invoiceGroupId,
                            CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
                        )
                    )
                )

                coEvery { invoiceGroupTaxReceiptService.getIssuedByInvoiceGroupIds(listOf(invoiceGroupId)) } returns emptyList()

                val result = service.check(invoice)

                Assertions.assertThat(result).isEqualTo(
                    InvoiceFileAvailability(
                        value = InvoiceFileType.TAX_RECEIPT,
                        friendlyName = InvoiceFileType.TAX_RECEIPT.friendlyName,
                        available = false,
                    )
                )

                coVerifyOnce {
                    invoiceGroupTaxReceiptService.getIssuedByInvoiceGroupIds(listOf(invoiceGroupId))
                }

                coVerifyNone {
                    invoiceLiquidationTaxReceiptService wasNot Called
                }
            }
        }

        @Test
        fun `should return invoice file as unavailable when invoice does not belong to any pre activation id`() =
            runBlocking {
                val invoiceGroupId = RangeUUID.generate()
                val invoice = invoice.copy(
                    model = CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT, relationships = listOf(
                        CompanyInvoiceRelationship(
                            invoiceId = invoiceGroupId,
                            CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
                        )
                    )
                )

                coEvery { invoiceGroupTaxReceiptService.getIssuedByInvoiceGroupIds(listOf(invoiceGroupId)) } returns emptyList()

                val result = service.check(invoice)

                Assertions.assertThat(result).isEqualTo(
                    InvoiceFileAvailability(
                        value = InvoiceFileType.TAX_RECEIPT,
                        friendlyName = InvoiceFileType.TAX_RECEIPT.friendlyName,
                        available = false,
                    )
                )

                coVerifyOnce {
                    invoiceGroupTaxReceiptService.getIssuedByInvoiceGroupIds(listOf(invoiceGroupId))
                }

                coVerifyNone {
                    invoiceLiquidationTaxReceiptService wasNot Called
                }
            }
    }
}
