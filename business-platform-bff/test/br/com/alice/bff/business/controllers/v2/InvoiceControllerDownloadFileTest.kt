package br.com.alice.bff.business.controllers.v2

import br.com.alice.bff.business.ControllerTestHelper
import br.com.alice.bff.business.services.InvoiceFilesService
import br.com.alice.business.client.CompanyStaffService
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.returns
import br.com.alice.moneyin.client.CompanyInvoiceService
import br.com.alice.moneyin.models.CompanyInvoiceModel
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.io.File
import kotlin.test.BeforeTest
import kotlin.test.Test

class InvoiceControllerDownloadFileTest : ControllerTestHelper() {
    private val companyStaffService: CompanyStaffService = mockk()
    private val invoiceFilesService: InvoiceFilesService = mockk()
    private val pdfFile = File(javaClass.classLoader.getResource("test.pdf")!!.path)
    private val xlsxFile = File(javaClass.classLoader.getResource("test.xlsx")!!.path)
    private val zipFile = File(javaClass.classLoader.getResource("test.zip")!!.path)
    private val companyInvoiceService: CompanyInvoiceService = mockk()
    private val controller =
        InvoiceController(
            companyStaffService,
            invoiceFilesService,
            companyInvoiceService
        )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { controller }
        coEvery { companyStaffService.getLatestByEmail(staff.email) } returns companyStaff
    }

    @Test
    fun `downloadFiles should return single file`() = runBlocking {
        val invoiceId = RangeUUID.generate()

        coEvery { invoiceFilesService.downloadInvoiceDetails(invoiceId) } returns xlsxFile

        authenticatedAs(idToken, staffTest) {
            get("/v2/companies/${company.id}/invoices/$invoiceId/download_file?files=[\"INVOICE_DETAILS\"]") { response ->
                ResponseAssert.assertThat(response).isOK()
                ResponseAssert.assertThat(response).containsHeaderWithValue("Content-Disposition","attachment; filename=detalhes-fatura.xlsx")
            }
        }
    }

    @Test
    fun `downloadFiles should return zip file for multiple files and the company invoice model is pre activation payment`() = runBlocking {
        val invoiceId = RangeUUID.generate()


        coEvery { invoiceFilesService.downloadInvoiceDetails(invoiceId, CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT) } returns xlsxFile
        coEvery { invoiceFilesService.downloadTaxReceipt(invoiceId) } returns pdfFile
        coEvery { invoiceFilesService.downloadBoleto(invoiceId) } returns pdfFile
        coEvery { invoiceFilesService.createZipFromFiles(any()) } returns zipFile

        authenticatedAs(idToken, staffTest) {
            get("/v2/companies/${company.id}/invoices/$invoiceId/download_file?files=[\"INVOICE_DETAILS\",\"TAX_RECEIPT\",\"BOLETO\"]&invoice_model=PRE_ACTIVATION_PAYMENT") { response ->
                ResponseAssert.assertThat(response).isOK()
                ResponseAssert.assertThat(response).containsHeaderWithValue("Content-Disposition","attachment; filename=docs-alice-$invoiceId.zip")
            }
        }
    }


    @Test
    fun `downloadFiles should handle empty files parameter`() = runBlocking {
        val invoiceId = RangeUUID.generate()

        authenticatedAs(idToken, staffTest) {
            get("/v2/companies/${company.id}/invoices/$invoiceId/download_file?files=[]") { response ->
                ResponseAssert.assertThat(response).isBadRequest()
            }
        }
    }

    @Test
    fun `downloadFiles should handle invalid file type`() = runBlocking {
        val invoiceId = RangeUUID.generate()

        authenticatedAs(idToken, staffTest) {
            get("/v2/companies/${company.id}/invoices/$invoiceId/download_file?files=[\"INVALID_TYPE\"]") { response ->
                ResponseAssert.assertThat(response).isBadRequest()
            }
        }
    }
}
