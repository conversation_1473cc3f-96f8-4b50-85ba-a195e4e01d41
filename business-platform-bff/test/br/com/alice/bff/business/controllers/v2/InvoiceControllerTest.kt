package br.com.alice.bff.business.controllers.v2

import br.com.alice.bff.business.ControllerTestHelper
import br.com.alice.bff.business.converters.v2.toInvoiceResponse
import br.com.alice.bff.business.models.v2.FilterOption
import br.com.alice.bff.business.models.v2.InvoiceFileAvailability
import br.com.alice.bff.business.models.v2.InvoiceFileType
import br.com.alice.bff.business.models.v2.PaginatedResponse
import br.com.alice.bff.business.services.InvoiceFilesService
import br.com.alice.business.client.CompanyStaffService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.atBeginningOfTheMonth
import br.com.alice.common.core.extensions.atEndOfTheMonth
import br.com.alice.common.core.extensions.money
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.models.MemberInvoiceGroupStatus
import br.com.alice.moneyin.client.CompanyInvoiceService
import br.com.alice.moneyin.models.CompanyInvoiceModel
import br.com.alice.moneyin.models.CompanyInvoiceRelationship
import br.com.alice.moneyin.models.CompanyInvoiceResponse
import br.com.alice.moneyin.models.CompanyInvoiceStatus
import br.com.alice.moneyin.models.CompanyInvoiceType
import br.com.alice.moneyin.models.CompanyInvoiceValidityPeriod
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test

class InvoiceControllerTest : ControllerTestHelper() {
    private val companyStaffService: CompanyStaffService = mockk()
    private val invoiceFilesService: InvoiceFilesService = mockk()
    private val companyInvoiceService: CompanyInvoiceService = mockk()

    private val companyId = RangeUUID.generate()
    private val companySubContractId = RangeUUID.generate()
    private val billingAccountablePartyId = RangeUUID.generate()
    private val referenceDate = LocalDate.now()
    private val invoiceId = RangeUUID.generate()

    private val invoices = listOf(
        CompanyInvoiceResponse(
            id = invoiceId,
            externalId = null,
            status = CompanyInvoiceStatus.PROCESSING,
            billingAccountablePartyId = billingAccountablePartyId,
            referenceDate = referenceDate,
            multipleValidityPeriods = listOf(
                CompanyInvoiceValidityPeriod(
                    start = referenceDate.atBeginningOfTheMonth(),
                    end = referenceDate.atEndOfTheMonth(),
                )
            ),
            validityPeriod = CompanyInvoiceValidityPeriod(
                start = referenceDate.atBeginningOfTheMonth(),
                end = referenceDate.atEndOfTheMonth()
            ),
            companySubContractIds = listOf(companySubContractId),
            companyIds = listOf(companyId),
            type = CompanyInvoiceType.RECURRENT,
            model = CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
            totalAmount = 100.money,
            discount = 0.money,
            addition = 0.money,
            dueDate = LocalDate.now().plusDays(5),
            quantityMemberInvoices = 1,
            memberInvoiceIds = listOf(RangeUUID.generate()),
            relationships = emptyList(),
            globalItems = emptyList(),
            installments = 0,
            totalInstallments = 0,
            version = 1,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
        ),
        CompanyInvoiceResponse(
            externalId = null,
            id = RangeUUID.generate(),
            status = CompanyInvoiceStatus.PROCESSED,
            referenceDate = referenceDate,
            billingAccountablePartyId = billingAccountablePartyId,
            validityPeriod = CompanyInvoiceValidityPeriod(
                start = referenceDate.atBeginningOfTheMonth(),
                end = referenceDate.atEndOfTheMonth()
            ),
            multipleValidityPeriods = listOf(
                CompanyInvoiceValidityPeriod(
                    start = referenceDate.atBeginningOfTheMonth(),
                    end = referenceDate.atEndOfTheMonth(),
                )
            ),
            companySubContractIds = listOf(companySubContractId),
            companyIds = listOf(companyId),
            type = CompanyInvoiceType.RECURRENT,
            model = CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
            totalAmount = 100.money,
            discount = 0.money,
            addition = 0.money,
            dueDate = LocalDate.now().plusDays(5),
            quantityMemberInvoices = 1,
            memberInvoiceIds = listOf(RangeUUID.generate()),
            relationships = emptyList(),
            globalItems = emptyList(),
            installments = 0,
            totalInstallments = 0,
            version = 1,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
        )
    )

    private val controller =
        InvoiceController(
            companyStaffService,
            invoiceFilesService,
            companyInvoiceService,
        )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { controller }
        coEvery { companyStaffService.getLatestByEmail(staff.email) } returns companyStaff
    }

    @Test
    fun `#listInvoices should return expected`() = runBlocking {
        val filterOptions = listOf(
            FilterOption(
                name = MemberInvoiceGroupStatus.PROCESSING.description,
                value = "PROCESSING",
                count = 1
            ),
            FilterOption(
                name = MemberInvoiceGroupStatus.PROCESSED.description,
                value = "PROCESSED",
                count = 1
            )
        )

        val expectedResponse = PaginatedResponse(
            filterOptions = mapOf("status" to filterOptions),
            filters = emptyMap(),
            offset = 0,
            limit = 20,
            results = invoices.map { it.toInvoiceResponse() },
            total = 2
        )

        coEvery { companyInvoiceService.listByCompanyId(company.id) } returns invoices

        authenticatedAs(idToken, staffTest) {
            get("/v2/companies/${company.id}/invoices") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#listInvoices should return expected with liquidation`() = runBlocking {
        val invoices =
            invoices.plus(
                CompanyInvoiceResponse(
                    id = RangeUUID.generate(),
                    externalId = null,
                    status = CompanyInvoiceStatus.PROCESSED,
                    referenceDate = referenceDate,
                    billingAccountablePartyId = billingAccountablePartyId,
                    validityPeriod = CompanyInvoiceValidityPeriod(
                        start = referenceDate.atBeginningOfTheMonth(),
                        end = referenceDate.atEndOfTheMonth()
                    ),
                    multipleValidityPeriods = listOf(
                        CompanyInvoiceValidityPeriod(
                            start = referenceDate.atBeginningOfTheMonth(),
                            end = referenceDate.atEndOfTheMonth(),
                        )
                    ),
                    companySubContractIds = listOf(companySubContractId),
                    companyIds = listOf(companyId),
                    type = CompanyInvoiceType.LIQUIDATION,
                    model = CompanyInvoiceModel.INVOICE_LIQUIDATION,
                    totalAmount = 100.money,
                    discount = 0.money,
                    addition = 0.money,
                    dueDate = LocalDate.now().plusDays(5),
                    quantityMemberInvoices = 1,
                    memberInvoiceIds = listOf(RangeUUID.generate()),
                    relationships = listOf(
                        CompanyInvoiceRelationship(
                            invoiceId,
                            CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
                        )
                    ),
                    globalItems = emptyList(),
                    installments = 1,
                    totalInstallments = 2,
                    version = 1,
                    createdAt = LocalDateTime.now(),
                    updatedAt = LocalDateTime.now(),
                )
            )

        val filterOptions = listOf(
            FilterOption(
                name = CompanyInvoiceStatus.PROCESSING.description,
                value = "PROCESSING",
                count = 1
            ),
            FilterOption(
                name = CompanyInvoiceStatus.PROCESSED.description,
                value = "PROCESSED",
                count = 2
            ),
        )

        val results =
            invoices.map { it.toInvoiceResponse() }


        val expectedResponse = PaginatedResponse(
            filterOptions = mapOf("status" to filterOptions),
            filters = emptyMap(),
            offset = 0,
            limit = 20,
            results = results,
            total = 3
        )

        coEvery { companyInvoiceService.listByCompanyId(company.id) } returns invoices

        authenticatedAs(idToken, staffTest) {
            get("/v2/companies/${company.id}/invoices?") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#listInvoices should return expected with liquidation and filters and range`() = runBlocking {

        val filterOptions = listOf(
            FilterOption(
                name = CompanyInvoiceStatus.PROCESSED.description,
                value = "PROCESSED",
                count = 1
            )
        )

        val results = listOf(
            invoices[1].toInvoiceResponse(),
        )

        val expectedResponse = PaginatedResponse(
            filterOptions = mapOf("status" to filterOptions),
            filters = mapOf("status" to listOf("PROCESSING", "PROCESSED")),
            offset = 1,
            limit = 2,
            results = results,
            total = 2
        )

        coEvery { companyInvoiceService.listByCompanyId(company.id) } returns invoices

        authenticatedAs(idToken, staffTest) {
            get("/v2/companies/${company.id}/invoices?filter={\"status\": [\"PROCESSING\",\"PROCESSED\"]}&range=[1,2]") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#listInvoices should return error when company is not from staff`() = runBlocking {
        authenticatedAs(idToken, staffTest) {
            get("/v2/companies/${UUID.randomUUID()}/invoices") { response ->
                ResponseAssert.assertThat(response).isForbidden()
            }
        }

        coVerifyNone { companyInvoiceService.getByIdAndModel(any()) }
    }

    @Test
    fun `#availableFiles should show the invoice file availability`() = runBlocking {
        val invoice = invoices.first().copy(status = CompanyInvoiceStatus.PROCESSED)

        coEvery {
            companyInvoiceService.getByIdAndModel(
                CompanyInvoiceService.GetByIdAndModelPayload(
                    invoice.id,
                    CompanyInvoiceModel.MEMBER_INVOICE_GROUP
                )
            )
        } returns invoice

        coEvery { invoiceFilesService.listAvailability(invoice) } returns listOf(
            InvoiceFileAvailability(
                value = InvoiceFileType.BOLETO,
                friendlyName = InvoiceFileType.BOLETO.friendlyName,
                available = true
            )
        )

        authenticatedAs(idToken, staffTest) {
            get("/v2/companies/${company.id}/invoices/${invoice.id}/available_files") { response ->
                ResponseAssert.assertThat(response).isOKWithData(
                    listOf(
                        InvoiceFileAvailability(
                            value = InvoiceFileType.BOLETO,
                            friendlyName = InvoiceFileType.BOLETO.friendlyName,
                            available = true
                        ),
                    )
                )
            }
        }

        coVerifyOnce {
            companyInvoiceService.getByIdAndModel(
                CompanyInvoiceService.GetByIdAndModelPayload(
                    invoice.id,
                    CompanyInvoiceModel.MEMBER_INVOICE_GROUP
                )
            )

            invoiceFilesService.listAvailability(invoice)
        }
    }

    @Test
    fun `#availableFiles should show boleto as inapplicable when invoice is paid`() = runBlocking {
        val invoice = invoices.first().copy(status = CompanyInvoiceStatus.PAID)

        coEvery {
            companyInvoiceService.getByIdAndModel(
                CompanyInvoiceService.GetByIdAndModelPayload(
                    invoice.id,
                    CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT
                )
            )
        } returns invoice

        coEvery { invoiceFilesService.listAvailability(invoice) } returns listOf(
            InvoiceFileAvailability(
                value = InvoiceFileType.BOLETO,
                friendlyName = InvoiceFileType.BOLETO.friendlyName,
                available = null
            ),
        )

        authenticatedAs(idToken, staffTest) {
            get("/v2/companies/${company.id}/invoices/${invoice.id}/available_files?invoice_model=PRE_ACTIVATION_PAYMENT") { response ->
                ResponseAssert.assertThat(response).isOKWithData(
                    listOf(
                        InvoiceFileAvailability(
                            value = InvoiceFileType.BOLETO,
                            friendlyName = InvoiceFileType.BOLETO.friendlyName,
                            available = null
                        ),
                    )
                )
            }
        }

        coVerifyOnce {
            companyInvoiceService.getByIdAndModel(
                CompanyInvoiceService.GetByIdAndModelPayload(
                    invoice.id,
                    CompanyInvoiceModel.PRE_ACTIVATION_PAYMENT
                )
            )

            invoiceFilesService.listAvailability(invoice)
        }
    }
}

