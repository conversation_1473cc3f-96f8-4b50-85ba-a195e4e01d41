package br.com.alice.bff.business.converters.v2

import br.com.alice.bff.business.models.v2.InvoiceModel
import br.com.alice.bff.business.models.v2.InvoiceResponse
import br.com.alice.bff.business.models.v2.InvoiceStatusResponse
import br.com.alice.bff.business.models.v2.Period
import br.com.alice.common.convertTo
import br.com.alice.moneyin.models.CompanyInvoiceResponse
import br.com.alice.moneyin.models.CompanyInvoiceStatus

fun CompanyInvoiceStatus.toStatusResponse() = InvoiceStatusResponse(
    friendlyName = this.friendlyName(),
    status = when (this) {
        CompanyInvoiceStatus.CANCELED_BY_LIQUIDATION -> CompanyInvoiceStatus.CANCELED.name
        else -> this.name
    }
)

fun CompanyInvoiceResponse.toInvoiceResponse() = InvoiceResponse(
    id = this.id,
    referenceDate = this.referenceDate,
    dueDate = this.dueDate,
    status = this.status.toStatusResponse(),
    totalAmount = this.totalAmount,
    periodFrom = this.validityPeriod.start,
    periodTo = this.validityPeriod.end,
    createdAt = this.createdAt,
    updatedAt = this.updatedAt,
    periods = this.multipleValidityPeriods.map { Period(from = it.start, to = it.end) },
    taxReceiptUrl = null,
    isLiquidation = this.model.isLiquidation,
    installments = this.installments,
    totalInstallments = this.totalInstallments,
    invoiceModel = this.model.convertTo(InvoiceModel::class)
)

fun CompanyInvoiceStatus.friendlyName() = when (this) {
    CompanyInvoiceStatus.CANCELED -> "Cancelado"
    CompanyInvoiceStatus.PAID -> "Pagamento realizado"
    CompanyInvoiceStatus.WAITING_PAYMENT,
    CompanyInvoiceStatus.PROCESSED -> "Em aberto"

    CompanyInvoiceStatus.PROCESSING -> "Processando"
    CompanyInvoiceStatus.PARTIALLY_PAID -> "Parcialmente pago"
    CompanyInvoiceStatus.CANCELED_BY_LIQUIDATION -> "Cancelado"
}
