package br.com.alice.bff.business.controllers.v2

import br.com.alice.bff.business.converters.v2.toInvoiceResponse
import br.com.alice.bff.business.models.v2.FilterOption
import br.com.alice.bff.business.models.v2.InvoiceFileAvailability
import br.com.alice.bff.business.models.v2.InvoiceFileType
import br.com.alice.bff.business.models.v2.InvoiceResponse
import br.com.alice.bff.business.models.v2.PaginatedResponse
import br.com.alice.bff.business.services.InvoiceFilesService
import br.com.alice.bff.business.services.toFileName
import br.com.alice.business.client.CompanyStaffService
import br.com.alice.common.Response
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.serialization.gson
import br.com.alice.common.toResponse
import br.com.alice.common.withContexts
import br.com.alice.moneyin.client.CompanyInvoiceService
import br.com.alice.moneyin.models.CompanyInvoiceModel
import br.com.alice.moneyin.models.CompanyInvoiceStatus
import com.google.api.client.util.ArrayMap
import io.ktor.http.ContentDisposition
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.server.application.ApplicationCall
import io.ktor.server.response.respondFile
import io.ktor.util.pipeline.PipelineContext
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.sync.Semaphore
import kotlinx.coroutines.sync.withPermit
import kotlinx.coroutines.withContext
import java.io.File
import java.util.UUID

class InvoiceController(
    companyStaffService: CompanyStaffService,
    private val invoiceFilesService: InvoiceFilesService,
    private val companyInvoiceService: CompanyInvoiceService,
) : BaseController(companyStaffService) {

    suspend fun listInvoices(companyId: String, queryParams: Parameters): Response = coroutineScope {
        validateCompany(companyId.toUUID()) {
            val requestFilters = queryParams.toFilter(companyId)

            val invoices = companyInvoiceService.listByCompanyId(companyId.toUUID()).get()

            logger.info(
                "Invoices found for company $companyId",
                "companyId" to companyId,
                "invoices" to invoices.map { mapOf("id" to it.id, "model" to it.model) }
            )

            val invoiceResponses = invoices.map { it.toInvoiceResponse() }

            val filterStatusNames = requestFilters.status?.map { it.name }

            val results = invoiceResponses
                .filter { response ->
                    filterStatusNames?.let { response.status.status in filterStatusNames } ?: true
                }.drop(requestFilters.range.first)
                .take(requestFilters.range.count())
                .sortedByDescending { it.referenceDate }

            logger.info(
                "Invoices filtered for company $companyId",
                "companyId" to companyId,
                "invoice_ids_filtered" to results.map { it.id },
                "filter" to requestFilters,
            )

            val filters: ArrayMap<String, Any>? = queryParams["filter"]?.let { gson.fromJson(it) }
            PaginatedResponse(
                filterOptions = mapOf("status" to results.toFilterOptions()),
                filters = filters?.toMap() ?: emptyMap(),
                offset = requestFilters.range.first,
                limit = requestFilters.range.count(),
                results = results,
                total = invoiceResponses.size
            ).toResponse()
        }
    }

    suspend fun availableFiles(invoiceId: UUID, queryParams: Parameters): Response =
        coResultOf<List<InvoiceFileAvailability>, Throwable> {
            val invoiceModel = getInvoiceModelFromParameters(queryParams)

            val invoice = companyInvoiceService.getByIdAndModel(
                CompanyInvoiceService.GetByIdAndModelPayload(
                    invoiceId,
                    invoiceModel,
                )
            ).get()

            invoiceFilesService.listAvailability(invoice)
        }.foldResponse()

    private fun getInvoiceModelFromParameters(parameters: Parameters) =
        parameters["invoice_model"]?.let { gson.fromJson<CompanyInvoiceModel>(it) }
            ?: CompanyInvoiceModel.MEMBER_INVOICE_GROUP

    suspend fun downloadFiles(call: PipelineContext<Unit, ApplicationCall>) {
        call.withContexts {
            withContext(call.coroutineContext) {
                val files: List<InvoiceFileType?> =
                    call.context.request.queryParameters["files"]?.let { gson.fromJson(it) }
                        ?: emptyList()
                val invoiceId = call.context.parameters["invoiceId"]?.toUUID() ?: return@withContext Response(
                    HttpStatusCode.BadRequest, "Missing parameters"
                )

                val companyInvoiceModel: CompanyInvoiceModel =
                    getInvoiceModelFromParameters(call.context.request.queryParameters)

                logger.info("Company Invoice Model: $companyInvoiceModel")

                if (files.isEmpty() || files.any { it == null }) throw IllegalArgumentException("Invalid files to download")

                // we're using a semaphore to limit the number of concurrent downloads so the machine is not overloaded
                val semaphore = Semaphore(2)

                val invoiceDetailsDeferred = async {
                    if (files.contains(InvoiceFileType.INVOICE_DETAILS)) {
                        semaphore.withPermit {
                            InvoiceFileType.INVOICE_DETAILS to invoiceFilesService.downloadInvoiceDetails(
                                invoiceId,
                                companyInvoiceModel,
                            )
                                .readBytes()
                        }
                    } else null
                }

                val receiptDeferred = async {
                    if (files.contains(InvoiceFileType.TAX_RECEIPT)) {
                        semaphore.withPermit {
                            InvoiceFileType.TAX_RECEIPT to invoiceFilesService.downloadTaxReceipt(invoiceId).readBytes()
                        }
                    } else null
                }

                val boletoDeferred = async {
                    if (files.contains(InvoiceFileType.BOLETO)) {
                        semaphore.withPermit {
                            InvoiceFileType.BOLETO to invoiceFilesService.downloadBoleto(invoiceId).readBytes()
                        }
                    } else null
                }

                val boleto = boletoDeferred.await()
                val receipt = receiptDeferred.await()
                val invoiceDetails = invoiceDetailsDeferred.await()

                val availableFiles = listOfNotNull(invoiceDetails, receipt, boleto)

                if (availableFiles.size == 1) {
                    val (type, bytes) = availableFiles.first()
                    val (filename, extension) = type.toFileName().split(".")
                    val file = File.createTempFile(filename, extension)
                    file.writeBytes(bytes)

                    logger.info("[downloadFiles] Responding with single file $filename.$extension")

                    respondWithFile(call, file, "${filename}.${extension}")
                } else {
                    val zipFile = invoiceFilesService.createZipFromFiles(availableFiles)
                    logger.info("[downloadFiles] Responding with zip file docs-alice-${invoiceId}.zip")

                    respondWithFile(call, zipFile, "docs-alice-${invoiceId}.zip")
                }
            }
        }
    }

    private suspend fun respondWithFile(call: PipelineContext<Unit, ApplicationCall>, file: File, fileName: String) {
        call.context.respondFile(file) {
            call.context.response.headers.append(
                HttpHeaders.ContentDisposition,
                ContentDisposition.Attachment.withParameter(
                    ContentDisposition.Parameters.FileName,
                    fileName
                ).toString()
            )
        }
    }

    private fun List<InvoiceResponse>.toFilterOptions(): List<FilterOption> =
        this.groupBy { it.status.status }
            .map {
                val value = it.key
                val status = CompanyInvoiceStatus.valueOf(value)

                FilterOption(
                    name = status.description,
                    value = value,
                    count = it.value.size
                )
            }

    private fun Parameters.toFilter(companyId: String) =
        CompanyInvoiceFilter(
            companyId = companyId.toUUID(),
            status = parseFilter<List<String>?>(this, "status")?.let { list ->
                list.map { CompanyInvoiceStatus.valueOf(it) }
            },
            range = parseRange(this)
        )

    data class CompanyInvoiceFilter(
        val companyId: UUID,
        val status: List<CompanyInvoiceStatus>? = null,
        val range: IntRange = IntRange(0, 19),
    )
}
