package br.com.alice.bff.business.models.v2

import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class InvoiceResponse(
    val id: UUID,
    val referenceDate: LocalDate,
    val dueDate: LocalDate,
    val status: InvoiceStatusResponse,
    val taxReceiptUrl: String?,
    val totalAmount: BigDecimal,
    val periodFrom: LocalDate,
    val periodTo: LocalDate,
    val periods: List<Period> = emptyList(),
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
    val installments: Int = 0,
    val totalInstallments: Int = 0,
    val isLiquidation: Boolean = false,
    val invoiceModel: InvoiceModel = InvoiceModel.MEMBER_INVOICE_GROUP,
)

data class Period(
    val from: LocalDate,
    val to: LocalDate
)

data class InvoiceStatusResponse(
    val friendlyName: String,
    val status: String,
)

data class InvoiceFileAvailability(
    val value: InvoiceFileType,
    val friendlyName: String,
    val available: Boolean?
)

enum class InvoiceFileType(val friendlyName: String) {
    TAX_RECEIPT("Nota fiscal"),
    INVOICE_DETAILS("Detalhes da fatura"),
    BOLETO("Boleto")
}

enum class InvoiceModel {
    PRE_ACTIVATION_PAYMENT,
    MEMBER_INVOICE_GROUP,
    INVOICE_LIQUIDATION,
}
