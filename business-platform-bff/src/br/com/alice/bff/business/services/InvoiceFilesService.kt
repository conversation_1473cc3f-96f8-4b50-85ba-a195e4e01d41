package br.com.alice.bff.business.services

import br.com.alice.bff.business.ServiceConfig
import br.com.alice.bff.business.models.v2.InvoiceFileAvailability
import br.com.alice.bff.business.models.v2.InvoiceFileType
import br.com.alice.business.client.InvoiceGroupTaxReceiptService
import br.com.alice.business.client.InvoiceLiquidationTaxReceiptService
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.Spannable
import br.com.alice.common.service.serialization.simpleGson
import br.com.alice.data.layer.models.InvoiceLiquidationStatus
import br.com.alice.data.layer.models.MemberInvoiceGroupStatus
import br.com.alice.moneyin.client.CompanyInvoiceDetailService
import br.com.alice.moneyin.client.InvoiceLiquidationService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.models.CompanyInvoiceModel
import br.com.alice.moneyin.models.CompanyInvoiceResponse
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.getOrNull
import com.google.common.io.Files
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.get
import io.ktor.client.statement.HttpResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.util.UUID
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream

class InvoiceFilesService(
    private val memberInvoiceGroupService: MemberInvoiceGroupService,
    private val companyInvoiceDetailService: CompanyInvoiceDetailService,
    private val invoiceGroupTaxReceiptService: InvoiceGroupTaxReceiptService,
    private val invoiceLiquidationTaxReceiptService: InvoiceLiquidationTaxReceiptService,
    private val paymentService: PaymentService,
    private val liquidationService: InvoiceLiquidationService,
    private val httpClient: HttpClient = DefaultHttpClient({
        install(ContentNegotiation) { simpleGson() }
    }, timeoutInMillis = 15_000),
    private val invoiceFileAvailabilityServices: List<InvoiceFileAvailabilityService>,
) : Spannable {

    companion object {
        private val MONEY_IN_INVOICE_URL = ServiceConfig.MoneyIn.INVOICE_URL
    }

    private suspend fun downloadFile(url: String): Result<ByteArray, Throwable> =
        coResultOf {
            span("downloadFile") {
                it.setAttribute("url", url)
                val httpResponse: HttpResponse = httpClient.get(url)
                val fileBytes = httpResponse.body<ByteArray>()

                fileBytes
            }
        }

    suspend fun downloadInvoiceDetails(
        invoiceId: UUID,
        model: CompanyInvoiceModel = CompanyInvoiceModel.MEMBER_INVOICE_GROUP,
    ): File {
        val fileBytes = companyInvoiceDetailService.getById(invoiceId, model).get()

        return withContext(Dispatchers.IO) {
            val file = File.createTempFile("detalhes-fatura", ".xlsx")
            file.writeBytes(fileBytes)
            file
        }
    }

    suspend fun downloadTaxReceipt(invoiceId: UUID): File {
        val receipt = invoiceGroupTaxReceiptService.getIssuedByInvoiceGroupIds(
            ids = listOf(invoiceId),
            findOptions = InvoiceGroupTaxReceiptService.FindOptions(withSecureLink = true)
        ).getOrNull()?.firstOrNull()

        logger.info("Tax receipt found for invoice $invoiceId", "receipt" to receipt)

        return receipt?.let {
            val fileBytes = downloadFile(receipt.pdfUrl).get()

            val file = File.createTempFile("nota-fiscal", ".pdf")
            file.writeBytes(fileBytes)

            file
        } ?: throw IllegalArgumentException("Tax receipt not found for invoice $invoiceId")
    }

    suspend fun downloadBoleto(invoiceId: UUID): File {
        val invoice = memberInvoiceGroupService.get(invoiceId).getOrNull()
            ?: throw IllegalArgumentException("Invoice not found for id $invoiceId")

        if (invoice.status == MemberInvoiceGroupStatus.PAID) throw IllegalArgumentException("Invoice already paid")
        if (invoice.status == MemberInvoiceGroupStatus.CANCELED) throw IllegalArgumentException("Invoice cancelled")

        val payment = paymentService.getPendingOrCreatePayment(invoice).getOrNull()
            ?: throw IllegalArgumentException("Payment not found for invoice $invoiceId")

        val pdfUrl = MONEY_IN_INVOICE_URL.replace(":paymentId", payment.id.toString())
        val fileBytes = downloadFile(pdfUrl).get()

        val file = withContext(Dispatchers.IO) {
            File.createTempFile("boleto", ".pdf")
        }
        file.writeBytes(fileBytes)

        return file
    }

    suspend fun downloadTaxReceiptFromLiquidation(liquidationId: UUID): File {
        val receipt = invoiceLiquidationTaxReceiptService.getIssuedByInvoiceLiquidationIds(
            ids = listOf(liquidationId),
            findOptions = InvoiceLiquidationTaxReceiptService.FindOptions(withSecureLink = true)
        ).getOrNull()?.firstOrNull()

        logger.info("Tax receipt found for liquidation $liquidationId", "receipt" to receipt)

        return receipt?.let {
            val fileBytes = downloadFile(receipt.pdfUrl).get()

            val file = File.createTempFile("nota-fiscal", ".pdf")
            file.writeBytes(fileBytes)

            file
        } ?: throw IllegalArgumentException("Tax receipt not found for liquidation $liquidationId")
    }

    suspend fun downloadBoletoFromLiquidation(liquidationId: UUID): File {
        val liquidation = liquidationService.get(liquidationId).getOrNull()
            ?: throw IllegalArgumentException("Liquidation not found for id $liquidationId")

        if (liquidation.status == InvoiceLiquidationStatus.PAID) throw IllegalArgumentException("Invoice already paid")
        if (liquidation.status == InvoiceLiquidationStatus.CANCELED) throw IllegalArgumentException("Invoice cancelled")

        val payment = paymentService.getPendingOrCreatePaymentFromLiquidation(liquidation).getOrNull()
            ?: throw IllegalArgumentException("Payment not found for invoice $liquidationId")


        val pdfUrl = MONEY_IN_INVOICE_URL.replace(":paymentId", payment.id.toString())
        val fileBytes = downloadFile(pdfUrl).get()

        val file = withContext(Dispatchers.IO) {
            File.createTempFile("boleto", ".pdf")
        }
        file.writeBytes(fileBytes)

        return file
    }

    suspend fun createZipFromFiles(files: List<Pair<InvoiceFileType, ByteArray>>): File = span("createZipFromFiles") {
        if (files.isEmpty()) throw IllegalArgumentException("No files to zip")

        val zipFile = File.createTempFile("invoice-files", ".zip")
        val zipOutputStream = Files.asByteSink(zipFile).openStream()

        val zip = ZipOutputStream(zipOutputStream)

        files.forEach { (type, bytes) ->
            val entry = ZipEntry(type.toFileName())
            zip.putNextEntry(entry)
            zip.write(bytes)
            zip.closeEntry()
        }

        zip.close()

        zipFile
    }

    suspend fun listAvailability(invoice: CompanyInvoiceResponse): List<InvoiceFileAvailability> =
        invoiceFileAvailabilityServices.mapNotNull { it.check(invoice) }
}


fun InvoiceFileType.toFileName(): String =
    when (this) {
        InvoiceFileType.BOLETO -> "boleto.pdf"
        InvoiceFileType.TAX_RECEIPT -> "nota-fiscal.pdf"
        InvoiceFileType.INVOICE_DETAILS -> "detalhes-fatura.xlsx"
    }
