package br.com.alice.bff.business.services

import br.com.alice.bff.business.models.v2.InvoiceFileAvailability
import br.com.alice.bff.business.models.v2.InvoiceFileType
import br.com.alice.business.client.InvoiceGroupTaxReceiptService
import br.com.alice.business.client.InvoiceLiquidationTaxReceiptService
import br.com.alice.moneyin.models.CompanyInvoiceResponse
import br.com.alice.moneyin.models.CompanyInvoiceStatus
import com.github.kittinunf.result.getOrNull

interface InvoiceFileAvailabilityService {
    suspend fun check(invoice: CompanyInvoiceResponse): InvoiceFileAvailability?
}

class BoletoAvailabilityService : InvoiceFileAvailabilityService {
    override suspend fun check(invoice: CompanyInvoiceResponse): InvoiceFileAvailability? {
        val isBoletoAvailability: Boolean? = when (invoice.status) {
            CompanyInvoiceStatus.PAID,
            CompanyInvoiceStatus.CANCELED,
            CompanyInvoiceStatus.CANCELED_BY_LIQUIDATION -> null

            CompanyInvoiceStatus.WAITING_PAYMENT,
            CompanyInvoiceStatus.PROCESSED,
            CompanyInvoiceStatus.PARTIALLY_PAID -> true

            CompanyInvoiceStatus.PROCESSING -> false
        }

        return InvoiceFileAvailability(
            value = InvoiceFileType.BOLETO,
            friendlyName = InvoiceFileType.BOLETO.friendlyName,
            available = isBoletoAvailability
        )
    }
}

class InvoiceDetailAvailabilityService : InvoiceFileAvailabilityService {
    override suspend fun check(invoice: CompanyInvoiceResponse): InvoiceFileAvailability? {
        return if (!invoice.isLiquidation) InvoiceFileAvailability(
            value = InvoiceFileType.INVOICE_DETAILS,
            friendlyName = InvoiceFileType.INVOICE_DETAILS.friendlyName,
            available = true
        ) else null
    }
}

class TaxReceiptAvailabilityService(
    private val invoiceGroupTaxReceiptService: InvoiceGroupTaxReceiptService,
    private val invoiceLiquidationTaxReceiptService: InvoiceLiquidationTaxReceiptService,
) : InvoiceFileAvailabilityService {
    override suspend fun check(invoice: CompanyInvoiceResponse): InvoiceFileAvailability? {
        val isTaxReceiptAvailability = checkTaxReceiptAvailability(invoice)

        return InvoiceFileAvailability(
            value = InvoiceFileType.TAX_RECEIPT,
            friendlyName = InvoiceFileType.TAX_RECEIPT.friendlyName,
            available = isTaxReceiptAvailability
        )
    }

    private suspend fun checkTaxReceiptAvailability(invoice: CompanyInvoiceResponse): Boolean {
        return if (invoice.isLiquidation) {
            val receipt = getLiquidationTaxReceipt(invoice)

            receipt != null
        } else {
            val receipt = getInvoiceTaxReceipt(invoice)

            receipt != null
        }
    }

    private suspend fun getLiquidationTaxReceipt(invoice: CompanyInvoiceResponse) =
        invoiceLiquidationTaxReceiptService.getIssuedByInvoiceLiquidationIds(listOf(invoice.id)).getOrNull()
            ?.firstOrNull()

    private suspend fun getInvoiceTaxReceipt(invoice: CompanyInvoiceResponse) =
        getInvoiceId(invoice)?.let {
            invoiceGroupTaxReceiptService.getIssuedByInvoiceGroupIds(listOf(it)).getOrNull()
                ?.firstOrNull()
        }

    private fun getInvoiceId(invoice: CompanyInvoiceResponse) = if (invoice.isMemberInvoiceGroup) {
        invoice.id
    } else {
        invoice.relationships.firstOrNull { it.model.isMemberInvoiceGroup }?.invoiceId
    }
}
