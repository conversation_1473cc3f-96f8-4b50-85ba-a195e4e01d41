package br.com.alice.bff.business.ioc

import br.com.alice.bff.business.SERVICE_NAME
import br.com.alice.bff.business.controllers.CacheController
import br.com.alice.bff.business.controllers.external.ExternalController
import br.com.alice.bff.business.controllers.v1.CompanySubcontractController
import br.com.alice.bff.business.controllers.v2.AuthController
import br.com.alice.bff.business.controllers.v2.BeneficiaryListController
import br.com.alice.bff.business.controllers.v2.CompanyController
import br.com.alice.bff.business.controllers.v2.CompanyStaffController
import br.com.alice.bff.business.controllers.v2.CopayController
import br.com.alice.bff.business.controllers.v2.DashboardController
import br.com.alice.bff.business.controllers.v2.GracePeriodController
import br.com.alice.bff.business.controllers.v2.InvoiceController
import br.com.alice.bff.business.controllers.v2.LiquidationController
import br.com.alice.bff.business.controllers.v2.NotificationController
import br.com.alice.bff.business.controllers.v2.PaymentController
import br.com.alice.bff.business.controllers.v2.ProductController
import br.com.alice.bff.business.controllers.v2.RefundController
import br.com.alice.bff.business.controllers.v2.ScreenController
import br.com.alice.bff.business.controllers.v2.WhatsNewController
import br.com.alice.bff.business.services.AuthService
import br.com.alice.bff.business.services.CoPaymentCostInfoCachedService
import br.com.alice.bff.business.services.ContractService
import br.com.alice.bff.business.services.InvoiceFilesService
import br.com.alice.bff.business.services.ModuleService
import br.com.alice.bff.business.services.PaymentService
import br.com.alice.bff.business.services.ProductCachedService
import br.com.alice.bff.business.services.BoletoAvailabilityService
import br.com.alice.bff.business.services.InvoiceDetailAvailabilityService
import br.com.alice.bff.business.services.InvoiceFileAvailabilityService
import br.com.alice.bff.business.services.TaxReceiptAvailabilityService
import br.com.alice.bff.business.webhooks.CognitoWebhooksReceiver
import br.com.alice.bff.business.webhooks.ENotasWebhookReceiver
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.core.BaseConfig
import br.com.alice.common.core.RunningMode
import br.com.alice.common.redis.CacheFactory
import br.com.alice.common.service.serialization.simpleGson
import br.com.alice.communication.email.sender.EmailSenderClient
import br.com.alice.communication.email.sender.SimpleEmailServiceClient
import br.com.alice.communication.email.template.EmailTemplateClient
import br.com.alice.communication.email.template.SimpleEmailServiceTemplateClient
import com.typesafe.config.ConfigFactory
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.config.HoconApplicationConfig
import org.koin.dsl.bind
import org.koin.dsl.module
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider
import software.amazon.awssdk.auth.credentials.AwsSessionCredentials
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.ses.SesClient
import br.com.alice.bff.business.controllers.v1.BeneficiaryController as BeneficiaryControllerV1
import br.com.alice.bff.business.controllers.v2.BeneficiaryController as BeneficiaryControllerV2

val ServiceModule = module {
    // Configuration
    val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))
    val cache = CacheFactory.newInstance("business-platform-bff")

    // Configuration
    single { config }
    val httpClient = DefaultHttpClient({
        install(ContentNegotiation) { simpleGson() }
    }, timeoutInMillis = 15_000)

    when (val environment = BaseConfig.instance.runningMode) {
        RunningMode.PRODUCTION -> {
            single<EmailSenderClient> { SimpleEmailServiceClient(get()) }
            single<EmailTemplateClient> { SimpleEmailServiceTemplateClient(get()) }
        }

        else -> {
            val awsCredentials = AwsSessionCredentials.create(
                config.property(
                    "${environment.value.lowercase()}.AWS_ACCESS_KEY_ID"
                ).getString(),
                config.property(
                    "${environment.value.lowercase()}.AWS_SECRET_ACCESS_KEY"
                ).getString(),
                config.property(
                    "${environment.value.lowercase()}.AWS_SESSION_TOKEN"
                ).getString()
            )

            val awsCredentialsProvider = AwsCredentialsProvider { awsCredentials }

            val sesClient = SesClient.builder().apply {
                credentialsProvider(awsCredentialsProvider)
                region(Region.US_EAST_1)
            }.build()

            single<EmailSenderClient> { SimpleEmailServiceClient(sesClient) }
            single<EmailTemplateClient> { SimpleEmailServiceTemplateClient(sesClient) }
        }
    }

    // Services
    single { ProductCachedService(cache, get(), get(), get(), get(), get()) }
    single { CoPaymentCostInfoCachedService(cache, get()) }
    single { ContractService(get(), get()) }
    single { PaymentService(get()) }
    single { BoletoAvailabilityService() } bind InvoiceFileAvailabilityService::class
    single { InvoiceDetailAvailabilityService() } bind InvoiceFileAvailabilityService::class
    single { TaxReceiptAvailabilityService(get(), get()) } bind InvoiceFileAvailabilityService::class
    single { InvoiceFilesService(get(), get(), get(), get(), get(), get(), httpClient, getAll()) }
    single { AuthService(get(), get()) }
    single { ModuleService() }

    // Controllers
    single { HealthController(SERVICE_NAME) }
    single { BeneficiaryControllerV1(get(), get(), get(), get(), get(), get(), get(), get()) }
    single { CompanySubcontractController(get(), get()) }

    // V2
    single { AuthController(get(), get(), get(), get(), get()) }
    single { InvoiceController(get(), get(), get()) }
    single { BeneficiaryListController(get(), get(), get()) }
    single { BeneficiaryControllerV2(get(), get(), get(), get(), get(), get(), get(), get(), get(), get()) }
    single { PaymentController(get(), get(), get()) }
    single { ProductController(get(), get(), get(), get(), get(), get(), get(), get(), get(), get(), get()) }
    single { NotificationController(get(), get()) }
    single { GracePeriodController(get(), get()) }
    single { CopayController(get(), get(), get()) }
    single { RefundController(get(), get(), get()) }
    single { ScreenController(get(), get(), get(), get()) }
    single { CompanyController(get(), get(), get(), get(), get(), cache) }
    single { LiquidationController(get(), get(), get(), get()) }
    single { DashboardController(get(), get(), get(), get(), get(), get()) }
    single { WhatsNewController(get(), get(), get(), get(), get(), get()) }
    single { CompanyStaffController(get(), get()) }

    // OTHERS
    single { CacheController(cache) }
    single { ExternalController(get(), get(), get(), get(), get(), get(), get()) }

    // Webhooks
    single { CognitoWebhooksReceiver(get()) }
    single { ENotasWebhookReceiver(get()) }

}
