package app.ehr_domain_service_test

import rego.v1

import data.app.ehr_domain_service

test_unauth_view_models_allowed if {
    resources = [
        "ActionPlanTask",
        "AppContentScreenDetail",
        "AppointmentCoordination",
        "AppointmentEvent",
        "AppointmentProcedureExecuted",
        "AssistanceCare",
        "Channel",
        "ChannelComment",
        "ChannelHistory",
        "ChannelTheme",
        "ClinicalOutcomeRecord",
        "ContactModel",
        "ConsentRegistration",
        "DasaDiagnosticReport",
        "DbLaboratoryTestResult",
        "DbLaboratoryTestResultProcess",
        "DischargeSummary",
        "EinsteinAlergia",
        "EinsteinAtendimento",
        "EinsteinAvaliacaoInicial",
        "EinsteinDadosDeAlta",
        "EinsteinDiagnostico",
        "EinsteinEncaminhamento",
        "EinsteinMedicamento",
        "EinsteinProcedimento",
        "EinsteinResultadoExame",
        "EinsteinResumoInternacao",
        "EinsteinStructuredTestResult",
        "ExecIndicatorAuthorizerModel",
        "ExecutionGroupModel",
        "ExternalReferralModel",
        "FhirBundle",
        "FhirDiagnosticReport",
        "FhirDocument",
        "FleuryTestResultFile",
        "GuiaModel",
        "HaocFhirProcess",
        "HDataOverview",
        "HLActionRecommendation",
        "HLAdherence",
        "HealthcareMap",
        "HealthcareResourceModel",
        "HealthcareTeamModel",
        "HealthConditionAxis",
        "HealthConditionRelated",
        "HealthConditionTemplate",
        "HealthDeclaration",
        "HealthForm",
        "HealthFormQuestion",
        "HealthLogicRecord",
        "HealthMeasurementModel",
        "HealthMeasurementTypeModel",
        "HealthPlanTaskGroupTemplate",
        "HealthPlanTaskTemplate",
        "HealthSpecialistResourceBundleModel",
        "HLAdherence",
        "HospitalizationInfoModel",
        "IntentionCoordination",
        "LaboratoryTestResultModel",
        "MedicalSpecialtyModel",
        "MemberModel",
        "MvAuthorizedProcedureModel",
        "OutcomeConf",
        "PersonCase",
        "PersonEligibilityDuquesa",
        "PersonHealthGoalModel",
        "PersonHealthLogic",
        "PersonHealthcareTeamRecommendationModel",
        "PersonModel",
        "PersonTeamAssociation",
        "ProviderUnitModel",
        "RiskCalculationConf",
        "ServiceScriptExecution",
        "ServiceScriptNode",
        "ServiceScriptRelationship",
        "StructuredAddress",
        "TestCodeModel",
        "TestCodePackageModel",
        "TestResultFeedback",
        "TestResultFileModel",
        "Timeline",
        "TotvsGuiaModel",
        "VideoCall",
        "WandaComment"
    ]

    every resource in resources {
        {1} == ehr_domain_service.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}

test_unauth_view_count_models_allowed if {
    resources = [
        "AppointmentEvolution",
        "BeneficiaryModel",
        "BeneficiaryOnboardingModel",
        "BeneficiaryOnboardingPhaseModel",
        "CassiMemberModel",
        "CompanyModel",
        "ProviderUnitModel"
    ]

    every resource in resources {
        {1,2} == ehr_domain_service.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}

test_unauth_view_create_models_allowed if {
    resources = [
        "CaseRecord"
    ]

    every resource in resources {
        {1,2} == ehr_domain_service.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 2,
                "action": "create",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}

test_unauth_view_update_models_allowed if {
    resources = [
        "DraftCommandModel",
        "HealthCondition",
        "HealthCommunityUnreferencedAccessModel"
    ]

    every resource in resources {
        {1,2} == ehr_domain_service.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 2,
                "action": "update",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}

test_unauth_view_create_update_models_allowed if {
    resources = [
        "PersonInternalReference",
        "HealthCommunitySpecialistModel",
        "PersonHealthEvent",
        "ClinicalBackground",
        "HealthMeasurementModel",
        "MemberHealthMetricModel",
        "AppointmentScheduleModel",
        "HealthPlan",
        "HealthPlanTask",
        "HealthPlanTaskGroup",
        "HealthLogicTracking",
        "ProtocolTracking",
        "HealthFormQuestionAnswer",
        "HealthFormAnswerGroup",
        "StaffModel",
        "HealthProfessionalModel",
        "PregnancyModel",
        "CounterReferral",
        "TertiaryIntentionTouchPoint",
        "ConsolidatedRewardsModel"
    ]

    every resource in resources {
        {1,2,3} == ehr_domain_service.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 2,
                "action": "create",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 3,
                "action": "update",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}

test_unauth_view_count_create_update_models_allowed if {
    resources = [
        "Appointment",
        "AssistanceSummaryModel"
    ]

    every resource in resources {
        {1,2,3,4} == ehr_domain_service.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 3,
                "action": "create",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 4,
                "action": "update",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}

test_unauth_view_create_update_delete_models_allowed if {
    resources = [
        "PersonClinicalAccount"
    ]

    every resource in resources {
        {1,2,3,4} == ehr_domain_service.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 2,
                "action": "create",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 3,
                "action": "update",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 4,
                "action": "delete",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}
