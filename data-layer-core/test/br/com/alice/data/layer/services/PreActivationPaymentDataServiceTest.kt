package br.com.alice.data.layer.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.DataServiceTestHelper
import br.com.alice.data.layer.models.PreActivationPaymentModel
import br.com.alice.data.layer.models.PreActivationPaymentStatus
import br.com.alice.data.layer.models.PreActivationPaymentType
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test

class PreActivationPaymentDataServiceTest : DataServiceTestHelper() {
    private val dataService = PreActivationPaymentModelDataServiceClient(httpInvoker)

    @Test
    fun `#add new PreActivationPaymentModel`() = runBlocking<Unit> {

        val model = buildPreActivationPaymentModel()

        assertThat(dataService.add(model).get())
            .usingRecursiveComparison()
            .ignoringFields("createdAt", "updatedAt")
            .isEqualTo(model)
    }

    @Test
    fun `#get PreActivationPaymentModel`() = runBlocking<Unit> {
        val model = buildPreActivationPaymentModel()
        dataService.add(model)

        val retrieved = dataService.get(model.id).get()

        assertThat(retrieved)
            .usingRecursiveComparison()
            .ignoringFields("createdAt", "updatedAt")
            .isEqualTo(model)
    }

    @Test
    fun `#update PreActivationPayment`() = runBlocking<Unit> {
        val model = buildPreActivationPaymentModel()
        dataService.add(model)
        dataService.update(model.copy(status = PreActivationPaymentStatus.PAID))

        val retrieved = dataService.get(model.id).get()

        assertThat(retrieved).usingRecursiveComparison()
            .ignoringFields("createdAt", "updatedAt")
            .isEqualTo(model.copy(status = PreActivationPaymentStatus.PAID, version = 1))
    }

    @Test
    fun `#delete PreActivationPayment`() = runBlocking<Unit> {
        val model = buildPreActivationPaymentModel()
        assertThat(dataService.add(model)).isSuccess()

        dataService.delete(model)

        val retrieved = dataService.get(model.id)

        assertThat(retrieved).isFailureOfType(NotFoundException::class)
    }

    @Test
    fun `#findByBillingAccountablePartyId PreActivationPaymentModel`() = runBlocking<Unit> {
        val model = buildPreActivationPaymentModel()
        dataService.add(model)

        val retrieved = dataService.findByBillingAccountablePartyId(model.billingAccountablePartyId).get()

        assertThat(retrieved)
            .usingRecursiveComparison()
            .ignoringFields("createdAt", "updatedAt")
            .isEqualTo(listOf(model))
    }
}

fun buildPreActivationPaymentModel(
    id: UUID = RangeUUID.generate(),
    memberInvoiceIds: List<UUID> = listOf(RangeUUID.generate()),
    billingAccountablePartyId: UUID = RangeUUID.generate(),
    referenceDate: LocalDate = LocalDate.now(),
    dueDate: LocalDate = LocalDate.now(),
    status: PreActivationPaymentStatus = PreActivationPaymentStatus.PROCESSED,
    externalId: String = RangeUUID.generate().toString(),
    type: PreActivationPaymentType = PreActivationPaymentType.B2B,
    totalAmount: BigDecimal = BigDecimal(1000.50),
) = PreActivationPaymentModel(
    id = id,
    externalId = externalId,
    memberInvoiceIds = memberInvoiceIds,
    billingAccountablePartyId = billingAccountablePartyId,
    referenceDate = referenceDate,
    dueDate = dueDate,
    status = status,
    totalAmount = totalAmount,
    type = type
)
