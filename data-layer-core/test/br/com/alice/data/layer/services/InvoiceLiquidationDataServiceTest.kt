package br.com.alice.data.layer.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.DataServiceTestHelper
import br.com.alice.data.layer.models.BusinessType
import br.com.alice.data.layer.models.InvoiceLiquidationModel
import br.com.alice.data.layer.models.InvoiceLiquidationStatus
import kotlinx.coroutines.runBlocking
import java.math.BigDecimal
import java.time.LocalDate
import kotlin.test.Test

class InvoiceLiquidationDataServiceTest : DataServiceTestHelper() {

    private val dataService = InvoiceLiquidationModelDataServiceClient(httpInvoker)

    @Test
    fun `#add new invoiceLiquidation`() = runBlocking {
        val invoiceLiquidation = InvoiceLiquidationModel(
            externalId = "DEFAULT_EXTERNAL_ID",
            amount = BigDecimal("0.00"),
            addition = BigDecimal.ZERO,
            discount = BigDecimal.ZERO,
            dueDate = LocalDate.now().plusDays(30),
            memberInvoiceGroupIds = listOf(RangeUUID.generate()),
            status = InvoiceLiquidationStatus.PROCESSED,
            billingAccountablePartyId = RangeUUID.generate(),
            companyIds = emptyList(),
            subcontractIds = emptyList(),
            companyId = null,
            subcontractId = null,
            installment = 1,
            totalInstallments = 1,
            businessType = BusinessType.B2B
        )
        assertThat(dataService.add(invoiceLiquidation)).isSuccessWithDataIgnoringGivenFields(
            invoiceLiquidation,
            "createdAt",
            "updatedAt",
            "updatedBy"
        )
    }

    @Test
    fun `#get invoiceLiquidation`() = runBlocking {
        val invoiceLiquidation = InvoiceLiquidationModel(
            externalId = "DEFAULT_EXTERNAL_ID",
            amount = BigDecimal("0.00"),
            addition = BigDecimal.ZERO,
            discount = BigDecimal.ZERO,
            dueDate = LocalDate.now().plusDays(30),
            memberInvoiceGroupIds = listOf(RangeUUID.generate()),
            status = InvoiceLiquidationStatus.PROCESSED,
            billingAccountablePartyId = RangeUUID.generate(),
            companyIds = emptyList(),
            subcontractIds = emptyList(),
            companyId = null,
            subcontractId = null,
            installment = 1,
            totalInstallments = 1,
            businessType = BusinessType.B2B
        )

        dataService.add(invoiceLiquidation)

        val invoiceLiquidationRetrieved = dataService.get(invoiceLiquidation.id)
        assertThat(invoiceLiquidationRetrieved).isSuccessWithDataIgnoringGivenFields(
            invoiceLiquidation,
            "createdAt",
            "updatedAt",
            "updatedBy"
        )
    }

    @Test
    fun `#update personContractualRisk`() = runBlocking {
        val invoiceLiquidation = InvoiceLiquidationModel(
            externalId = "DEFAULT_EXTERNAL_ID",
            amount = BigDecimal("0.00"),
            addition = BigDecimal.ZERO,
            discount = BigDecimal.ZERO,
            dueDate = LocalDate.now().plusDays(30),
            memberInvoiceGroupIds = listOf(RangeUUID.generate()),
            status = InvoiceLiquidationStatus.PROCESSED,
            billingAccountablePartyId = RangeUUID.generate(),
            companyIds = emptyList(),
            subcontractIds = emptyList(),
            companyId = null,
            subcontractId = null,
            installment = 1,
            totalInstallments = 1,
            businessType = BusinessType.B2B
        )

        val toUpdate = invoiceLiquidation.copy(amount = BigDecimal(99.99))

        dataService.add(invoiceLiquidation)
        dataService.update(toUpdate)

        val invoiceLiquidationRetrieved = dataService.get(invoiceLiquidation.id)
        assertThat(invoiceLiquidationRetrieved).isSuccessWithDataIgnoringGivenFields(
            toUpdate,
            "createdAt",
            "updatedAt",
            "updatedBy",
            "version"
        )
    }

    @Test
    fun `#find by memberInvoiceGroupIds`() = runBlocking {
        val memberInvoiceGroupId1 = RangeUUID.generate()
        val memberInvoiceGroupId2 = RangeUUID.generate()

        val invoiceLiquidation = InvoiceLiquidationModel(
            externalId = "DEFAULT_EXTERNAL_ID",
            amount = BigDecimal("0.00"),
            addition = BigDecimal.ZERO,
            discount = BigDecimal.ZERO,
            dueDate = LocalDate.now().plusDays(30),
            memberInvoiceGroupIds = listOf(memberInvoiceGroupId1),
            status = InvoiceLiquidationStatus.PROCESSED,
            billingAccountablePartyId = RangeUUID.generate(),
            companyIds = emptyList(),
            subcontractIds = emptyList(),
            companyId = null,
            subcontractId = null,
            installment = 1,
            totalInstallments = 1,
            businessType = BusinessType.B2B
        )

        dataService.add(invoiceLiquidation)

        val invoiceLiquidationsByContainsRetrieved = dataService.find {
            where {
                memberInvoiceGroupIds.contains(memberInvoiceGroupId1)
            }
        }

        assertThat(invoiceLiquidationsByContainsRetrieved).isSuccessWithDataIgnoringGivenFields(
            listOf(invoiceLiquidation),
            "createdAt",
            "updatedAt",
            "updatedBy"
        )

        val invoiceLiquidationsByContainsAnyRetrieved = dataService.find {
            where {
                memberInvoiceGroupIds.containsAny(listOf(memberInvoiceGroupId1, memberInvoiceGroupId2))
            }
        }

        assertThat(invoiceLiquidationsByContainsAnyRetrieved).isSuccessWithDataIgnoringGivenFields(
            listOf(invoiceLiquidation),
            "createdAt",
            "updatedAt",
            "updatedBy"
        )
    }

    @Test
    fun `#find by companyIds`() = runBlocking {
        val companyId1 = RangeUUID.generate()
        val companyId2 = RangeUUID.generate()
        val companyId3 = RangeUUID.generate()

        val invoiceLiquidation = InvoiceLiquidationModel(
            externalId = "DEFAULT_EXTERNAL_ID",
            amount = BigDecimal("0.00"),
            addition = BigDecimal.ZERO,
            discount = BigDecimal.ZERO,
            dueDate = LocalDate.now().plusDays(30),
            memberInvoiceGroupIds = listOf(RangeUUID.generate()),
            status = InvoiceLiquidationStatus.PROCESSED,
            billingAccountablePartyId = RangeUUID.generate(),
            companyIds = listOf(companyId1, companyId2),
            subcontractIds = emptyList(),
            companyId = null,
            subcontractId = null,
            installment = 1,
            totalInstallments = 1,
            businessType = BusinessType.B2B
        )

        dataService.add(invoiceLiquidation)

        var invoiceLiquidationsRetrieved = dataService.find {
            where {
                companyIds.contains(companyId1)
            }
        }

        assertThat(invoiceLiquidationsRetrieved).isSuccessWithDataIgnoringGivenFields(
            listOf(invoiceLiquidation),
            "createdAt",
            "updatedAt",
            "updatedBy"
        )

        invoiceLiquidationsRetrieved = dataService.find {
            where {
                companyIds.contains(companyId3)
            }
        }

        assertThat(invoiceLiquidationsRetrieved).isSuccessWithDataIgnoringGivenFields(
            emptyList(),
            "createdAt",
            "updatedAt",
            "updatedBy"
        )
    }

    @Test
    fun `#findBySubContractId`() = runBlocking {
        val subContractId = RangeUUID.generate()

        val invoiceLiquidation = InvoiceLiquidationModel(
            externalId = "DEFAULT_EXTERNAL_ID",
            amount = BigDecimal("0.00"),
            addition = BigDecimal.ZERO,
            discount = BigDecimal.ZERO,
            dueDate = LocalDate.now().plusDays(30),
            memberInvoiceGroupIds = listOf(RangeUUID.generate()),
            status = InvoiceLiquidationStatus.PROCESSED,
            billingAccountablePartyId = RangeUUID.generate(),
            companyIds = emptyList(),
            subcontractIds = listOf(subContractId),
            companyId = null,
            subcontractId = null,
            installment = 1,
            totalInstallments = 1,
            businessType = BusinessType.B2B
        )

        dataService.add(invoiceLiquidation)

        val invoiceLiquidationRetrieved = dataService.findBySubContractId(subContractId)
        assertThat(invoiceLiquidationRetrieved)
            .isSuccessWithDataIgnoringGivenFields(
                listOf(invoiceLiquidation),
                "createdAt",
                "updatedAt",
                "updatedBy"
            )
    }

    @Test
    fun `#findByBillingAccountablePartyId`() = runBlocking {
        val billingAccountablePartyId = RangeUUID.generate()

        val invoiceLiquidation = InvoiceLiquidationModel(
            externalId = "DEFAULT_EXTERNAL_ID",
            amount = BigDecimal("0.00"),
            addition = BigDecimal.ZERO,
            discount = BigDecimal.ZERO,
            dueDate = LocalDate.now().plusDays(30),
            memberInvoiceGroupIds = listOf(RangeUUID.generate()),
            status = InvoiceLiquidationStatus.PROCESSED,
            billingAccountablePartyId = billingAccountablePartyId,
            companyIds = emptyList(),
            subcontractIds = emptyList(),
            companyId = null,
            subcontractId = null,
            installment = 1,
            totalInstallments = 1,
            businessType = BusinessType.B2B
        )

        dataService.add(invoiceLiquidation)

        val invoiceLiquidationRetrieved = dataService.findByBillingAccountablePartyId(billingAccountablePartyId)
        assertThat(invoiceLiquidationRetrieved)
            .isSuccessWithDataIgnoringGivenFields(
                listOf(invoiceLiquidation),
                "createdAt",
                "updatedAt",
                "updatedBy"
            )
    }
}
