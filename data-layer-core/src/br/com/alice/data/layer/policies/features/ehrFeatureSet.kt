package br.com.alice.data.layer.policies.features

import br.com.alice.common.models.ExternalHealthInformation
import br.com.alice.common.models.HealthInformation
import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Delete
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.*
import br.com.alice.data.layer.policies.testPersonResource
import br.com.alice.data.layer.subjects.Unauthenticated

val createPersonInternalReference = policySet {
    match("can view and update and create", { action is View || action is Update || action is Create }) {
        match("any PersonInternalReference") { resource is PersonInternalReference }
    }
}

val scheduleAppointments = policySet {
    describe("at calendly-webhook") {
        match("can view", { action is View }) {
            match("any Person") { resource is PersonModel }
            match("any Healthcare Team") { resource is HealthcareTeamModel }
            match("any StaffModel") { resource is StaffModel }
            match("any DeviceModel") { resource is DeviceModel }
            match("any Appointment") { resource is Appointment }
        }
        match("can view and update  and create", { action is View || action is Update || action is Create }) {
            match("any AppointmentScheduleModel") { resource is AppointmentScheduleModel }
            match("any PersonClinicalAccount") { resource is PersonClinicalAccount }
            match("any PersonInternalReference") { resource is PersonInternalReference }
            match("any PersonHealthcareTeamRecommendationModel") { resource is PersonHealthcareTeamRecommendationModel }
        }
    }
}

val completeAppointments = policySet {
    describe("at complete-appointments-scheduler") {
        match("can view and update and create", { action is View || action is Update || action is Create }) {
            match("any Appointment") { resource is Appointment }
        }
    }
}

val viewAndCreateAppointmentEvolution = policySet {
    match("can view and create", { action is View || action is Create || action is Count }) {
        match("any AppointmentEvolution") { resourceIs(AppointmentEvolution::class) }
    }
}

val sendOverdueTasks = policySet {
    describe("at send-overdue-tasks") {
        match("can view", { action is View }) {
            match("any HealthPlanTask") { resource is HealthPlanTask }
        }
    }
}

val disassociatePersonFromHealthcareTeam = policySet {
    describe("at ehr-disassociate-person-from-healthcare-team") {
        match("can count", { action is Count }) {
            match("any Appointment") { resourceIs(Appointment::class) }
            match("any Appointment") { resourceIs(AppointmentEvolution::class) }
        }
        match("can view and delete", { action is View || action is Delete }) {
            match("any PersonClinicalAccount") { resourceIs(PersonClinicalAccount::class) }
        }
    }
}

val addStaffOnPersonClinicalAccount = policySet {
    match("can view", { action is View }) {
        match("any PersonClinicalAccount") { resourceIs(PersonClinicalAccount::class) }
        match("any StaffModel") { resourceIs(StaffModel::class) }
    }
    match("can update and create", { action is Update || action is Create }) {
        match("any PersonClinicalAccount") { resourceIs(PersonClinicalAccount::class) }
    }
}

val sendReferralReferenceLetter = policySet {
    describe("at ehr-send-referral-reference-letter") {
        match("can view", { action is View }) {
            match("any Health Information") { resourceIs(HealthInformation::class) }
            match("any PersonModel") { resourceIs(PersonModel::class) }
            match("any Health Community Specialist") { resourceIs(HealthCommunitySpecialistModel::class) }
            match("any StaffModel") { resourceIs(StaffModel::class) }
            match("any HealthDeclaration") { resource is HealthDeclaration }
            match("any MemberModel") { resource is MemberModel }
        }
        match("can update and create", { action is Update || action is Create }) {
            match("any Health Plan") { resourceIs(HealthPlan::class) }
            match("any Health Plan Task") { resourceIs(HealthPlanTask::class) }
        }
    }
}

val sendToAutomaticTaskEngine = policySet {
    match("can view", { action is View }) {
        match("any AssistanceCare") { resourceIs(AssistanceCare::class) }
        match("any ServiceScriptNode") { resourceIs(ServiceScriptNode::class) }
        match("any ServiceScriptRelationship") { resourceIs(ServiceScriptRelationship::class) }
        match("any ServiceScriptExecution") { resourceIs(ServiceScriptExecution::class) }
    }
}

val createHealthPlanFromHealthLogics = policySet {
    describe("at health logics consumer") {
        match("can view", { action is View }) {
            match("HealthcareTeamModel") { resourceIs(HealthcareTeamModel::class) }
            match("HealthPlanTask") { resourceIs(HealthPlanTask::class) }
            match("HealthPlanTaskGroup") { resourceIs(HealthPlanTaskGroup::class) }
            match("HealthPlanTaskTemplate") { resourceIs(HealthPlanTaskTemplate::class) }
            match("HealthPlanTaskGroupTemplate") { resourceIs(HealthPlanTaskGroupTemplate::class) }
            match("HealthLogicTracking") { resourceIs(HealthLogicTracking::class) }
            match("ProtocolTracking") { resourceIs(ProtocolTracking::class) }
            match("CaseRecord") { resourceIs(CaseRecord::class) }
            match("PersonCase") { resourceIs(PersonCase::class) }
        }
        match("can update and create", { action is Update || action is Create }) {
            match("HealthPlanTaskGroup") { resourceIs(HealthPlanTaskGroup::class) }
            match("HealthPlanTask") { resourceIs(HealthPlanTask::class) }
            match("ProtocolTracking") { resourceIs(ProtocolTracking::class) }
            match("HealthLogicTracking") { resourceIs(HealthLogicTracking::class) }
        }
    }
}

val updateHealthPlanChannels = policySet {
    describe("at ehr-update-health-plan-channels") {
        match("can view", { action is View }) {
            match("any PersonClinicalAccount") { resourceIs(PersonClinicalAccount::class) }
            match("any HealthcareTeamModel") { resourceIs(HealthcareTeamModel::class) }
            match("any StaffModel") { resourceIs(StaffModel::class) }
        }
    }
}

val updatePersonHealthCareTeam = policySet {
    describe("at ehr-update-person-clinical-account") {
        match("can view and update and create", { action is View || action is Update || action is Create }) {
            match("any PersonClinicalAccount") { resourceIs(PersonClinicalAccount::class) }
        }
        match("can view", { action is View }) {
            match("any StaffModel") { resourceIs(StaffModel::class) }
        }
    }
}

val archivePersonChannels = policySet {
    describe("at ehr-domain-service") {
        match("Unauthenticated", { subject is Unauthenticated }) {
            match("can view", { action is View }) {
                match("any HealthcareTeamModel") { resource is HealthcareTeamModel }
                match("PersonModel's MemberModel") {
                    resource is MemberModel && (subject as Unauthenticated).key == resource.personId.toString()
                }
                match("PersonModel's PersonClinicalAccount") {
                    resource is PersonClinicalAccount && (subject as Unauthenticated).key == resource.personId.toString()
                }
                match("PersonModel's PersonInternalReference") {
                    resource is PersonInternalReference && (subject as Unauthenticated).key == resource.personId.toString()
                }
            }
        }
    }
}

val calendlyScheduleOrCancel = policySet {
    match("can view", { action is View }) {
        match("any PersonModel") { resourceIs(PersonModel::class) }
        match("any HealthCommunitySpecialistModel") { resourceIs(HealthCommunitySpecialistModel::class) }
        match("any Healthcare Team") { resourceIs(HealthcareTeamModel::class) }
        match("any Health Plan Task") { resourceIs(HealthPlanTask::class) }
        match("any Health Plan Task Group") { resourceIs(HealthPlanTaskGroup::class) }
    }
    match("can view and update and create", { action is View || action is Update || action is Create }) {
        match("any AppointmentScheduleModel") { resourceIs(AppointmentScheduleModel::class) }
    }
}

val updateMemberHealthMetric = policySet {
    match("can view", { action is View }) {
        match("MemberModel") { resourceIs(MemberModel::class) }
        match("AppointmentScheduleType") { resourceIs(AppointmentScheduleType::class) }
    }
    match("can view and update and create", { action is View || action is Update || action is Create }) {
        match("MemberHealthMetricModel") { resourceIs(MemberHealthMetricModel::class) }
    }
}

val completedHealthForm = policySet {
    describe("at ehr-completed-health-form") {
        match("can view", { action is View }) {
            match("HealthFormQuestion") { resourceIs(HealthFormQuestion::class) }
            match("HealthFormQuestionAnswer") { resourceIs(HealthFormQuestionAnswer::class) }
        }
        match("can view and update and create", { action is View || action is Update || action is Create }) {
            match("ClinicalBackground") { resourceIs(ClinicalBackground::class) }
        }
    }
}

val publishHealthFormToHealthLogic = policySet {
    describe("at ehr-completed-health-form") {
        match("can view", { action is View }) {
            match("HealthForm") { resourceIs(HealthForm::class) }
            match("HealthFormQuestionAnswer") { resourceIs(HealthFormQuestionAnswer::class) }
            match("PersonModel") { resourceIs(PersonModel::class) }
            match("HealthcareTeamModel") { resourceIs(HealthcareTeamModel::class) }
            match("TestCode") { resourceIs(TestCodeModel::class) }
            match("TestCodePackage") { resourceIs(TestCodePackageModel::class) }
            match("HealthPlanTaskGroup") { resourceIs(HealthPlanTaskGroup::class) }
            match("HealthPlanTask") { resourceIs(HealthPlanTask::class) }
        }
        match("can update and create", { action is Update || action is Create }) {
            match("HealthPlanTaskGroup") { resourceIs(HealthPlanTaskGroup::class) }
            match("HealthPlanTask") { resourceIs(HealthPlanTask::class) }
        }
    }
}

val autoCompleteHealthPlanTask = policySet {
    describe("at auto-complete-health-plan-task") {
        match("can view and update and create", { action is View || action is Update || action is Create }) {
            match("HealthPlanTask") { resourceIs(HealthPlanTask::class) }
        }
        match("can view", { action is View }) {
            match("CounterReferral") { resourceIs(CounterReferral::class) }
        }
    }
}

val archiveChannelsWhenMemberCancelled = policySet {
    describe("at archive-channels-when-member-cancelled") {
        match("can view", { action is View }) {
            match("MemberModel") { resourceIs(MemberModel::class) }
            match("PersonClinicalAccount") { resourceIs(PersonClinicalAccount::class) }
            match("HealthcareTeamModel") { resourceIs(HealthcareTeamModel::class) }
            match("PersonInternalReference") { resourceIs(PersonInternalReference::class) }
        }
    }
}

val backfillHealthFormQuestionAnswer = policySet {
    describe("at backfill-health-form-question-answer") {
        match("can view and update", { action is View || action is Update || action is Create }) {
            match("any Health Form Question Answer") { resource is HealthFormQuestionAnswer }
            match("any Health Form Answer Group") { resource is HealthFormAnswerGroup }
        }
    }
}

val backfillHealthCondition = policySet {
    describe("at backfill-health-conditions-search-tokens") {
        match("can view and update", { action is View || action is Update }) {
            match("any Health Condition") { resource is HealthCondition }
        }
    }
}

val backfillHealthMeasurements = policySet {
    describe("at backfill-health-measurements-type-id") {
        match("can view", { action is View }) {
            match("any Health Measurement Type") { resource is HealthMeasurementTypeModel }
        }

        match("can view and update", { action is View || action is Update }) {
            match("any Health Measurement") { resource is HealthMeasurementModel }
        }
    }
}

val backfillStaffCouncilNumber = policySet {
    describe("at backfill-staff-council-number") {
        match("can view and update", { action is View || action is Update }) {
            match("any StaffModel") { resource is StaffModel }
            match("any HealthProfessionalModel") { resource is HealthProfessionalModel }
        }
    }
}

val clinicalOutcomeRecordCreated = policySet {
    describe("at clinical-outcome-record-created-event consumer") {
        match("can view", { action is View }) {
            match("any Health Measurement Type") { resource is HealthMeasurementTypeModel }
            match("any Outcome Conf") { resource is OutcomeConf }
        }

        match("can view and update and create", { action is View || action is Update || action is Create }) {
            match("any Health Measurement") { resource is HealthMeasurementModel }
        }
    }
}

val viewAppointmentsAndExams = policySet {
    match("can view", { action is View }) {
        match("any PersonModel") { resource is PersonModel }
        match("any MemberModel") { resource is MemberModel }
        match("any Timeline") { resource is Timeline }
        match("any Appointment") { resource is Appointment }
        match("any AssistanceCare") { resource is AssistanceCare }
        match("any AppointmentEvolution") { resource is AppointmentEvolution }
        match("any CounterReferral") { resource is CounterReferral }
        match("any ExternalHealthInformation") { resource is ExternalHealthInformation }
        match("any TestResultFileModel") { resource is TestResultFileModel }
        match("any TertiaryIntentionTouchPoint") { resource is TertiaryIntentionTouchPoint }
        match("can view", { action is View }) {
            match("any exam document") {
                resource is FileVault &&
                        resource.domain == "interop" &&
                        resource.namespace == "exam_by_provider"
            }
            match("any provider health document in vault") {
                resource is FileVault &&
                        resource.domain == "eita" &&
                        resource.namespace == "provider_health_document"
            }
        }
    }
}

val viewHealthPlan = policySet {
    match("can view", { action is View }) {
        match("any PersonModel") { resource is PersonModel }
        match("any MemberModel") { resource is MemberModel }
        match("any HealthPlanTaskGroup") { resource is HealthPlanTaskGroup }
        match("any HealthPlan") { resource is HealthPlan }
        match("any PersonTaskModel") { resource is PersonTaskModel }
        match("any HealthPlanTask") { resource is HealthPlanTask }
    }
}

val viewAndUpdateHealthPlan = policySet {
    match("can view", { action is View }) {
        match("any PersonModel") { resource is PersonModel }
        match("any MemberModel") { resource is MemberModel }
    }
    match("an view and update and create", { action is View || action is Update || action is Create }) {
        match("any HealthPlanTaskGroup") { resource is HealthPlanTaskGroup }
        match("any HealthPlan") { resource is HealthPlan }
        match("any PersonTaskModel") { resource is PersonTaskModel }
        match("any HealthPlanTask") { resource is HealthPlanTask }
    }
}

val viewEhrClinicalBackground = policySet {
    match("can view", { action is View }) {
        match("any PersonModel") { resource is PersonModel }
        match("any MemberModel") { resource is MemberModel }
        match("any HealthDeclaration") { resource is HealthDeclaration }
        match("any ClinicalBackground") { resource is ClinicalBackground }
        match("any HealthMeasurementModel") { resource is HealthMeasurementModel }
        match("any HealthFormQuestionAnswer") { resource is HealthFormQuestionAnswer }
    }
}
val viewAndUpdateEhrClinicalBackground = policySet {
    match("can view", { action is View }) {
        match("any PersonModel") { resource is PersonModel }
        match("any MemberModel") { resource is MemberModel }
    }
    match("an view and update and create", { action is View || action is Update || action is Create }) {
        match("any HealthDeclaration") { resource is HealthDeclaration }
        match("any ClinicalBackground") { resource is ClinicalBackground }
        match("any HealthMeasurementModel") { resource is HealthMeasurementModel }
    }
}

val viewMemberProfile = policySet {
    match("can view", { action is View }) {
        match("any PersonModel") { resource is PersonModel }
        match("any MemberModel") { resource is MemberModel }
        match("any PersonModel Internal Reference") { resource is PersonInternalReference }
        match("any PersonModel Clinical Account") { resource is PersonClinicalAccount }
        match("any PersonModel Additional Info") { resource is PersonAdditionalInfoModel }
        match("any CuriosityNoteModel") { resource is CuriosityNoteModel }
        match("any OngoingCompanyDeal") { resource is OngoingCompanyDeal }
    }
}
val viewAndUpdateMemberProfile = policySet {
    match("can view", { action is View }) {
        match("any PersonModel") { resource is PersonModel }
        match("any MemberModel") { resource is MemberModel }
    }
    match("an view and update and create", { action is View || action is Update || action is Create }) {
        match("any PersonModel Internal Reference") { resource is PersonInternalReference }
        match("any PersonModel Clinical Account") { resource is PersonClinicalAccount }
        match("any PersonModel Additional Info") { resource is PersonAdditionalInfoModel }
        match("any CuriosityNoteModel") { resource is CuriosityNoteModel }
    }
}

val viewCaseRecord = policySet {
    match("can view", { action is View }) {
        match("any CaseRecord") { resource is CaseRecord }
        match("any PersonCase") { resource is PersonCase }
    }
}

val viewAndCreateTestResultFeedback = policySet {
    match("can view and create", { action is View || action is Create }) {
        match("any Test Result Feedback") { resource is TestResultFeedback }
    }
}

val viewClinicalOutcomeRecordInListOfHealthDemandDetails = policySet {
    match("can view", { action is View }) {
        match("any ClinicalOutcomeRecord") { resource is ClinicalOutcomeRecord }
        match("any OutcomeConf") { resource is OutcomeConf }
        match("any HLActionRecommendation") { resource is HLActionRecommendation }
    }
}

val viewQuestionnaire = policySet {
    match("view Questionnaire", { action is View }) {
        match("any Health Form") { resource is HealthForm }
        match("any Health Form Section") { resource is HealthFormSection }
        match("any Health Form Question") { resource is HealthFormQuestion }
        match("Health Form Answer Group") { resource is HealthFormAnswerGroup }
        match("any Health Form Question Answer") { testPersonResource(HealthFormQuestionAnswer::class) }
    }
}

val viewSpecialistOpinion = policySet {
    allows(SpecialistOpinion::class, Count, View)
    allows(SpecialistOpinionMessage::class, Count, View)
}

val upsertSpecialistOpinion = policySet {
    allows(SpecialistOpinion::class, Create, Update)
    allows(SpecialistOpinionMessage::class, Create)
}

val viewHealthPlanTaskReferrals = policySet {
    allows(HealthPlanTaskReferrals::class, Count, View)
}

val viewStaffFileVault = policySet {
    allows(GenericFileVault::class, Count, View)
}

val createStaffFileVault = policySet {
    allows(GenericFileVault::class, Create)
}

val viewFileVault = policySet {
    allows(FileVault::class, Count, View)
}

val assistanceSummaryUpsert = policySet {
    allows(AssistanceSummaryModel::class, View, Count, Create, Update)
}

val viewAssistanceSummary = policySet {
    allows(AssistanceSummaryModel::class, View)
}

val viewRiskCalculationConf = policySet {
    allows(RiskCalculationConf::class, View)
}

val createHealthPlanTaskFromMemberOnboarding = policySet {
    allows(PersonModel::class, View)
    allows(Risk::class, View)
    allows(HealthCondition::class, View)
    allows(CaseRecord::class, View, Create)
    allows(PersonCase::class, View)
    allows(PersonClinicalAccount::class, View)
    allows(HealthcareTeamModel::class, View)
    allows(StaffModel::class, View)
    allows(OutcomeConf::class, View)
    allows(ClinicalOutcomeRecord::class, View)
    allows(HealthPlanTaskTemplate::class, View)
    allows(HealthPlanTask::class, Create)
    allows(MemberOnboarding::class, View, Update)
}
