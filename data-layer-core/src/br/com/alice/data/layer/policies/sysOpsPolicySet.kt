package br.com.alice.data.layer.policies

import br.com.alice.common.core.StaffType
import br.com.alice.common.core.Subject
import br.com.alice.data.layer.SYSTEM_OPS_ROOT_SERVICE_NAME
import br.com.alice.data.layer.ServiceConfig
import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.ActionPlanTask
import br.com.alice.data.layer.models.AppointmentScheduleEventTypeModel
import br.com.alice.data.layer.models.CassiSpecialistModel
import br.com.alice.data.layer.models.CompanyStaffModel
import br.com.alice.data.layer.models.CoveredGeoRegionModel
import br.com.alice.data.layer.models.DemandActionPlan
import br.com.alice.data.layer.models.HealthProfessionalModel
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleModel
import br.com.alice.data.layer.models.ResourceBundleSpecialtyModel
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingModel
import br.com.alice.data.layer.models.RiskCalculationConf
import br.com.alice.data.layer.models.RiskGroup
import br.com.alice.data.layer.models.RoutingRule
import br.com.alice.data.layer.models.ScreenData
import br.com.alice.data.layer.models.StaffModel
import br.com.alice.data.layer.policies.features.*
import br.com.alice.data.layer.subjects.EmailDomain
import br.com.alice.data.layer.subjects.Unauthenticated

val sysOpsPolicySet = policySet {
    match("at system ops", { rootService.name == SYSTEM_OPS_ROOT_SERVICE_NAME }) {

        match("Unauthenticated", { subject is Unauthenticated }) {
            includes(doLogin)
        }

        match("StaffModel", { subject is StaffModel }) {
            includes(viewAppointmentMacro)
        }

        match("B2B - Ops or Ops Role", { subject is StaffModel && (subject.isB2BOps() || subject.isOps()) }) {
            includes(viewAndUpdatePerson)
            includes(viewAndUpdateMember)
            includes(viewAndUpdateBeneficiary)
            includes(viewUpdateAndCreateBeneficiaryOnboardingPhase)
            includes(viewUpdateAndCreatePersonBillingAccountableParty)
            includes(viewAndUpdateCompany)
            includes(viewAndUpdateCompanyStaff)
            includes(viewPersonInternalReference)
            includes(viewAndUpdateHealthcareTeam)
            includes(viewMember)
            includes(viewProductComponents)
            includes(viewHealthCondition)
            includes(scheduleAppointments)
            includes(viewUpdateAndDeleteFeatureConfig)
            includes(viewProviders)
            includes(canReactivateMember)
            includes(createProducts)
            includes(viewAndUpdateAnyContractFiles)
            includes(viewAndCreateMemberContract)
            includes(viewAndUpdateAnyTermFiles)
            includes(viewBeneficiaryHubspot)
            includes(viewHealthDeclaration)
        }

        match("StaffModel", { subject is StaffModel }) {
            includes(viewStaff)
            includes(viewHealthProfessional)
            includes(viewHealthProfessionalOpsProfile)
            includes(viewHealthcareTeam)
            includes(viewPersonHealthcareTeam)
            includes(viewProductComponents)
            includes(viewHealthFormAndSubModels)
            includes(viewAndUpdateProviderUnitTestCodes) // TODO: remove from StaffModel after it is on every specific StaffModel
            includes(viewAndUpdateAppointmentScheduleOption) // TODO: remove from StaffModel after it is on every specific StaffModel
            includes(viewUpdateAndDeleteTestPreparation) // TODO: remove from StaffModel after it is on every specific StaffModel
            includes(viewUpdateHealthGoal) // TODO: remove after configured Staffs with Insurance Ops Lead
            includes(viewFeatureConfig)
            includes(viewProtocols)
            includes(viewHealthCondition)
            includes(viewHealthLogic)
            includes(viewTestCodes)
            includes(viewTestCodePackages)
            includes(viewProviders)
            includes(viewProviderUnits)
            includes(viewLocalities)
            includes(viewProviderUnitGroups)
            includes(viewProviderTestCodes)
            includes(viewMedicalSpecialties)
            includes(viewCboCodes)
            includes(viewCompany)
            includes(viewCompanyStaff)
            includes(viewBeneficiary)
            includes(viewCountPersonBillingAccountableParty)
            includes(viewCountBillingAccountableParty)
            includes(viewCountBeneficiaryOnboarding)
            includes(viewCountBeneficiaryOnboardingPhase)
            includes(viewCreateUpdateAndDeleteInsurancePortabilityHealthInsurance)
            includes(viewCreateUpdateAndDeleteInsurancePortabilityRequest)
            includes(viewCreateUpdateAndDeleteInsurancePortabilityRequestFile)
            includes(viewMember)
            includes(viewChannelFup)
            includes(viewHealthFormOutcomeCalculatorConf)
            includes(viewAndUpdateAndCreateOnboardingTemplate)
            includes(viewBeneficiaryHubspot)
            includes(viewAndCountHealthcareResources)
            allows(AppointmentScheduleEventTypeModel::class, View, Count, Update, Create)
            allows(CassiSpecialistModel::class, View, Count, Update, Create)
        }
        match("Product and Tech", { subject is StaffModel && subject.isProductTech() }) {
            includes(productAndTechPolicySet)
            includes(viewAndUpdateAndCreateProcedureProvider)
            includes(viewAddress)
            includes(viewCreateUpdateAndDeleteAppointmentTemplate)
            includes(viewAndUpdateAndCreateProfessionalTierProcedureValues)
            includes(viewAndUpdateAndCreateUpdateAppRule)
            includes(viewRiskData)
            includes(viewUpdateAndCreateHealthPlanTask)
            includes(viewUpdateAndCreateHealthPlanTaskGroup)
            includes(viewAndCreateAndUpdatePromoCode)
            includes(viewAndCreateUpdateProcedures)
            includes(viewAndCreateAndUpdateTussProcedureSpecialty)
            includes(viewAndCreatedUpdatedDeletedTestCodeAnalyte)
            includes(viewAndCreateAndUpdateAnalyteOutcomeMapping)
            includes(viewAndUpdateSpecialistOpinion)
            includes(viewAndCreateCboCodes)
            includes(viewAndCreateExternalHealthProfessionals)
            includes(viewAndCreateHealthcareBundles)
            allows(ActionPlanTask::class, View, Count, Create, Update)
            allows(DemandActionPlan::class, View, Count, Create, Update)
            includes(viewUpdateGasB2cAllowedMembers)
            includes(viewSalesFirm)
            includes(upsertSalesFirm)
            includes(viewCsatTemplate)
            includes(viewAndUpdateCompany)
            includes(viewAndUpdateBeneficiary)
            allows(
                CompanyStaffModel::class,
                View, Update, Create, Count,
            )
            allows(
                CoveredGeoRegionModel::class,
                View, Update, Create, Count,
            )
            includes(viewCreateUpdateDeleteSiteAccreditedNetwork)
            includes(viewCountSalesAgent)
            includes(createUpdateSalesAgent)
            includes(viewCreateUpdateCountVicProductOption)
            includes(viewCreateUpdateCountProductGroups)
            includes(viewAndUpdateFaqGroups)
            includes(viewAndUpdateFaqContents)
            allows(HealthSpecialistResourceBundleModel::class, View)
            allows(ResourceBundleSpecialtyModel::class, View)
            allows(ResourceBundleSpecialtyPricingModel::class, View)
        }
        match("Product and Tech Health", { subject is StaffModel && subject.isProductTechHealth() }) {
            includes(productAndTechPolicySet)
            includes(viewAndUpdateAndCreateProcedureProvider)
            includes(viewAndUpdateHealthLogic)
            includes(viewAndUpdateEta)
            includes(viewAndUpdateProtocols)
            includes(viewUpdateAndCreateHealthPlanTaskTemplate)
            includes(viewUpdateAndCreateHealthPlanTaskGroupTemplate)
            includes(viewCreateUpdateRiskGroup)
            includes(viewCreateUpdateCountRiskCalculationConf)
            includes(viewAndUpdateOutcomeConf)
            includes(viewAndUpdateHealthDemandMonitoring)
            includes(viewCreateUpdateAndDeleteAppointmentTemplate)
            includes(viewAndUpdateProtocol)
            includes(registerAppointmentMacro)
            includes(viewAndCreateHealthForm)
            includes(viewAndCreateExternalHealthProfessionals)
            includes(viewAndCreateHealthcareBundles)
            includes(viewCreateUpdateCsatTemplate)
            allows(ScreenData::class, View, Count, Create, Update)
            allows(RoutingRule::class, View, Count, Create, Update)
            includes(viewCountSalesAgent)
            includes(createUpdateSalesAgent)
            includes(viewAndUpdateChannelsConf)
        }
        match("Physician", { subject is StaffModel && subject.isPhysician() }) {
            includes(viewAndUpdatePrescriptions)
        }
        match("Manage Nutritionist", { subject is StaffModel && subject.isManageNutritionist() }) {
            includes(viewAndUpdateEta)
            includes(viewProtocols)
            includes(viewHealthLogic)
            includes(viewAndUpdateHealthForm)
        }
        match("Nurse or Navigator", { subject is StaffModel && subject.isNurseOrNavigator() }) {
            includes(viewAndUpdatePrescriptions)
            includes(viewAndUpdateHealthForm)
        }
        match("Digital Care Nurse", { subject is StaffModel && subject.isDigitalCareNurse() }) {
            includes(viewAndUpdateEta)
            includes(viewHealthLogic)
        }
        match("Digital Care Physician", { subject is StaffModel && subject.isDigitalCarePhysician() }) {
            includes(viewAndUpdateEta)
            includes(viewHealthLogic)
        }
        match("Care Coord Nurse", { subject is StaffModel && subject.isCareCoordNurse() }) {
            includes(viewAndUpdateEta)
            includes(viewAndUpdateHealthLogic)
            includes(viewAndUpdateProtocols)
            includes(viewAndUpdateTestCodes)
            includes(viewAndUpdateTestCodePackage)
            includes(viewUpdateAndDeleteTestPreparation)
            includes(viewAndUpdateChannelsConf)
            includes(viewAndUpdateHealthForm)
            includes(viewAndUpdatePrescriptions)
            includes(viewAndUpdateHealthCommunitySpecialists)
            includes(viewAndCreateCboCodes)
            includes(viewAndUpdateHealthCommunitySpecialists)
            includes(viewUpdateAndCreateHealthPlanTaskTemplate)
            includes(viewUpdateAndCreateHealthPlanTaskGroupTemplate)
        }
        match("Health Ops", { subject is StaffModel && subject.isHealthOps() }) {
            includes(viewAndUpdateHealthcareTeam)
            includes(viewAndUpdatePersonHealthcareTeam)
            includes(viewAndUpdateAppointmentScheduleOption)
            includes(viewAndUpdateTestCodes)
            includes(viewAndUpdateTestCodePackage)
            includes(viewAndCreatedUpdatedDeletedTestCodeAnalyte)
            includes(viewUpdateAndDeleteTestPreparation)
            includes(viewAndUpdateChannelsConf)
            includes(viewAndUpdatePrescriptions)
            includes(viewAndUpdateHealthForm)
            includes(viewUpdateHealthGoal)
            includes(viewAndUpdateProductAndComponents)
            includes(viewUpdateAndDeleteHealthCondition)
            includes(viewAndCreateCboCodes)
            includes(viewAndUpdateHealthCommunitySpecialists)
            includes(viewAndUpdateHealthProfessional)
            includes(viewUpdateAndCreateHealthProfessionalOpsProfile)
            includes(viewUpdateAndDeleteFeatureConfig)
            includes(viewUpdateAndCreateHealthPlanTaskTemplate)
            includes(viewUpdateAndCreateHealthPlanTaskGroupTemplate)
            includes(viewAndUpdateEta)
            includes(viewAndUpdateHealthLogic)
            includes(viewAndUpdateHealthDemandMonitoring)
            includes(viewAndUpdateProtocols)
            includes(viewAndCountAndUpdateAndCreateChannelFup)
            includes(viewUpdateAndCreateAppointmentTemplate)
            includes(viewHealthFormOutcomeCalculatorConf)
            includes(viewAndUpdateOutcomeConf)
            includes(createAndUpdateHealthFormOutcomeCalculatorConf)
            includes(configureConsolidatedCalculatorConf)
            includes(viewAndUpdateProtocol)
            includes(viewUpdateAndCreateHealthMeasurementTypeAndCategory)
            includes(registerAppointmentMacro)
        }
        match("Health Ops - Multi", {
            subject is StaffModel && (
                    subject.isHealthOpsMulti() || subject.isHealthOpsLead()
                    )
        }) {
            includes(viewAndUpdateHealthcareTeam)
            includes(viewAndUpdatePersonHealthcareTeam)
            includes(viewAndUpdateAppointmentScheduleOption)
            includes(viewAndUpdateHealthLogic)
            includes(viewAndUpdateHealthCommunitySpecialists)
            includes(viewAndCreateCboCodes)
        }
        match("Member Ops", { subject is StaffModel && subject.isMemberOps() }) {
            includes(viewAddress)
            includes(viewAppointmentSchedule)
            includes(viewCreateUpdateAndDeleteInsurancePortabilityRequest)
            includes(viewCreateUpdateAndDeleteInsurancePortabilityRequestFile)
        }
        match("Chief Risk", { subject is StaffModel && subject.isChiefRisk() }) {
            includes(viewAndUpdatePerson)
            includes(viewAndUpdateProviders)
            includes(viewAndUpdateProductAndComponents)
            includes(viewUpdateAndCreateBillingAccountableParty)
            includes(viewUpdateAndDeleteHealthCondition)
            includes(viewUpdateAndCreateBeneficiaryOnboardingPhase)
            includes(viewAndUpdateProtocols)
            includes(registerAppointmentMacro)

            allows(RiskCalculationConf::class, View, Count)
            allows(RiskGroup::class, View, Count)
        }
        match("Risk Nurse", { subject is StaffModel && subject.isRiskNurse() }) {
            includes(viewUpdateAndDeleteHealthCondition)
            includes(viewUpdateAndCreateBeneficiaryOnboardingPhase)
        }
        match("Med Risk", { subject is StaffModel && subject.isMedRisk() }) {
            includes(viewAndUpdatePerson)
            includes(viewAndUpdateProviders)
            includes(viewAndUpdateProductAndComponents)
            includes(viewUpdateAndCreateBillingAccountableParty)
            includes(viewUpdateAndDeleteHealthCondition)
        }
        match("MedEx", { subject is StaffModel && subject.isMedEx() }) {
            includes(viewAndUpdateEta)
            includes(viewAndUpdateHealthLogic)
            includes(viewAndUpdateHealthDemandMonitoring)
            includes(viewAndUpdateProtocols)
            includes(viewAndUpdateProtocol)
            includes(viewAndUpdateHealthForm)
            includes(viewUpdateAndCreateHealthPlanTaskTemplate)
            includes(viewUpdateAndCreateHealthPlanTaskGroupTemplate)
        }
        match("Ops Role", { subject is StaffModel && subject.isOps() }) {
            includes(viewAndUpdatePerson)
            includes(viewAndUpdateProviders)
            includes(viewAndUpdateProductAndComponents)
            includes(viewUpdateAndCreateBillingAccountableParty)
            includes(viewCreateUpdateAndDeleteInsurancePortabilityRequest)
            includes(viewCreateUpdateAndDeleteInsurancePortabilityRequestFile)
            includes(viewCreateUpdateAndDeleteInsurancePortabilityHealthInsurance)
            includes(upsertSalesFirm)
        }
        match("Health Community", { subject is StaffModel && subject.isHealthCommunity() }) {
            includes(viewAndUpdateProviders)
            includes(viewAndUpdateProviderUnits)
            includes(viewAndUpdateLocality)
            includes(viewAndUpdateProviderUnitGroups)
            includes(viewAndCreateCboCodes)
            includes(viewAndUpdateHealthCommunitySpecialists)
        }
        match(
            "Insurance Ops - Health Institution Ops and Insurance Ops - Community Success",
            {
                subject is StaffModel && (
                        subject.isInsuranceOpsHealthInstitutionOps() ||
                                subject.isInsuranceOpsCommunitySuccess()
                        )
            }) {
            includes(viewAndUpdatePerson)
            includes(viewAndUpdateAndCreateProcedureProvider)
            includes(viewAndUpdateTestCodes)
            includes(viewAndUpdateTestCodePackage)
            includes(viewUpdateAndDeleteTestPreparation)
            includes(viewAndUpdateExecIndicatorAuthorizer)
            includes(viewAndUpdateProviders)
            includes(viewAndUpdateProviderUnits)
            includes(viewAndUpdateLocality)
            includes(viewAndUpdateProviderUnitGroups)
            includes(viewUpdateAndDeleteProviderTestCodes)
            includes(viewAndUpdateHealthCommunitySpecialists)
            includes(viewAndUpdateAppointmentScheduleOption)
            includes(viewAndUpdateProductAndComponents)
            includes(viewAndCreateUpdateProcedures)
            includes(viewAndCreateAndUpdateTussProcedureSpecialty)
            allows(HealthSpecialistResourceBundleModel::class, View)
            allows(ResourceBundleSpecialtyModel::class, View)
            allows(ResourceBundleSpecialtyPricingModel::class, View)
        }
        match("Health Professional or Navigator", { subject is StaffModel && subject.isHealthProfessionalOrNavigator() }) {
            includes(viewUpdateAndCreateHealthPlanTaskTemplate)
            includes(viewUpdateAndCreateHealthMeasurementTypeAndCategory)
            includes(viewAndCountAndUpdateAndCreateChannelFup)
            includes(viewAndUpdateHerOwnHealthProfessional)
            includes(viewUpdateAndCreateHealthPlanTaskGroupTemplate)
            includes(viewUpdateAndCreateAppointmentTemplate)
        }
        match("StaffModel editors", { checkStaffEditor(subject) }) {
            match(
                "can view, count and update and create",
                { action is View || action is Count || action is Update || action is Create }) {
                match("any StaffModel") { resourceIs(StaffModel::class) }
            }
            includes(viewUpdateAndCreateHealthProfessional)
            includes(viewUpdateAndCreateHealthProfessionalOpsProfile)
        }
        match("Navigator or Chief Digital Care Nurse or Quality Nurse", { subject is StaffModel && (subject.isNavigator() || subject.isChiefDigitalCareNurse() || subject.isQualityNurse()) }) {
            includes(viewAndUpdatePerson)
            includes(viewPersonInternalReference)
            includes(viewMember)
            includes(viewAndUpdateTestCodes)
            includes(viewAndUpdateTestCodePackage)
            includes(viewUpdateAndDeleteTestPreparation)
            includes(viewAppointmentsAndExams)
            includes(viewHealthcareTeam)
            includes(viewAndUpdatePersonHealthcareTeam)
            includes(viewStaff)
            includes(viewHealthCommunitySpecialists)
            allows(
                CompanyStaffModel::class,
                View, Update, Create, Count,
            )
        }
        match("Navigator Chief", { subject is StaffModel && subject.isChiefNavigator() }) {
            includes(viewAndUpdateChannelTags)
            includes(viewAndUpdateChannelMacros)
        }
        match("Alice Health Professional", { subject is StaffModel && subject.isAliceHealthProfessional() }) {
            includes(viewHealthcareTeam)
            includes(viewAndUpdatePersonHealthcareTeam)
            includes(viewProtocols)
            includes(viewEta)
            includes(viewHealthLogic)
            includes(viewAndUpdateChannelsConf)
        }
        match("Digital Care Nurse Or Physician Chief", { subject is StaffModel && subject.isChiefDigitalCare() }) {
            includes(viewAndUpdateHealthcareTeam)
            includes(viewAndUpdateTestCodes)
            includes(viewAndUpdateTestCodePackage)
            includes(viewUpdateAndDeleteTestPreparation)
            includes(viewUpdateAndDeleteHealthCondition)
        }
        match("Navigator Chief or Chief Digital Care Nurse",
            { subject is StaffModel && (subject.isChiefNavigator() || subject.isChiefDigitalCareNurse()) }) {
            includes(viewAndUpdatePerson)
            includes(viewPersonInternalReference)
            includes(viewMember)
            includes(viewAndUpdateTestCodes)
            includes(viewAndUpdateTestCodePackage)
            includes(viewUpdateAndDeleteTestPreparation)
            includes(viewAndUpdateHealthcareTeam)
            includes(viewUpdateAndDeleteFeatureConfig)
            includes(viewAndUpdateProtocols)
            includes(viewAndUpdateProviders)
            includes(viewAndUpdateProviderUnits)
            includes(viewAndUpdateLocality)
            includes(viewAndUpdateProviderUnitGroups)
            includes(viewAndUpdateHealthCommunitySpecialists)
            includes(viewAndUpdateHealthProfessional)
            includes(viewUpdateAndCreateHealthProfessionalOpsProfile)
            includes(viewAndCreateUpdateProcedures)
            includes(viewAndUpdateChatAdmRoutingRules)
            includes(viewAndCreateExternalHealthProfessionals)
            includes(viewAndCreateHealthcareBundles)
            includes(viewUpdateAndCreateBillingAccountableParty)
        }
        match(
            "Insurance Ops - Health Institution Ops",
            { subject is StaffModel && subject.isInsuranceOpsHealthInstitutionOps() }) {
            includes(viewMember)
            includes(viewHealthcareTeam)
            includes(viewAndUpdateTestCodes)
            includes(viewAndUpdateTestCodePackage)
            includes(viewUpdateAndDeleteProviderTestCodes)
            includes(viewUpdateAndDeleteTestPreparation)
            includes(viewAndUpdateProviders)
            includes(viewAndUpdateProviderUnits)
            includes(viewAndUpdateLocality)
            includes(viewAndUpdateProviderUnitGroups)
            includes(viewAndUpdateHealthCommunitySpecialists)
            includes(viewAndUpdateAppointmentScheduleOption)
            includes(viewAndUpdateProductAndComponents)
            includes(viewAndCreateCboCodes)
            includes(viewEta)
            includes(viewHealthLogic)
            includes(viewAndUpdateMedicalSpecialties)
            match(
                "can view, count, update, create",
                { action is View || action is Count || action is Update || action is Create }) {
                match("any StaffModel") {
                    resourceIs(StaffModel::class) &&
                            ((resource as StaffModel).type == StaffType.COMMUNITY_SPECIALIST || resource.type == StaffType.HEALTH_ADMINISTRATIVE)
                }
            }
        }

        match(
            "Insurance Ops - Community Success",
            { subject is StaffModel && (subject.isInsuranceOpsCommunitySuccess()) }) {
            includes(viewAndUpdateHealthForm)
            includes(viewUpdateAndCreateHealthPlanTaskTemplate)
            includes(viewUpdateAndCreateHealthPlanTaskGroupTemplate)
        }
        match(
            "Insurance Ops - Health Institution Ops",
            { subject is StaffModel && subject.isInsuranceOpsHealthInstitutionOps() }) {
            includes(viewUpdateAndCreateHealthProfessional)
            includes(viewUpdateAndCreateHealthProfessionalOpsProfile)
            includes(viewAndCreateUpdateProcedures)
            includes(viewAndCreateExternalHealthProfessionals)
            includes(viewAndCreateHealthcareBundles)
            includes(viewAndUpdateProductAndComponents)
        }
        match("Health - Técnica(o) de Enfermagem Casa Alice", { subject is StaffModel && subject.isTechniqueNurse() }) {
            includes(viewMember)
            includes(viewPersonInternalReference)
            includes(viewAndUpdateTestCodes)
            includes(viewAndUpdateProviderTestCode)
            includes(viewAndUpdateTestCodePackage)
            includes(viewUpdateAndDeleteTestPreparation)
        }

        match("Health Ops - Customer Experience", { subject is StaffModel && subject.isCXOps() }) {
            includes(viewHealthCommunitySpecialists)
            includes(viewPersonInternalReference)
            includes(viewMember)
            includes(viewTestCodes)
            match("can view", { action is View }) {
                match("any HealthProfessionalModel") { resourceIs(HealthProfessionalModel::class) }
            }
        }

        match("EITA user", { subject is EmailDomain }) {
            includes(viewAndCountHealthcareResources)
        }
    }

}

private fun checkStaffEditor(subject: Subject) =
    subject is StaffModel &&
            (!ServiceConfig.isProduction || backofficeStaffEditorsEmails.contains(subject.email))

private val backofficeStaffEditorsEmails: List<String> =
    listOf(
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    )
