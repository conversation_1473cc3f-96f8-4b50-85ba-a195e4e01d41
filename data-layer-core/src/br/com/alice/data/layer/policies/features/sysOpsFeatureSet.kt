package br.com.alice.data.layer.policies.features

import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Delete
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.*
import br.com.alice.data.layer.models.ServiceScriptNodeType.ACTION
import br.com.alice.data.layer.models.ServiceScriptNodeType.HEALTH_LOGIC
import br.com.alice.data.layer.models.ServiceScriptNodeType.PROGRAM
import br.com.alice.data.layer.models.ServiceScriptNodeType.TRIGGER
import br.com.alice.data.layer.policies.herself
import br.com.alice.data.layer.policies.staffAttemptingToLoginWith

val productAndTechPolicySet = policySet {
    includes(viewAndUpdateDeadletterQueue)
    includes(viewAndUpdatePerson)
    includes(viewUpdateAndDeleteFeatureConfig)
    includes(viewAndUpdateExecIndicatorAuthorizer)
    includes(viewAndUpdatePrescriptions)
    includes(viewAndUpdateHealthForm)
    includes(viewHealthFormAnswerGroup)
    includes(viewAndUpdateAppContentScreenDetail)
    includes(viewAndUpdateHealthcareTeam)
    includes(viewPersonInternalReference)
    includes(viewUpdateAndDeleteHealthCondition)
    includes(viewAndUpdateTestCodes)
    includes(viewAndUpdateTestCodePackage)
    includes(viewUpdateAndDeleteTestPreparation)
    includes(viewAndUpdateProviders)
    includes(viewAndUpdateProviderUnits)
    includes(viewAndUpdateProviderUnitGroups)
    includes(viewUpdateAndDeleteProviderTestCodes)
    includes(viewAndUpdateLocality)
    includes(viewAndUpdateMedicalSpecialties)
    includes(viewAndUpdateHealthCommunitySpecialists)
    includes(viewUpdateAndCreateBillingAccountableParty)
    includes(viewAndUpdateAppointmentScheduleOption)
    includes(viewUpdateAndCreateHealthMeasurementTypeAndCategory)
    includes(viewAndUpdateProductAndComponents)
    includes(viewUpdateAndCreateFhirProviderAccess)
    includes(createAndUpdateHealthFormOutcomeCalculatorConf)
    includes(configureConsolidatedCalculatorConf)
    includes(viewCreateUpdateAndDeleteInsurancePortabilityHealthInsurance)
    includes(viewCreateUpdateAndDeleteInsurancePortabilityRequest)
    includes(viewCreateUpdateAndDeleteInsurancePortabilityRequestFile)
    includes(viewAndUpdateHealthProfessional)
    includes(viewUpdateAndCreateHealthProfessionalOpsProfile)
    includes(viewMember)
    includes(viewChannelsConf)
    includes(viewCreateUpdateAndDeleteLegalGuardianAssociation)
    includes(viewAndUpdateProductRecommendation)
}

val doLogin = policySet {
    match("can view", { action is View }) {
        match("StaffModel attempting to login with") { staffAttemptingToLoginWith() }
    }
}
val viewStaff = policySet {
    match("can view and count", { action is View || action is Count }) {
        match("any StaffModel") { resourceIs(StaffModel::class) }
    }
}
val viewHealthProfessional = policySet {
    match("can view and count", { action is View || action is Count }) {
        match("any HealthProfessionalModel") { resourceIs(HealthProfessionalModel::class) }
        match("any StructuredAddress") { resourceIs(StructuredAddress::class) }
        match("any ContactModel") { resourceIs(ContactModel::class) }
    }
}
val viewHealthProfessionalOpsProfile = policySet {
    match("can view and count", { action is View || action is Count }) {
        match("any HealthProfessionalOpsProfile") { resourceIs(HealthProfessionalOpsProfileModel::class) }
    }
}
val viewUpdateAndCreateHealthProfessional = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any HealthProfessionalModel") { resourceIs(HealthProfessionalModel::class) }
        match("any StructuredAddress") { resourceIs(StructuredAddress::class) }
        match("any ContactModel") { resource is ContactModel }
    }
    match(
        "can view, count and update and create and delete",
        { action is View || action is Count || action is Update || action is Create || action is Delete }) {
        match("any ContactModel") { resource is ContactModel }
    }
}
val viewAndUpdateHealthProfessional = policySet {
    match(
        "can view, count and update",
        { action is View || action is Count || action is Update }) {
        match("any HealthProfessionalModel") { resourceIs(HealthProfessionalModel::class) }
    }
}
val viewUpdateAndCreateHealthProfessionalOpsProfile = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any HealthProfessionalOpsProfile") { resourceIs(HealthProfessionalOpsProfileModel::class) }
    }
}
val viewAndUpdateHerOwnHealthProfessional = policySet {
    match(
        "can view, count and update",
        { action is View || action is Count || action is Update }) {
        match("her self") { herself() }
    }
}
val viewPerson = policySet {
    match("can view and count", { action is View || action is Count }) {
        match("any PersonModel") { resourceIs(PersonModel::class) }
    }
}
val viewAndUpdateDeadletterQueue = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any PersonModel") { resourceIs(DeadletterQueue::class) }
    }
}
val viewPersonClinicalAccount = policySet {
    match("can view and count", { action is View || action is Count }) {
        match("any PersonClinicalAccount") { resourceIs(PersonClinicalAccount::class) }
    }
}
val viewAndUpdateCompany = policySet {
    can(View, Count, Update, Create) {
        resources(CompanyModel::class, CompanyContractModel::class, CompanySubContractModel::class, GenericFileVault::class)
    }
}

val viewRefund = policySet {
    can(View, Count) {
        resources(RefundCostInfoModel::class, CompanyRefundCostInfoModel::class)
    }
}

val viewBeneficiaryHubspot = policySet {
    can(View, Count) { resources(BeneficiaryHubspotModel::class) }
}

val viewAndUpdateBeneficiary = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any Beneficiary") { resourceIs(BeneficiaryModel::class) }
        match("any CassiMember") { resourceIs(CassiMemberModel::class) }
        match("any BeneficiaryOnboarding") { resourceIs(BeneficiaryOnboardingModel::class) }
        match("any BeneficiaryOnboardingPhase") { resourceIs(BeneficiaryOnboardingPhaseModel::class) }
    }

    match("can create", { action is Create }) {
        match("any HealthDeclaration") { resourceIs(HealthDeclaration::class) }
        match("any PersonRegistrationModel") { resourceIs(PersonRegistrationModel::class) }
        match("any PersonOnboardingModel") { resourceIs(PersonOnboardingModel::class) }
    }

    match("can view and count", { action is View || action is Count }) {
        match("any PersonRegistrationModel") { resourceIs(PersonRegistrationModel::class) }
        match("any PersonOnboardingModel") { resourceIs(PersonOnboardingModel::class) }
    }
}
val viewCompany = policySet {
    can(View, Count) {
        resources(CompanyModel::class, CompanyContractModel::class, CompanySubContractModel::class, GenericFileVault::class)
    }
}
val viewCompanyStaff = policySet {
    match("can view and count", { action is View || action is Count }) {
        match("any CompanyStaff") { resourceIs(CompanyStaffModel::class) }
        match("any Company") { resourceIs(CompanyModel::class) }
        match("any StaffModel") { resourceIs(StaffModel::class) }
    }
}
val viewAndUpdateCompanyStaff = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any CompanyStaff") { resourceIs(CompanyStaffModel::class) }
        match("any Company") { resourceIs(CompanyModel::class) }
    }
    allows(StaffModel::class, Count, View)
}
val viewBeneficiary = policySet {
    match("can view and count", { action is View || action is Count }) {
        match("any Beneficiary") { resourceIs(BeneficiaryModel::class) }
        match("any BeneficiaryOnboarding") { resourceIs(BeneficiaryOnboardingModel::class) }
        match("any BeneficiaryOnboardingPhase") { resourceIs(BeneficiaryOnboardingPhaseModel::class) }
        match("any CassiMember") { resourceIs(CassiMemberModel::class) }
        match("any Company") { resourceIs(CompanyModel::class) }
    }
}

val viewAndUpdateCompanyContract = policySet {
    allows(CompanyModel::class, View, Count, Create)
    allows(CompanyContractModel::class, View, Count, Create)
    allows(GenericFileVault::class, View, Count, Create)
}

val viewCompanyProductConfiguration = policySet {
    allows(CompanyProductConfigurationModel::class, View, Count)
}

val viewAndUpsertCompanyProductConfiguration = policySet {
    allows(CompanyProductConfigurationModel::class, View, Count, Update, Create)
}

val viewAndCreateMemberContract = policySet {
    allows(MemberContractModel::class, View, Count, Create)
    allows(MemberContractTermModel::class, View, Count, Create)
}

val viewAndUpdateEta = policySet {
    match("count", { action is Count }) {
        match("any ServiceScriptNode") { resourceIs(ServiceScriptNode::class) }
        match("any ServiceScriptRelationship") { resourceIs(ServiceScriptRelationship::class) }
    }
    match("can view and count", { action is View || action is Count }) {
        match("any OutcomeConf") { resourceIs(OutcomeConf::class) }
    }
    match("can view, update and create", { action is View || action is Update || action is Create }) {
        match("ServiceScriptNode of type TRIGGER and ACTION") {
            resource is ServiceScriptNode && listOf(TRIGGER, ACTION).contains(resource.type)
        }
        match("any ServiceScriptRelationship") { resourceIs(ServiceScriptRelationship::class) }
    }
}
val viewEta = policySet {
    match("count", { action is Count }) {
        match("any ServiceScriptNode") { resourceIs(ServiceScriptNode::class) }
        match("any ServiceScriptRelationship") { resourceIs(ServiceScriptRelationship::class) }
    }
    match("can view", { action is View }) {
        match("ServiceScriptNode of type TRIGGER and ACTION") {
            resource is ServiceScriptNode && listOf(TRIGGER, ACTION).contains(resource.type)
        }
        match("any ServiceScriptRelationship") { resourceIs(ServiceScriptRelationship::class) }
    }
}
val viewAndUpdateHealthLogic = policySet {
    match("count", { action is Count }) {
        match("any ServiceScriptNode") { resourceIs(ServiceScriptNode::class) }
        match("any ServiceScriptRelationship") { resourceIs(ServiceScriptRelationship::class) }
    }
    match("can view, update and create", { action is View || action is Update || action is Create }) {
        match("ServiceScriptNode of type PROGRAM and HEALTH_LOGIC") {
            resource is ServiceScriptNode && listOf(PROGRAM, HEALTH_LOGIC).contains(resource.type)
        }
        match("any ServiceScriptRelationship") { resourceIs(ServiceScriptRelationship::class) }
        match("any ServiceScriptAction") { resourceIs(ServiceScriptAction::class) }
    }
}
val viewHealthLogic = policySet {
    match("can view", { action is View || action is Count }) {
        match("ServiceScriptNode of type PROGRAM and HEALTH_LOGIC") {
            resource is ServiceScriptNode && listOf(PROGRAM, HEALTH_LOGIC).contains(resource.type)
        }
        match("any ServiceScriptRelationship") { resourceIs(ServiceScriptRelationship::class) }
        match("any ServiceScriptAction") { resourceIs(ServiceScriptAction::class) }
    }
}
val viewProtocols = policySet {
    allows(BudNode::class, View, Count)
    allows(Protocol::class, View, Count)
    allows(ServiceScriptRelationship::class, View, Count)
    allows(ServiceScriptExecution::class, View)
    allows(ServiceScriptNavigation::class, View)
}
val viewAndUpdateProtocols = policySet {
    allows(BudNode::class, View, Update, Create, Count)
    allows(Protocol::class, View, Update, Create, Count)
    allows(ServiceScriptRelationship::class, View, Update, Create, Count)

    allows(ServiceScriptExecution::class, View, Update, Create)
    allows(ServiceScriptNavigation::class, View, Update, Create)
    allows(ServiceScriptNode::class, View)
}
val viewAndUpdatePrescriptions = policySet {
    match(
        "can view, count, update and delete and create",
        { action is View || action is Count || action is Update || action is Delete || action is Create }) {
        match("any PrescriptionSentenceModel") { resourceIs(PrescriptionSentenceModel::class) }
    }
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any MedicineModel") { resourceIs(MedicineModel::class) }
    }
}
val viewAndUpdateChannelsConf = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Delete || action is Create }) {
        match("any AliceAgoraWorkingHours") { resourceIs(AliceAgoraWorkingHours::class) }
        match("any ChannelTag") { resourceIs(ChannelTag::class) }
        match("any ChannelMacro") { resourceIs(ChannelMacro::class) }
    }
}
val viewAndUpdateChannelTags = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Delete || action is Create }) {
        match("any ChannelTag") { resourceIs(ChannelTag::class) }
    }
}
val viewAndUpdateChannelMacros = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Delete || action is Create }) {
        match("any ChannelMacro") { resourceIs(ChannelMacro::class) }
    }
}
val viewChannelsConf = policySet {
    match("can view", { action is View || action is Count }) {
        match("any AliceAgoraWorkingHours") { resourceIs(AliceAgoraWorkingHours::class) }
        match("any ChannelTag") { resourceIs(ChannelTag::class) }
        match("any ChannelMacro") { resourceIs(ChannelMacro::class) }
    }
}
val viewPersonHealthcareTeam = policySet {
    match("can view and count", { action is View || action is Count }) {
        match("any HealthcareTeamModel") { resourceIs(HealthcareTeamModel::class) }
        match("any HealthcareAdditionalTeam") { resourceIs(HealthcareAdditionalTeam::class) }
        match("any PersonModel") { resourceIs(PersonModel::class) }
        match("any PersonClinicalAccount") { resourceIs(PersonClinicalAccount::class) }
        match("any StaffModel") { resourceIs(StaffModel::class) }
    }
}
val viewAndUpdatePersonHealthcareTeam = policySet {
    match("can view and count", { action is View || action is Count }) {
        match("any HealthcareTeamModel") { resourceIs(HealthcareTeamModel::class) }
        match("any HealthcareAdditionalTeam") { resourceIs(HealthcareAdditionalTeam::class) }
        match("any PersonModel") { resourceIs(PersonModel::class) }
        match("any PersonClinicalAccount") { resourceIs(PersonClinicalAccount::class) }
        match("any StaffModel") { resourceIs(StaffModel::class) }
        match("any PersonInternalReference") { resourceIs(PersonInternalReference::class) }
    }
    match("can update and create", { action is Update || action is Create }) {
        match("any PersonClinicalAccount") { resource is PersonClinicalAccount }
    }
}
val viewPersonInternalReference = policySet {
    match("can view", { action is View }) {
        match("any PersonInternalReference") { resourceIs(PersonInternalReference::class) }
    }
}
val viewUpdateGasB2cAllowedMembers = policySet {
    match(
        "can view, update and create",
        { action is View || action is Update || action is Create }) {
        match("GasB2cAllowedMembers FeatureConfigModel") {
            resource is FeatureConfigModel && resource.namespace == FeatureNamespace.ALICE_APP &&
                    resource.key == "gas_b2c_allowed_members"
        }
    }
}
val viewUpdateAndDeleteFeatureConfig = policySet {
    match(
        "can view, count, update and delete and create",
        { action is View || action is Count || action is Update || action is Delete || action is Create }) {
        match("any FeatureConfigModel") { resourceIs(FeatureConfigModel::class) }
    }
}
val viewFeatureConfig = policySet {
    match("can view", { action is View || action is Count }) {
        match("any FeatureConfigModel") { resourceIs(FeatureConfigModel::class) }
    }
}
val viewHealthcareTeam = policySet {
    match("can view and count", { action is View || action is Count }) {
        match("any HealthcareTeamModel") { resourceIs(HealthcareTeamModel::class) }
    }
}
val viewAndUpdateHealthcareTeam = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any HealthcareTeamModel") { resourceIs(HealthcareTeamModel::class) }
        match("any HealthcareAdditionalTeam") { resourceIs(HealthcareAdditionalTeam::class) }
    }
}

val viewTestCodes = policySet {
    match("can view and count", { action is View || action is Count }) {
        match("any TestCode") { resourceIs(TestCodeModel::class) }
        match("any ProviderTestCode") { resourceIs(ProviderTestCodeModel::class) }
    }
}
val viewTestCodePackages = policySet {
    match("can view", { action is View }) {
        match("any TestCodePackage") { resourceIs(TestCodePackageModel::class) }
    }
}
val viewAndUpdateTestCodes = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any TestCodes") { resourceIs(TestCodeModel::class) }
    }
}
val viewAndUpdateProviderTestCode = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any ProviderTestCode") { resource is ProviderTestCodeModel }
    }
}
val viewAndUpdateExecIndicatorAuthorizer = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any ExecIndicatorAuthorizer") { resourceIs(ExecIndicatorAuthorizerModel::class) }
    }
}
val viewAndUpdateFaqGroups = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any FaqGroupModel") { resourceIs(FaqGroupModel::class) }
    }
}
val viewAndUpdateFaqContents = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any FaqContentModel") { resourceIs(FaqContentModel::class) }
    }
}
val viewProviders = policySet {
    match("can view and count", { action is View || action is Count }) {
        match("any Provider") { resourceIs(ProviderModel::class) }
    }
    match("can view and count", { action is View || action is Count }) {
        match("any Products") { resourceIs(ProductModel::class) }
    }
}
val viewAndUpdateProviders = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any Provider") { resourceIs(ProviderModel::class) }
    }
    match("can view and count", { action is View || action is Count }) {
        match("any Products") { resourceIs(ProductModel::class) }
    }
}
val viewProviderUnits = policySet {
    match("can view and count", { action is View || action is Count }) {
        match("any ProviderUnit") { resourceIs(ProviderUnitModel::class) }
        match("any StructuredAddress") { resourceIs(StructuredAddress::class) }
    }
    match("can view and count", { action is View || action is Count }) {
        match("any Products") { resourceIs(ProductModel::class) }
    }
    match("can view and count", { action is View || action is Count }) {
        match("any StructuredAddress") { resourceIs(StructuredAddress::class) }
    }
    match("can view and count", { action is View || action is Count }) {
        match("any PartnerIntegrationProviderUnit") { resourceIs(PartnerIntegrationProviderUnitModel::class) }
    }
}
val viewLocalities = policySet {
    match("can view and count", { action is View || action is Count }) {
        match("any Cities") { resourceIs(City::class) }
    }
    match("can view and count", { action is View || action is Count }) {
        match("any FederativeUnits") { resourceIs(FederativeUnit::class) }
    }
}
val viewAndUpdateProviderUnits = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any ProviderUnit") { resourceIs(ProviderUnitModel::class) }
    }
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any StructuredAddress") { resourceIs(StructuredAddress::class) }
    }
    match("can view and count", { action is View || action is Count }) {
        match("any Products") { resourceIs(ProductModel::class) }
    }
    match(
        "can view, count, create, update and delete",
        { action is View || action is Count || action is Update || action is Create || action is Delete }) {
        match("any PartnerIntegrationProviderUnit") { resourceIs(PartnerIntegrationProviderUnitModel::class) }
    }
}
val viewAndUpdateLocality = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Create || action is Update }) {
        match("any City") { resourceIs(City::class) }
    }
    match("can view and count", { action is View || action is Count || action is Create || action is Update }) {
        match("any FederativeUnit") { resourceIs(FederativeUnit::class) }
        match("any ZipcodeAddress") { resourceIs(ZipcodeAddress::class) }
    }
}
val viewProviderUnitGroups = policySet {
    match("can view and count", { action is View || action is Count }) {
        match("any ProviderUnitGroup") { resourceIs(ProviderUnitGroupModel::class) }
    }
}
val viewAndUpdateProviderUnitGroups = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any ProviderUnitGroup") { resourceIs(ProviderUnitGroupModel::class) }
    }
}
val viewProviderTestCodes = policySet {
    match("can view and count", { action is View || action is Count }) {
        match("any ProviderTestCode") { resourceIs(ProviderTestCodeModel::class) }
        match("any ProviderTestCodeDataIntegration") { resourceIs(ProviderTestCodeDataIntegrationModel::class) }
    }
}
val viewUpdateAndDeleteProviderTestCodes = policySet {
    match(
        "can view, count, update and delete and create",
        { action is View || action is Count || action is Update || action is Delete || action is Create }) {
        match("any ProviderTestCode") { resourceIs(ProviderTestCodeModel::class) }
        match("any ProviderTestCodeDataIntegration") { resourceIs(ProviderTestCodeDataIntegrationModel::class) }
    }
}
val viewAndUpdateProviderUnitTestCodes = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create || action is Delete }) {
        match("any ProviderUnitTestCode") { resourceIs(ProviderUnitTestCodeModel::class) }
    }
}
val viewMedicalSpecialties = policySet {
    match("can view, count and update and create", { action is View || action is Count || action is Create }) {
        match("any MedicalSpecialties") { resourceIs(MedicalSpecialtyModel::class) }
    }
}
val viewCboCodes = policySet {
    match(
        "can view, count",
        { action is View || action is Count }) {
        match("any Cbo Codes") { resourceIs(CboCodeModel::class) }
    }
}
val viewAndUpdateMedicalSpecialties = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any MedicalSpecialties") { resourceIs(MedicalSpecialtyModel::class) }
    }
}
val viewAndCreateCboCodes = policySet {
    match(
        "can view, count and create",
        { action is View || action is Count || action is Create }) {
        match("any Cbo Codes") { resourceIs(CboCodeModel::class) }
    }
}
val viewHealthCommunitySpecialists = policySet {
    match("can view and count", { action is View || action is Count }) {
        match("any HealthCommunitySpecialistModel") { resourceIs(HealthCommunitySpecialistModel::class) }
        match("any HealthProfessionalModel") { resourceIs(HealthProfessionalModel::class) }
        match("any StructuredAddress") { resourceIs(StructuredAddress::class) }
        match("any ContactModel") { resourceIs(ContactModel::class) }
    }
}
val viewAndUpdateHealthCommunitySpecialists = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any HealthCommunitySpecialistModel") { resourceIs(HealthCommunitySpecialistModel::class) }
        match("any HealthProfessionalModel") { resourceIs(HealthProfessionalModel::class) }
        match("any StructuredAddress") { resource is StructuredAddress }
    }
    match(
        "can view, count and update and create and delete",
        { action is View || action is Count || action is Update || action is Create || action is Delete }) {
        match("any ContactModel") { resource is ContactModel }
    }

}
val viewAndUpdateTestCodePackage = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any TestCodePackage") { resourceIs(TestCodePackageModel::class) }
    }
}
val viewAndUpdateAppointmentScheduleOption = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any AppointmentScheduleOptionModel") { resourceIs(AppointmentScheduleOptionModel::class) }
    }
}
val viewUpdateAndDeleteTestPreparation = policySet {
    match(
        "can view, count, update and delete and create",
        { action is View || action is Count || action is Update || action is Delete || action is Create }) {
        match("any TestPreparationModel") { resourceIs(TestPreparationModel::class) }
    }
}

val viewUpdateHealthGoal = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any HealthGoalModel") { resourceIs(HealthGoalModel::class) }
    }
}

val viewAndUpdateHaocClaimProcess = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any Haoc Claim Process") { resourceIs(HaocClaimProcess::class) }
        match("any Haoc Pix Registration") { resourceIs(HaocPixRegistration::class) }
    }
    match("can view", { action is View }) {
        match("any PersonModel Internal Reference") { resource is PersonInternalReference }
    }
}
val viewAndUpdateProductAndComponents = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any Product") { resourceIs(ProductModel::class) }
        match("any Product Bundle") { resourceIs(ProductBundleModel::class) }
        match("any Product Group") { resourceIs(ProductGroupModel::class) }
    }
}

val viewProductComponents = policySet {
    match("can view and count", { action is View || action is Count }) {
        match("any Product") { resourceIs(ProductModel::class) }
        match("any Product Bundle") { resourceIs(ProductBundleModel::class) }
        match("any Price Listing") { resourceIs(PriceListingModel::class) }
        match("any Product Price Listing") { resourceIs(ProductPriceListingModel::class) }
        match("any CompanyProductPriceListing") { resourceIs(CompanyProductPriceListingModel::class) }
        match("any Product Group Listing") { resourceIs(ProductGroupModel::class) }
    }
}

val viewAndUpdateHealthForm = policySet {
    match(
        "can view, update and delete and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any Health Form") { resourceIs(HealthForm::class) }
        match("any Health Section") { resourceIs(HealthFormSection::class) }
        match("any Health Question") { resourceIs(HealthFormQuestion::class) }
    }
}


val viewHealthFormAnswerGroup = policySet {
    match(
        "can view",
        { action is View }) {
        match("any Health Form Answer Group") { resourceIs(HealthFormAnswerGroup::class) }
    }
}

val viewAndUpdateAppContentScreenDetail = policySet {
    match(
        "can view, update and delete and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any AppContentScreenDetail") { resourceIs(AppContentScreenDetail::class) }
    }
}


val viewAndCreateHealthForm = policySet {
    match("can view, update and delete and create", { action is View || action is Count || action is Create }) {
        match("any Health Form") { resourceIs(HealthForm::class) }
        match("any Health Section") { resourceIs(HealthFormSection::class) }
        match("any Health Question") { resourceIs(HealthFormQuestion::class) }
    }
}

val viewAndCreateHealthcareBundles = policySet {
    match(
        "can view, update and delete and create",
        { action is View || action is Count || action is Create || action is Update }) {
        match("any Healthcare Resource") { resourceIs(HealthcareResourceModel::class) }
        match("any Healthcare Resource Group") { resourceIs(HealthcareResourceGroupModel::class) }
        match("any Healthcare Bundle") { resourceIs(HealthcareBundleModel::class) }
        match("any Healthcare Resource Group Association") { resourceIs(HealthcareResourceGroupAssociationModel::class) }
    }
    match("can view", { action is View }) {
        match("any StaffModel") { resourceIs(StaffModel::class) }
        match("any Provider Unit") { resourceIs(ProviderUnitModel::class) }
    }
}

val viewAndCountHealthcareResources = policySet {
    match(
        "can view and count",
        { action is View || action is Count }) {
        match("any Healthcare Resource") { resourceIs(HealthcareResourceModel::class) }
    }
}


val viewAndCreateExternalHealthProfessionals = policySet {
    match("can view, update and delete and create", { action is View || action is Count || action is Create }) {
        match("any Hippocrates Healthcare Professional") { resourceIs(HippocratesHealthcareProfessionalModel::class) }
    }
}

val viewHealthFormAndSubModels = policySet {
    match("can view", { action is View || action is Count }) {
        match("any Health Form") { resourceIs(HealthForm::class) }
        match("any Health Section") { resourceIs(HealthFormSection::class) }
        match("any Health Question") { resourceIs(HealthFormQuestion::class) }
    }
}

val viewHealthCondition = policySet {
    match("can view", { action is View || action is Count }) {
        match("any HealthCondition") { resourceIs(HealthCondition::class) }
        match("any HealthConditionTemplate") { resourceIs(HealthConditionTemplate::class) }
        match("any HealthConditionAxis") { resourceIs(HealthConditionAxis::class) }
        match("any HealthConditionRelated") { resourceIs(HealthConditionRelated::class) }
    }
}
val viewUpdateAndDeleteHealthCondition = policySet {
    match(
        "can view, update and delete and create",
        { action is View || action is Count || action is Update || action is Delete || action is Create }) {
        match("any HealthCondition") { resourceIs(HealthCondition::class) }
        match("any HealthConditionTemplate") { resourceIs(HealthConditionTemplate::class) }
        match("any HealthConditionAxis") { resourceIs(HealthConditionAxis::class) }
        match("any HealthConditionRelated") { resourceIs(HealthConditionRelated::class) }
    }
}

val viewCountBillingAccountableParty = policySet {
    match(
        "can view and count", { action is View || action is Count }) {
        match("any BillingAccountableParty") { resourceIs(BillingAccountablePartyModel::class) }
    }
}

val viewUpdateAndCreateBillingAccountableParty = policySet {
    match(
        "can view, update and create", { action is View || action is Count || action is Update || action is Create }) {
        match("any BillingAccountableParty") { resourceIs(BillingAccountablePartyModel::class) }
    }
}

val viewUpdateAndCreatePersonBillingAccountableParty = policySet {
    match(
        "can view, update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any BillingAccountableParty") { resourceIs(BillingAccountablePartyModel::class) }
        match("any PersonBillingAccountableParty") { resourceIs(PersonBillingAccountablePartyModel::class) }
    }
}

val viewCountPersonBillingAccountableParty = policySet {
    match("can view and count", { action is View || action is Count }) {
        match("any PersonBillingAccountableParty") { resourceIs(PersonBillingAccountablePartyModel::class) }
    }
}

val viewHealthDeclaration = policySet {
    match(
        "can view and count", { action is View || action is Count }) {
        match("any HealthDeclaration") { resourceIs(HealthDeclaration::class) }
    }
}
val viewCountBeneficiaryOnboarding = policySet {
    match("can view and count", { action is View || action is Count }) {
        match("any BeneficiaryOnboarding") { resourceIs(BeneficiaryOnboardingModel::class) }
    }
}

val viewCountBeneficiaryOnboardingPhase = policySet {
    match("can view and count", { action is View || action is Count }) {
        match("any BeneficiaryOnboardingPhase") { resourceIs(BeneficiaryOnboardingPhaseModel::class) }
    }
}

val viewUpdateAndCreateBeneficiaryOnboarding = policySet {
    match(
        "can view, update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any BeneficiaryOnboarding") { resourceIs(BeneficiaryOnboardingModel::class) }
    }
}

val viewUpdateAndCreateBeneficiaryOnboardingPhase = policySet {
    match(
        "can view, update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any BeneficiaryOnboardingPhase") { resourceIs(BeneficiaryOnboardingPhaseModel::class) }
    }
}

val viewUpdateAndCreateHealthPlanTaskTemplate = policySet {
    match(
        "can view, count, update and delete and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any HealthPlanTaskTemplate") { resourceIs(HealthPlanTaskTemplate::class) }
    }
}

val viewUpdateAndCreateHealthPlanTaskGroupTemplate = policySet {
    match(
        "can view, count, update and delete and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any HealthPlanTaskGroupTemplate") { resourceIs(HealthPlanTaskGroupTemplate::class) }
    }
}

val viewUpdateAndCreateHealthPlanTask = policySet {
    match(
        "can view, count, update and delete and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any HealthPlanTask") { resourceIs(HealthPlanTask::class) }
    }
}

val viewUpdateAndCreateHealthPlanTaskGroup = policySet {
    match(
        "can view, count, update and delete and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any HealthPlanTaskGroup") { resourceIs(HealthPlanTaskGroup::class) }
    }
}

val viewUpdateAndCreateHealthMeasurementTypeAndCategory = policySet {
    match(
        "can view, count, update and delete and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any HealthMeasurementTypeModel") { resourceIs(HealthMeasurementTypeModel::class) }
        match("any HealthMeasurementCategory") { resourceIs(HealthMeasurementCategoryModel::class) }
    }
}

val viewUpdateAndCreateFhirProviderAccess = policySet {
    match(
        "can view and update and create and count",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any Fhir Provider Access") { resourceIs(FhirProviderAccess::class) }
    }
}

val createAndUpdateHealthFormOutcomeCalculatorConf = policySet {
    match(
        "can create and update and view and count",
        { action is Create || action is Update || action is View || action is Count }) {
        match("any HealthFormOutcomeCalculatorConf") { resourceIs(HealthFormOutcomeCalculatorConf::class) }
    }
}

val viewHealthFormOutcomeCalculatorConf = policySet {
    match("can view", { action is View }) {
        match("any HealthFormOutcomeCalculatorConf") { resourceIs(HealthFormOutcomeCalculatorConf::class) }
    }
}

val configureConsolidatedCalculatorConf = policySet {
    match("can create, update and view", { action is Create || action is Update || action is View }) {
        match("any ClinicalOutcomesConsolidatedCalculatorConf") { resourceIs(ClinicalOutcomesConsolidatedCalculatorConf::class) }
    }
}

val viewCreateUpdateAndDeleteInsurancePortabilityHealthInsurance = policySet {
    match(
        "can view, create, update and delete",
        { action is Create || action is Update || action is View || action is Count || action is Delete }) {
        match("any InsurancePortabilityHealthInsurance") { resourceIs(InsurancePortabilityHealthInsuranceModel::class) }
    }
}

val viewCreateUpdateAndDeleteInsurancePortabilityRequest = policySet {
    match(
        "can view, create, update and delete",
        { action is Create || action is Update || action is View || action is Count || action is Delete }) {
        match("any InsurancePortabilityRequest") { resourceIs(InsurancePortabilityRequestModel::class) }
    }
}

val viewCreateUpdateAndDeleteInsurancePortabilityRequestFile = policySet {
    match(
        "can view, create, update and delete",
        { action is Create || action is Update || action is View || action is Count || action is Delete }) {
        match("any InsurancePortabilityRequestFile") { resourceIs(InsurancePortabilityRequestFileModel::class) }
    }
}

val viewCreateUpdateRiskGroup = policySet {
    match(
        "can view, create, update and count",
        { action is Create || action is Update || action is View || action is Count }) {
        match("any RiskGroup") { resourceIs(RiskGroup::class) }
    }
}

val viewCreateUpdateCountRiskCalculationConf = policySet {
    match(
        "can view, create, update and count",
        { action is Create || action is Update || action is View || action is Count }) {
        match("any RiskCalculationConf") { resourceIs(RiskCalculationConf::class) }
    }
}

val viewCreateUpdateAndDeleteLegalGuardianAssociation = policySet {
    match(
        "can view, create, update and delete",
        { action is Create || action is Update || action is View || action is Count || action is Delete }) {
        match("any LegalGuardianAssociation") { resourceIs(LegalGuardianAssociationModel::class) }
        match("any LegalGuardianInfoTemp") { resourceIs(LegalGuardianInfoTempModel::class) }
    }
}

val viewAndUpdateMember = policySet {
    match(
        "can view, create, update and count",
        { action is Create || action is Update || action is View || action is Count }) {
        match("any MemberModel") { resourceIs(MemberModel::class) }
    }
}

val viewMemberProductChangeSchedule = policySet {
    match(
        "can view and count",
        { action is View || action is Count }) {
        match("any MemberProductChangeScheduleModel") { resourceIs(MemberProductChangeScheduleModel::class) }
    }
}

val viewAddress = policySet {
    match(
        "can view and count",
        { action is View || action is Count }) {
        match("any Address") { resourceIs(AddressModel::class) }
    }
}

val canReactivateMember = policySet {
    can(View, Create, Update, Count) {
        resources(
            MemberModel::class,
            ProductPriceListingModel::class,
            MemberProductPriceModel::class,
            PriceListingModel::class,
            CassiMemberModel::class,
            BeneficiaryModel::class,
            BeneficiaryOnboardingModel::class,
            BeneficiaryOnboardingPhaseModel::class,
        )
    }
}

val viewAndUpdateOutcomeConf = policySet {
    match(
        "can view, count, update and create",
        { action is View || action is Count || action is Update || action is Create }
    ) {
        match("any OutcomeConf") { resourceIs(OutcomeConf::class) }
    }
}

val viewAndUpdateAndCreateOnboardingTemplate = policySet {
    match(
        "can view, count, update and create",
        { action is View || action is Count || action is Update || action is Create }
    ) {
        match("any MemberOnboardingTemplate") { resourceIs(MemberOnboardingTemplate::class) }
        match("any MemberOnboardingStep") { resourceIs(MemberOnboardingStep::class) }
        match("any MemberOnboardingAction") { resourceIs(MemberOnboardingAction::class) }
        match("any MemberOnboardingCheckpoint") { resourceIs(MemberOnboardingCheckpoint::class) }
    }
}

val viewAndUpdateHealthDemandMonitoring = policySet {
    match(
        "can view, count, update and create",
        { action is View || action is Count || action is Update || action is Create }
    ) {
        match("any HealthDemandMonitoring") { resourceIs(HealthDemandMonitoring::class) }
    }
}

val viewCreateUpdateAndDeleteAppointmentTemplate = policySet {
    match(
        "can view, count, update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any AppointmentTemplate") { resourceIs(AppointmentTemplate::class) }
    }
}

val viewUpdateAndCreateAppointmentTemplate = policySet {
    match(
        "can view, count, update and delete and create",
        { action is View || action is Count || action is Update || action is Create || action is Delete }) {
        match("any AppointmentTemplate") { resourceIs(AppointmentTemplate::class) }
    }
}

val viewDevice = policySet {
    match("can view and count", { action is View || action is Count }) {
        match("any DeviceModel") { resourceIs(DeviceModel::class) }
    }
}

val viewAndUpdateProtocol = policySet {
    match(
        "can view, count, update and create",
        { action is View || action is Count || action is Update || action is Create }
    ) {
        match("any Protocol") { resourceIs(Protocol::class) }
    }
}

val viewAndUpdateProductRecommendation = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any ProductRecommendation") { resourceIs(ProductRecommendation::class) }
    }
}

val viewAndUpdateAndCreateUpdateAppRule = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any UpdateAppRuleModel") { resourceIs(UpdateAppRuleModel::class) }
    }
}
val viewAndCreateAndUpdatePromoCode = policySet {
    match(
        "can view, count and update and create",
        { action is View || action is Count || action is Update || action is Create }) {
        match("any PromoCodeModel") { resourceIs(PromoCodeModel::class) }
    }
}
val viewAndCreatedUpdatedDeletedTestCodeAnalyte = policySet {
    match("can view, count, update, create and deleted",
        { action is View || action is Count || action is Update || action is Create || action is Delete }
    ) {
        match("any TestCodeAnalyte") { resourceIs(TestCodeAnalyteModel::class) }
    }
}
val viewAndCreateUpdateProcedures = policySet {
    match("can view, count, update, create",
        { action is View || action is Count || action is Update || action is Create }
    ) {
        match("any TussProcedureSpecialty") { resourceIs(TussProcedureSpecialtyModel::class) }
    }
}

val viewAndUpdateChatAdmRoutingRules = policySet {
    allows(RoutingRule::class, View, Count)
    match("can update", { action is Update }) {
        match("any Chat ADM RoutingRule") {
            resourceIs(RoutingRule::class) && (resource as RoutingRule).name.contains("Chat ADM")
        }
    }
}

val viewAndCreateAndUpdateAnalyteOutcomeMapping = policySet {
    match("can view", { action is View }) {
        match("any OutcomeConf") { resourceIs(OutcomeConf::class) }
        match("any TestCodeAnalyte") { resourceIs(TestCodeAnalyteModel::class) }
    }

    match("can view, create, update and count",
        { action is View || action is Create || action is Update || action is Count }
    ) {
        match("any AnalyteOutcomeMapping") { resourceIs(AnalyteOutcomeMapping::class) }
    }
}

val viewAndCreateAndUpdateTussProcedureSpecialty = policySet {
    match("can view and create and update", { action is View || action is Create || action is Update }) {
        match("any TussProcedureSpecialty") { resource is TussProcedureSpecialtyModel }
    }
}

val viewAndUpdateSpecialistOpinion = policySet {
    match("can view and update", { action is View || action is Update }) {
        match("any SpecialistOpinion") { resource is SpecialistOpinion }
    }
}

val viewSalesFirm = policySet {
    match("can view", { action is View || action is Count }) {
        match("any SalesFirm") { resourceIs(SalesFirm::class) }
        match("any SalesFirmStaff") { resourceIs(SalesFirmStaff::class) }
    }
}

val upsertSalesFirm = policySet {
    match("can view, upsert", { action is View || action is Count || action is Create || action is Update }) {
        match("any SalesFirm") { resourceIs(SalesFirm::class) }
        match("any SalesFirmStaff") { resourceIs(SalesFirmStaff::class) }
        match("any SalesAgent") { resourceIs(SalesAgent::class) }
    }
}

val viewAndCreateAndUpdateCompanyActivationFiles = policySet {
    match("can view and create and update", { action is View || action is Create || action is Update }) {
        match("any CompanyActivationFiles") { resource is CompanyActivationFilesModel }
    }
}

val viewCreateUpdateDeleteSiteAccreditedNetwork = policySet {
    allows(
        SiteAccreditedNetwork::class,
        View, Update, Create, Count, Delete,
    )
    allows(
        SiteAccreditedNetworkProvider::class,
        View, Update, Create, Count,
    )
    allows(
        SiteAccreditedNetworkAddress::class,
        View, Update, Create, Count,
    )
}

val viewAndCreateStandardCost = policySet {
    allows(StandardCostModel::class, View, Create, Count)
}
