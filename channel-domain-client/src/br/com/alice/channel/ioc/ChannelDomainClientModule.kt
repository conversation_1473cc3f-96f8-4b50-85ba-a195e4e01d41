package br.com.alice.channel.ioc

import br.com.alice.channel.ChannelDomainClientConfiguration
import br.com.alice.channel.client.*
import br.com.alice.channel.services.FileUploadService
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.rfc.HttpInvoker
import br.com.alice.common.service.serialization.gsonSnakeCase
import br.com.alice.common.storage.FileVaultStorage
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import org.koin.dsl.module

val ChannelDomainClientModule = module {

    val baseUrl = "${ChannelDomainClientConfiguration.baseUrl()}/rfc"

    val invoker = HttpInvoker(baseUrl = baseUrl)
    val akinatorCallInvoker = HttpInvoker(DefaultHttpClient(timeoutInMillis = 10_000), baseUrl = baseUrl)
    val videoCallInvoker = HttpInvoker(DefaultHttpClient(timeoutInMillis = 15_000), baseUrl = baseUrl)

    val client = DefaultHttpClient({
        install(ContentNegotiation) {
            gsonSnakeCase()
        }
    }, timeoutInMillis = 15_000)

    single { FileVaultStorage(client) }
    single { FileUploadService(get()) }

    single<AvailabilityService> { AvailabilityServiceClient(invoker) }
    single<ChannelDemandService> { ChannelDemandServiceClient(invoker) }
    single<ChannelFupService> { ChannelFupServiceClient(invoker) }
    single<ChannelDeIdentifiedService> { ChannelDeIdentifiedServiceClient(invoker) }
    single<ChannelHistoryService> { ChannelHistoryServiceClient(invoker) }
    single<ChannelClassifyService> { ChannelClassifyServiceClient(akinatorCallInvoker) }
    single<ChannelAdministrativeService> { ChannelAdministrativeServiceClient(invoker) }
    single<ChannelService> { ChannelServiceClient(invoker) }
    single<FollowUpService> { FollowUpServiceClient(invoker) }
    single<FollowUpTriageChannelService> { FollowUpTriageChannelServiceClient(invoker) }
    single<MacroService> { MacroServiceClient(invoker) }
    single<StaffService> { StaffServiceClient(invoker) }
    single<TagService> { TagServiceClient(invoker) }
    single<UpdateChannelStaffsService> { UpdateChannelStaffsServiceClient(invoker) }
    single<VideoCallService> { VideoCallServiceClient(videoCallInvoker) }
    single<WorkingHoursService> { WorkingHoursServiceClient(invoker) }
    single<VirtualClinicService> { VirtualClinicServiceClient(invoker) }
    single<VideoCallTranscriptionService> { VideoCallTranscriptionServiceClient(invoker) }
    single<ChannelMonitoringService> { ChannelMonitoringServiceClient(invoker) }
}
