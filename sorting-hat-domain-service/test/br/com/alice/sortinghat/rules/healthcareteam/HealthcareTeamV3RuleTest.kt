package br.com.alice.sortinghat.rules.healthcareteam

import br.com.alice.common.DistanceUtils
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.common.Brand
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.RoutingHistoryModel
import br.com.alice.data.layer.models.RoutingRuleContent
import br.com.alice.data.layer.models.RoutingRuleValidation
import br.com.alice.data.layer.models.RoutingRuleValidationOperator
import br.com.alice.data.layer.models.RoutingRuleValidationType
import br.com.alice.data.layer.models.TierType
import br.com.alice.sortinghat.RuleTestHelper
import br.com.alice.sortinghat.models.RoutingResult
import br.com.alice.sortinghat.models.balances.healthCareTeam.HealthcareTeamBalance
import br.com.alice.sortinghat.models.input.HealthcareTeamModel
import br.com.alice.sortinghat.models.inputs.healthCareTeam.HealthcareTeamInput
import br.com.alice.sortinghat.models.output.HealthcareTeamOutputModel
import br.com.alice.sortinghat.rules.NotAppliedRuleException
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.spyk
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDateTime
import kotlin.test.Test

class HealthCareTeamV3RuleTest : RuleTestHelper() {

    private val rule = HealthcareTeamV3Rule(routingHistoryDataService)

    private val source = HealthcareTeamModel(
        id = "someId",
        personId = personId
    )

    private val officeLatitude = -23.********
    private val officeLongitude = -46.********

    @Test
    fun `#ruleOrder returns an integer represents the order of execution`() {
        val result = rule.ruleOrder()
        assertThat(result).isEqualTo(0)
    }

    @Test
    fun `#ruleVersion returns an integer represents the rule version`() {
        val result = rule.ruleVersion()
        assertThat(result).isEqualTo(0)
    }

    @Nested
    inner class AdultTest {

        private val somePerson = TestModelFactory.buildPerson(
            dateOfBirth = LocalDateTime.now().minusYears(20),
            addresses = listOf(
                TestModelFactory.buildAddress(
                    city = "city ONE",
                    lat = officeLatitude,
                    lng = officeLongitude
                )
            ),
            sex = Sex.FEMALE
        )

        private val somePersonWithoutLatLng = TestModelFactory.buildPerson(
            dateOfBirth = LocalDateTime.now().minusYears(20),
            addresses = listOf(
                TestModelFactory.buildAddress(
                    city = "city ONE",
                    lat = null,
                    lng = null
                )
            )
        )

        private val someRisk = TestModelFactory.buildRisk()

        private val routingRules = listOf(
            TestModelFactory.buildRoutingRule(
                name = "rule_1",
                type = RoutingHistoryModel.HEALTHCARE_TEAM,
                content = RoutingRuleContent(
                    result = listOf("1_LESS_POPULATED_LEAN_DEFAULT_ID", "2_LEAN_DEFAULT_ID"),
                    priority = 0,
                    validations = listOf(
                        RoutingRuleValidation(
                            type = RoutingRuleValidationType.AGE,
                            value = 12,
                            operator = RoutingRuleValidationOperator.GREATER_EQUALS
                        ),
                        RoutingRuleValidation(
                            type = RoutingRuleValidationType.CITY,
                            value = "City one",
                            operator = RoutingRuleValidationOperator.EQUALS
                        )
                    )
                )
            ),
            TestModelFactory.buildRoutingRule(
                name = "rule_2",
                type = RoutingHistoryModel.HEALTHCARE_TEAM,
                content = RoutingRuleContent(
                    result = listOf("3_LEAN_DEFAULT_ID"),
                    priority = 1,
                    validations = listOf(
                        RoutingRuleValidation(
                            type = RoutingRuleValidationType.AGE,
                            value = 12,
                            operator = RoutingRuleValidationOperator.GREATER_EQUALS
                        ),
                        RoutingRuleValidation(
                            type = RoutingRuleValidationType.CITY,
                            value = "City two",
                            operator = RoutingRuleValidationOperator.EQUALS
                        ),
                        RoutingRuleValidation(
                            type = RoutingRuleValidationType.TIER,
                            value = "TIER_1",
                            operator = RoutingRuleValidationOperator.EQUALS
                        )
                    )
                )
            )
        )

        private val adultCityRoutingRuleWithTwoTeams = listOf(
            TestModelFactory.buildRoutingRule(
                name = "rule_1",
                type = RoutingHistoryModel.HEALTHCARE_TEAM,
                content = RoutingRuleContent(
                    result = listOf("3_LEAN_DEFAULT_ID", "2_LEAN_DEFAULT_ID"),
                    priority = 1,
                    validations = listOf(
                        RoutingRuleValidation(
                            type = RoutingRuleValidationType.AGE,
                            value = 12,
                            operator = RoutingRuleValidationOperator.GREATER_EQUALS
                        ),
                        RoutingRuleValidation(
                            type = RoutingRuleValidationType.CITY,
                            value = "City one",
                            operator = RoutingRuleValidationOperator.EQUALS
                        )
                    )
                )
            )
        )

        private val input = HealthcareTeamInput(
            personTeamAssociation = null,
            balances = healthcareTeamBalances,
            source = source,
            person = somePerson,
            risk = someRisk.riskDescription,
            routingRules = routingRules
        )

        @Test
        fun `#modelType returns HEALTHCARE_TEAM as type`() {
            val result = rule.modelType()
            assertThat(result).isEqualTo(RoutingHistoryModel.HEALTHCARE_TEAM)
        }

        @Test
        fun `#logic returns less busy lean for routing rule 1`() = runBlocking<Unit> {
            withFeatureFlag(FeatureNamespace.SORTING_HAT, "should_balance_according_sex", false) {
                val input = input.copy(person = somePersonWithoutLatLng)

                val expected = RoutingResult(
                    outputModels = listOf(HealthcareTeamOutputModel(id = "1_LESS_POPULATED_LEAN_DEFAULT_ID")),
                    ruleName = "rule_1"
                )

                val result = rule.logic(input)
                assertThat(result).isEqualTo(expected)
            }
        }

        @Test
        fun `#logic returns lean matched in routing rule 2`() = runBlocking<Unit> {
            val somePerson = somePersonWithoutLatLng.copy(
                addresses = listOf(TestModelFactory.buildAddress(city = "CITY TWO", lat = null, lng = null)),
                productInfo = TestModelFactory.buildProductInfo(tierType = TierType.TIER_1)
            )

            val input = input.copy(person = somePerson)

            val expected = RoutingResult(
                outputModels = listOf(HealthcareTeamOutputModel(id = "3_LEAN_DEFAULT_ID")),
                ruleName = "rule_2"
            )

            val result = rule.logic(input)
            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `#logic returns the same lean when person already has one associated`() = runBlocking<Unit> {
            val inputWithTeamAssociation = input.copy(personTeamAssociation = personTeamAssociation)

            val expected = RoutingResult(
                outputModels = listOf(HealthcareTeamOutputModel(id = personTeamAssociation.healthcareTeamId.toString())),
                ruleName = "HealthcareTeamV3Rule"
            )

            val result = rule.logic(inputWithTeamAssociation)
            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `#logic returns exception when person is not legal person`() = runBlocking<Unit> {
            val ofLegalPerson = somePersonWithoutLatLng.copy(dateOfBirth = LocalDateTime.now().minusYears(3))
            val inputWithOfLegalPerson = input.copy(person = ofLegalPerson)

            assertThatThrownBy {
                runBlocking { rule.logic(inputWithOfLegalPerson) }
            }.isInstanceOf(NotAppliedRuleException::class.java)
        }

        @Test
        fun `#logic returns less busy lean when association already exists but request force process`() =
            runBlocking<Unit> {
                withFeatureFlag(FeatureNamespace.SORTING_HAT, "should_balance_according_sex", false) {
                    val expected = RoutingResult(
                        outputModels = listOf(HealthcareTeamOutputModel(id = "1_LESS_POPULATED_LEAN_DEFAULT_ID")),
                        ruleName = "rule_1"
                    )

                    val input = input.copy(
                        source = source.copy(force = true),
                        personTeamAssociation = personTeamAssociation,
                        person = somePersonWithoutLatLng
                    )

                    val result = rule.logic(input)
                    assertThat(result).isEqualTo(expected)
                }
            }

        @Test
        fun `#logic returns lean matched in routing by priority`() = runBlocking<Unit> {
            val routingRules = listOf(
                TestModelFactory.buildRoutingRule(
                    name = "rule_1",
                    type = RoutingHistoryModel.HEALTHCARE_TEAM,
                    content = RoutingRuleContent(
                        result = listOf("1_LESS_POPULATED_LEAN_DEFAULT_ID", "2_LEAN_DEFAULT_ID"),
                        priority = 10,
                        validations = listOf(
                            RoutingRuleValidation(
                                type = RoutingRuleValidationType.AGE,
                                value = 12,
                                operator = RoutingRuleValidationOperator.GREATER_EQUALS
                            ),
                            RoutingRuleValidation(
                                type = RoutingRuleValidationType.CITY,
                                value = "City one",
                                operator = RoutingRuleValidationOperator.EQUALS
                            )
                        )
                    )
                ),
                TestModelFactory.buildRoutingRule(
                    name = "rule_2",
                    type = RoutingHistoryModel.HEALTHCARE_TEAM,
                    content = RoutingRuleContent(
                        result = listOf("3_LEAN_DEFAULT_ID"),
                        priority = 1,
                        validations = listOf()
                    )
                )
            )

            val input = input.copy(routingRules = routingRules, person = somePersonWithoutLatLng)

            val expected = RoutingResult(
                outputModels = listOf(HealthcareTeamOutputModel(id = "3_LEAN_DEFAULT_ID")),
                ruleName = "rule_2"
            )

            val result = rule.logic(input)
            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `#logic returns more closer lean matched in routing by priority`() = mockDistances {
            val input = input.copy(routingRules = adultCityRoutingRuleWithTwoTeams)

            val expected = RoutingResult(
                outputModels = listOf(HealthcareTeamOutputModel(id = "3_LEAN_DEFAULT_ID")),
                ruleName = "rule_1"
            )

            val result = rule.logic(input)
            assertThat(result).isEqualTo(expected)

            verify(exactly = 2) { DistanceUtils.distanceInMeters(any(), any(), any(), any()) }
        }

        @Test
        fun `#logic returns more closer lean matched in routing when all teams are full`() = mockDistances {
            val input = input.copy(
                routingRules = adultCityRoutingRuleWithTwoTeams,
                balances = healthcareTeamBalancesWithAllFullTeam
            )

            val expected = RoutingResult(
                outputModels = listOf(HealthcareTeamOutputModel(id = "3_LEAN_DEFAULT_ID")),
                ruleName = "rule_1"
            )

            val result = rule.logic(input)
            assertThat(result).isEqualTo(expected)

            verify(exactly = 2) { DistanceUtils.distanceInMeters(any(), any(), any(), any()) }
        }

        @Test
        fun `#logic returns default lean matched in routing when person doest have lat lng`() = runBlocking<Unit> {
            val input = input.copy(
                routingRules = adultCityRoutingRuleWithTwoTeams,
                balances = healthcareTeamBalancesWithOneFullTeam,
                person = somePersonWithoutLatLng
            )

            val expected = RoutingResult(
                outputModels = listOf(HealthcareTeamOutputModel(id = "2_LEAN_DEFAULT_ID")),
                ruleName = "rule_1"
            )

            val result = rule.logic(input)
            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `#logic returns matched from BRAND`() = runBlocking<Unit> {
            withFeatureFlag(FeatureNamespace.SORTING_HAT, "should_balance_according_sex", false) {
                val somePersonDuquesa = somePersonWithoutLatLng.copy(
                    productInfo = TestModelFactory.buildProductInfo(brand = Brand.DUQUESA)
                )
                val routingRules = listOf(
                    TestModelFactory.buildRoutingRule(
                        name = "rule_1",
                        type = RoutingHistoryModel.HEALTHCARE_TEAM,
                        content = RoutingRuleContent(
                            result = listOf("1_LESS_POPULATED_LEAN_DEFAULT_ID", "2_LEAN_DEFAULT_ID"),
                            priority = 0,
                            validations = listOf(
                                RoutingRuleValidation(
                                    type = RoutingRuleValidationType.BRAND,
                                    value = "DUQUESA",
                                    operator = RoutingRuleValidationOperator.EQUALS
                                )
                            )
                        )
                    ),
                    TestModelFactory.buildRoutingRule(
                        name = "rule_2",
                        type = RoutingHistoryModel.HEALTHCARE_TEAM,
                        content = RoutingRuleContent(
                            result = listOf("3_LEAN_DEFAULT_ID"),
                            priority = 1,
                            validations = listOf()
                        )
                    )
                )

                val input = input.copy(routingRules = routingRules, person = somePersonDuquesa)

                val expected = RoutingResult(
                    outputModels = listOf(HealthcareTeamOutputModel(id = "1_LESS_POPULATED_LEAN_DEFAULT_ID")),
                    ruleName = "rule_1"
                )

                val result = rule.logic(input)
                assertThat(result).isEqualTo(expected)
            }
        }

        @Test
        fun `#logic returns not matched from BRAND`() = runBlocking<Unit> {
            val somePersonDuquesa = somePersonWithoutLatLng.copy(
                productInfo = TestModelFactory.buildProductInfo(brand = Brand.ALICE)
            )
            val routingRules = listOf(
                TestModelFactory.buildRoutingRule(
                    name = "rule_1",
                    type = RoutingHistoryModel.HEALTHCARE_TEAM,
                    content = RoutingRuleContent(
                        result = listOf("1_LESS_POPULATED_LEAN_DEFAULT_ID", "2_LEAN_DEFAULT_ID"),
                        priority = 0,
                        validations = listOf(
                            RoutingRuleValidation(
                                type = RoutingRuleValidationType.BRAND,
                                value = "DUQUESA",
                                operator = RoutingRuleValidationOperator.EQUALS
                            )
                        )
                    )
                ),
                TestModelFactory.buildRoutingRule(
                    name = "rule_2",
                    type = RoutingHistoryModel.HEALTHCARE_TEAM,
                    content = RoutingRuleContent(
                        result = listOf("3_LEAN_DEFAULT_ID"),
                        priority = 1,
                        validations = listOf()
                    )
                )
            )

            val input = input.copy(routingRules = routingRules, person = somePersonDuquesa)

            val expected = RoutingResult(
                outputModels = listOf(HealthcareTeamOutputModel(id = "3_LEAN_DEFAULT_ID")),
                ruleName = "rule_2"
            )

            val result = rule.logic(input)
            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `#logic returns matched if contains BRAND`() = runBlocking<Unit> {
            withFeatureFlag(FeatureNamespace.SORTING_HAT, "should_balance_according_sex", false) {
                val somePersonDuquesa = somePersonWithoutLatLng.copy(
                    productInfo = TestModelFactory.buildProductInfo(brand = Brand.DUQUESA)
                )
                val routingRules = listOf(
                    TestModelFactory.buildRoutingRule(
                        name = "rule_1",
                        type = RoutingHistoryModel.HEALTHCARE_TEAM,
                        content = RoutingRuleContent(
                            result = listOf("1_LESS_POPULATED_LEAN_DEFAULT_ID", "2_LEAN_DEFAULT_ID"),
                            priority = 0,
                            validations = listOf(
                                RoutingRuleValidation(
                                    type = RoutingRuleValidationType.BRAND,
                                    value = listOf("DUQUESA", "ALICE"),
                                    operator = RoutingRuleValidationOperator.CONTAINS
                                )
                            )
                        )
                    ),
                    TestModelFactory.buildRoutingRule(
                        name = "rule_2",
                        type = RoutingHistoryModel.HEALTHCARE_TEAM,
                        content = RoutingRuleContent(
                            result = listOf("3_LEAN_DEFAULT_ID"),
                            priority = 1,
                            validations = listOf()
                        )
                    )
                )

                val input = input.copy(routingRules = routingRules, person = somePersonDuquesa)

                val expected = RoutingResult(
                    outputModels = listOf(HealthcareTeamOutputModel(id = "1_LESS_POPULATED_LEAN_DEFAULT_ID")),
                    ruleName = "rule_1"
                )

                val result = rule.logic(input)
                assertThat(result).isEqualTo(expected)
            }
        }

        @Test
        fun `#logic returns matched if not contains BRAND`() = runBlocking<Unit> {
            val somePersonDuquesa = somePersonWithoutLatLng.copy(
                productInfo = TestModelFactory.buildProductInfo(brand = Brand.ALICE_DUQUESA)
            )
            val routingRules = listOf(
                TestModelFactory.buildRoutingRule(
                    name = "rule_1",
                    type = RoutingHistoryModel.HEALTHCARE_TEAM,
                    content = RoutingRuleContent(
                        result = listOf("1_LESS_POPULATED_LEAN_DEFAULT_ID", "2_LEAN_DEFAULT_ID"),
                        priority = 0,
                        validations = listOf(
                            RoutingRuleValidation(
                                type = RoutingRuleValidationType.BRAND,
                                value = listOf("DUQUESA", "ALICE"),
                                operator = RoutingRuleValidationOperator.CONTAINS
                            )
                        )
                    )
                ),
                TestModelFactory.buildRoutingRule(
                    name = "rule_2",
                    type = RoutingHistoryModel.HEALTHCARE_TEAM,
                    content = RoutingRuleContent(
                        result = listOf("3_LEAN_DEFAULT_ID"),
                        priority = 1,
                        validations = listOf()
                    )
                )
            )

            val input = input.copy(routingRules = routingRules, person = somePersonDuquesa)

            val expected = RoutingResult(
                outputModels = listOf(HealthcareTeamOutputModel(id = "3_LEAN_DEFAULT_ID")),
                ruleName = "rule_2"
            )

            val result = rule.logic(input)
            assertThat(result).isEqualTo(expected)
        }

        @TestInstance(TestInstance.Lifecycle.PER_CLASS)
        @Nested
        inner class RecommendationBasedInSexTests {
            private fun testParams() = listOf(
                arrayOf(
                    "return recommendations with different staff genders when member is female",
                    healthcareTeamBalancesWithSexInformation,
                    somePerson,
                    listOf(
                        HealthcareTeamOutputModel(id = "1_LEAN_DEFAULT_ID"),
                        HealthcareTeamOutputModel(id = "3_LEAN_DEFAULT_ID"),
                        HealthcareTeamOutputModel(id = "6_LEAN_DEFAULT_ID"),
                        HealthcareTeamOutputModel(id = "4_LEAN_DEFAULT_ID")
                    )
                ),
                arrayOf(
                    "return correct recommendations when member is male",
                    healthcareTeamBalancesWithSexInformation,
                    somePerson.copy(sex = Sex.MALE),
                    listOf(
                        HealthcareTeamOutputModel(id = "1_LEAN_DEFAULT_ID"),
                        HealthcareTeamOutputModel(id = "2_LEAN_DEFAULT_ID"),
                        HealthcareTeamOutputModel(id = "5_LEAN_DEFAULT_ID")
                    )
                ),
                arrayOf(
                    "return default recommendations when member is female and all femaleCount is bigger than 60 percent",
                    healthcareTeamBalancesWithSexInformation.map { it.copy(femaleCount = 5) },
                    somePerson,
                    listOf(
                        HealthcareTeamOutputModel(id = "1_LEAN_DEFAULT_ID"),
                        HealthcareTeamOutputModel(id = "2_LEAN_DEFAULT_ID"),
                        HealthcareTeamOutputModel(id = "5_LEAN_DEFAULT_ID"),
                        HealthcareTeamOutputModel(id = "3_LEAN_DEFAULT_ID"),
                        HealthcareTeamOutputModel(id = "4_LEAN_DEFAULT_ID"),
                        HealthcareTeamOutputModel(id = "6_LEAN_DEFAULT_ID")
                    )
                ),
                arrayOf(
                    "return default recommendations when staff is null",
                    healthcareTeamBalancesWithSexInformation.map { it.copy(physicianStaffGender = null) },
                    somePerson,
                    listOf(
                        HealthcareTeamOutputModel(id = "1_LEAN_DEFAULT_ID"),
                        HealthcareTeamOutputModel(id = "3_LEAN_DEFAULT_ID"),
                        HealthcareTeamOutputModel(id = "4_LEAN_DEFAULT_ID"),
                        HealthcareTeamOutputModel(id = "6_LEAN_DEFAULT_ID")
                    )
                ),
                arrayOf(
                    "return default recommendations when member is female and femaleCount and maleCount are 0",
                    healthcareTeamBalancesWithSexInformation.map { it.copy(femaleCount = 0, maleCount = 0) },
                    somePerson,
                    listOf(
                        HealthcareTeamOutputModel(id = "1_LEAN_DEFAULT_ID"),
                        HealthcareTeamOutputModel(id = "2_LEAN_DEFAULT_ID"),
                        HealthcareTeamOutputModel(id = "5_LEAN_DEFAULT_ID"),
                        HealthcareTeamOutputModel(id = "3_LEAN_DEFAULT_ID"),
                        HealthcareTeamOutputModel(id = "4_LEAN_DEFAULT_ID"),
                        HealthcareTeamOutputModel(id = "6_LEAN_DEFAULT_ID")
                    )
                )
            )

            @ParameterizedTest(name = "{0}")
            @MethodSource("testParams")
            fun `#logic returns more closer lean with recommendations based in sex or gender`(
                testName: String,
                balances: List<HealthcareTeamBalance>,
                person: Person,
                expectedOutput: List<HealthcareTeamOutputModel>
            ) = mockDistances {
                withFeatureFlag(FeatureNamespace.SORTING_HAT, "should_balance_according_sex", true) {
                    val adultCityRoutingRuleWithMultipleTeams = listOf(
                        TestModelFactory.buildRoutingRule(
                            name = "rule_1",
                            type = RoutingHistoryModel.HEALTHCARE_TEAM,
                            content = RoutingRuleContent(
                                result = listOf(
                                    "1_LEAN_DEFAULT_ID",
                                    "2_LEAN_DEFAULT_ID",
                                    "3_LEAN_DEFAULT_ID",
                                    "4_LEAN_DEFAULT_ID",
                                    "5_LEAN_DEFAULT_ID",
                                    "6_LEAN_DEFAULT_ID"
                                ),
                                priority = 1,
                                validations = listOf(
                                    RoutingRuleValidation(
                                        type = RoutingRuleValidationType.CITY,
                                        value = "City one",
                                        operator = RoutingRuleValidationOperator.EQUALS
                                    )
                                )
                            )
                        )
                    )
                    val input = HealthcareTeamInput(
                        personTeamAssociation = null,
                        balances = balances,
                        source = source,
                        person = person,
                        risk = null,
                        routingRules = adultCityRoutingRuleWithMultipleTeams
                    )

                    val expected = RoutingResult(
                        outputModels = expectedOutput,
                        ruleName = "rule_1"
                    )

                    val result = rule.logic(input)
                    assertThat(result).isEqualTo(expected)

                    verify(exactly = 6) { DistanceUtils.distanceInMeters(any(), any(), any(), any()) }
                }
            }

            @Test
            fun `#logic returns default recommendations when there are less than 3 teams`() = mockDistances {
                withFeatureFlag(FeatureNamespace.SORTING_HAT, "should_balance_according_sex", true) {
                    val balances = listOf(healthcareTeamBalancesWithSexInformation[0])
                    val expectedOutput = listOf(HealthcareTeamOutputModel(id = "1_LEAN_DEFAULT_ID"))

                    val adultCityRoutingRuleWithMultipleTeams = listOf(
                        TestModelFactory.buildRoutingRule(
                            name = "rule_1",
                            type = RoutingHistoryModel.HEALTHCARE_TEAM,
                            content = RoutingRuleContent(
                                result = listOf(
                                    "1_LEAN_DEFAULT_ID",
                                    "2_LEAN_DEFAULT_ID",
                                    "3_LEAN_DEFAULT_ID",
                                    "4_LEAN_DEFAULT_ID",
                                    "5_LEAN_DEFAULT_ID",
                                    "6_LEAN_DEFAULT_ID"
                                ),
                                priority = 1,
                                validations = listOf(
                                    RoutingRuleValidation(
                                        type = RoutingRuleValidationType.CITY,
                                        value = "City one",
                                        operator = RoutingRuleValidationOperator.EQUALS
                                    )
                                )
                            )
                        )
                    )
                    val input = HealthcareTeamInput(
                        personTeamAssociation = null,
                        balances = balances,
                        source = source,
                        person = somePerson,
                        risk = null,
                        routingRules = adultCityRoutingRuleWithMultipleTeams
                    )

                    val expected = RoutingResult(
                        outputModels = expectedOutput,
                        ruleName = "rule_1"
                    )

                    val result = rule.logic(input)
                    assertThat(result).isEqualTo(expected)

                    verify(exactly = 1) { DistanceUtils.distanceInMeters(any(), any(), any(), any()) }
                }
            }

            @Test
            fun `#logic returns error when team is full`() = mockDistances {
                withFeatureFlag(FeatureNamespace.SORTING_HAT, "should_balance_according_sex", true) {
                    val balances = listOf(healthcareTeamBalancesWithSexInformation[0].copy(maxMemberAssociation = 4))

                    val adultCityRoutingRuleWithMultipleTeams = listOf(
                        TestModelFactory.buildRoutingRule(
                            name = "rule_1",
                            type = RoutingHistoryModel.HEALTHCARE_TEAM,
                            content = RoutingRuleContent(
                                result = listOf(
                                    "1_LEAN_DEFAULT_ID",
                                    "2_LEAN_DEFAULT_ID"
                                ),
                                priority = 1,
                                validations = listOf(
                                    RoutingRuleValidation(
                                        type = RoutingRuleValidationType.CITY,
                                        value = "City one",
                                        operator = RoutingRuleValidationOperator.EQUALS
                                    )
                                )
                            )
                        )
                    )
                    val input = HealthcareTeamInput(
                        personTeamAssociation = null,
                        balances = balances,
                        source = source,
                        person = somePerson,
                        risk = null,
                        routingRules = adultCityRoutingRuleWithMultipleTeams
                    )

                    assertThatThrownBy {
                        runBlocking { rule.logic(input) }
                    }.isInstanceOf(NotAppliedRuleException::class.java)

                    verify(exactly = 1) { DistanceUtils.distanceInMeters(any(), any(), any(), any()) }
                }
            }
        }

        private fun mockDistances(testFunction: suspend () -> Unit) = runBlocking {
            mockkObject(DistanceUtils) {
                every {
                    DistanceUtils.distanceInMeters(
                        footballMuseumLatitude.toDouble(), footballMuseumLongitude.toDouble(),
                        officeLatitude, officeLongitude
                    )
                } returns 5000.0

                every {
                    DistanceUtils.distanceInMeters(
                        casaAlicelatitude.toDouble(), casaAlicelongitude.toDouble(),
                        officeLatitude, officeLongitude
                    )
                } returns 50.0

                testFunction()
            }
        }
    }

    @Nested
    inner class ChildTest {
        private val somePerson = spyk(
            TestModelFactory.buildPerson(
                dateOfBirth = LocalDateTime.now().minusYears(3),
                addresses = listOf(TestModelFactory.buildAddress(city = "city ONE"))
            )
        )

        private val somePersonWithoutLatLng = TestModelFactory.buildPerson(
            dateOfBirth = LocalDateTime.now().minusYears(3),
            addresses = listOf(
                TestModelFactory.buildAddress(
                    city = "city ONE",
                    lat = null,
                    lng = null
                )
            )
        )

        private val someRisk = TestModelFactory.buildRisk()

        private val routingRules = listOf(
            TestModelFactory.buildRoutingRule(
                name = "rule_1",
                type = RoutingHistoryModel.HEALTHCARE_TEAM,
                content = RoutingRuleContent(
                    result = listOf("1_LEAN_PEDIATRIC_ID"),
                    priority = 0,
                    validations = listOf(
                        RoutingRuleValidation(
                            type = RoutingRuleValidationType.AGE,
                            value = 12,
                            operator = RoutingRuleValidationOperator.LESS
                        ),
                        RoutingRuleValidation(
                            type = RoutingRuleValidationType.CITY,
                            value = "City one",
                            operator = RoutingRuleValidationOperator.EQUALS
                        )
                    )
                )
            ),
            TestModelFactory.buildRoutingRule(
                name = "rule_2",
                type = RoutingHistoryModel.HEALTHCARE_TEAM,
                content = RoutingRuleContent(
                    result = listOf("2_LEAN_PEDIATRIC_ID"),
                    priority = 0,
                    validations = listOf(
                        RoutingRuleValidation(
                            type = RoutingRuleValidationType.AGE,
                            value = 12,
                            operator = RoutingRuleValidationOperator.LESS
                        ),
                        RoutingRuleValidation(
                            type = RoutingRuleValidationType.CITY,
                            value = "City two",
                            operator = RoutingRuleValidationOperator.EQUALS
                        )
                    )
                )
            )
        )

        private val input = HealthcareTeamInput(
            personTeamAssociation = null,
            balances = healthcareTeamBalances,
            source = source,
            person = somePerson,
            risk = someRisk.riskDescription,
            routingRules = routingRules
        )

        @Test
        fun `#modelType returns HEALTHCARE_TEAM as type`() {
            val result = rule.modelType()
            assertThat(result).isEqualTo(RoutingHistoryModel.HEALTHCARE_TEAM)
        }

        @Test
        fun `#logic returns pediatric lean matched in routing rule 1`() = runBlocking<Unit> {
            val input = input.copy(person = somePersonWithoutLatLng)

            val expected = RoutingResult(
                outputModels = listOf(HealthcareTeamOutputModel(id = "1_LEAN_PEDIATRIC_ID")),
                ruleName = "rule_1"
            )

            val result = rule.logic(input)
            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `#logic returns pediatric lean matched in routing rule 2`() = runBlocking<Unit> {
            val somePerson = somePersonWithoutLatLng.copy(
                addresses = listOf(TestModelFactory.buildAddress(city = "CITY TWO", lat = null, lng = null)),
                productInfo = TestModelFactory.buildProductInfo(tierType = TierType.TIER_1)
            )

            val input = input.copy(person = somePerson)

            val expected = RoutingResult(
                outputModels = listOf(HealthcareTeamOutputModel(id = "2_LEAN_PEDIATRIC_ID")),
                ruleName = "rule_2"
            )

            val result = rule.logic(input)
            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `#logic returns the same lean when person already has one associated`() = runBlocking<Unit> {
            val inputWithTeamAssociation = input.copy(personTeamAssociation = personTeamAssociation)

            val expected = RoutingResult(
                outputModels = listOf(HealthcareTeamOutputModel(id = personTeamAssociation.healthcareTeamId.toString())),
                ruleName = "HealthcareTeamV3Rule"
            )

            val result = rule.logic(inputWithTeamAssociation)
            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `#logic returns exception when person is not eligible to a pediatric team`() = runBlocking<Unit> {
            val ofLegalPerson = somePersonWithoutLatLng.copy(dateOfBirth = LocalDateTime.now().minusYears(20))
            val inputWithOfLegalPerson = input.copy(person = ofLegalPerson)

            assertThatThrownBy {
                runBlocking { rule.logic(inputWithOfLegalPerson) }
            }.isInstanceOf(NotAppliedRuleException::class.java)
        }

    }

}
