package br.com.alice.sortinghat.rules.healthcareteam

import br.com.alice.common.core.extensions.classSimpleName
import br.com.alice.common.models.Sex
import br.com.alice.common.observability.setAttribute
import br.com.alice.data.layer.models.Address
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.RoutingHistoryModel
import br.com.alice.data.layer.models.RoutingRuleValidationType
import br.com.alice.data.layer.services.RoutingHistoryDataService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.sortinghat.models.balances.healthCareTeam.HealthcareTeamBalance
import br.com.alice.sortinghat.models.balances.healthCareTeam.HealthcareTeamType
import br.com.alice.sortinghat.models.inputs.healthCareTeam.HealthcareTeamInput
import br.com.alice.sortinghat.models.output.HealthcareTeamOutputModel
import br.com.alice.sortinghat.models.toRoutingResult
import br.com.alice.sortinghat.rules.NotAppliedRuleException
import br.com.alice.sortinghat.rules.Rule
import io.opentelemetry.api.trace.Span
import kotlin.collections.map

abstract class HealthcareTeamRule(
    routingHistoryDataService: RoutingHistoryDataService
) : Rule<HealthcareTeamInput, HealthcareTeamOutputModel>(routingHistoryDataService) {

    override fun modelType() = RoutingHistoryModel.HEALTHCARE_TEAM

    protected fun memberAlreadyAssociated(input: HealthcareTeamInput) =
        input.personTeamAssociation?.let {
            if (input.source.force) return@let null
            listOf(HealthcareTeamOutputModel(id = input.personTeamAssociation.healthcareTeamId.toString()))
        }?.toRoutingResult(this.classSimpleName())

    protected suspend fun applyRoutingRule(input: HealthcareTeamInput, type: HealthcareTeamType) =
        span("applyRoutingRule") { span ->
            input.routingRules.filter { rule ->
                rule.content.validations.all { validation ->
                    when (validation.type) {
                        RoutingRuleValidationType.AGE -> input.person.age
                        RoutingRuleValidationType.CITY -> input.person.addresses.first().city
                        RoutingRuleValidationType.TIER -> input.person.productInfo?.tier?.name
                        RoutingRuleValidationType.BRAND -> input.person.productInfo?.brand?.name
                        else -> null
                    }.let {
                        validation.match(it)
                    }
                }
            }.minByOrNull { it.content.priority }
                ?.let { ruleMatched ->
                    span.setAttribute("rule_match", ruleMatched.name)

                    val result = getCloserLessBusyTeam(input, type, ruleMatched.content.result)

                    span.setAttribute("result_output_ids", result.map { it.id })

                    result.toRoutingResult(ruleMatched.name)
                } ?: throw NotAppliedRuleException()
        }

    protected fun Person.isConsideredAdult() = this.age >= 12

    private fun HealthcareTeamBalance.toOutput() =
        HealthcareTeamOutputModel(id = this.value)

    private suspend fun getTeam(
        input: HealthcareTeamInput,
        type: HealthcareTeamType,
        teamIds: List<String>
    ) = input
        .balances
        .filter { filterBalance(it, type, teamIds) }
        .ifEmpty { input.balances.filter { teamIds.contains(it.value) } }
        .setRecommendations(input, false)

    private suspend fun filterBalance(
        balance: HealthcareTeamBalance,
        type: HealthcareTeamType,
        teamIds: List<String>
    ) = span("filterBalance") { span ->
        span.setBalance(balance)
        val baseFilter = balance.healthcareTeamType == type && (teamIds.contains(balance.value))

        span.setAttribute("base_filter", baseFilter)

        val filterResult = balance.maxMemberAssociation?.let { baseFilter && (balance.count + 1) < it }
            ?: baseFilter

        span.setAttribute("filter_result", filterResult)

        filterResult
    }

    private suspend fun getCloserLessBusyTeam(
        input: HealthcareTeamInput,
        type: HealthcareTeamType,
        teamIds: List<String>
    ) = span("getCloserLessBusyTeam") { span ->
        span.setInput(input, teamIds)

        getCloserTeam(input, type, teamIds)
            ?: getTeam(input, type, teamIds)
            ?: throw NotAppliedRuleException()
    }

    private suspend fun getCloserTeam(
        input: HealthcareTeamInput,
        type: HealthcareTeamType,
        teamIds: List<String>
    ) = span("getCloserBalance") { span ->
        val address = input.person.addresses.first()
        if (address.lat == null || address.lng == null) return@span null

        input
            .balances
            .map { balance -> balance.enrichWithDistance(address, span) }
            .filter { filterBalance(it, type, teamIds) }
            .setRecommendations(input, true)
    }

    private suspend fun List<HealthcareTeamBalance>.setRecommendations(input: HealthcareTeamInput, shouldSelectMinDistance: Boolean) =
        span("setRecommendations") { span ->
            span.setAttribute("filtered_balances", this.size)
            if(this.isEmpty()) return@span null

            val shouldBalanceAccordingSex = shouldUseSexBalance()
            span.setAttribute("should_balance_according_sex", shouldBalanceAccordingSex)

            if (!shouldBalanceAccordingSex) return@span setDefaultRecommendations(shouldSelectMinDistance)

            val orderedBalances = this.orderBalances()
            span.setAttribute("ordered_balances", orderedBalances.map { it.value }.toString())

            if(orderedBalances.isEmpty()) return@span null

            if(orderedBalances.size < MINIMUM_OF_TEAMS_TO_BALANCE_ACCORDING_SEX) return@span orderedBalances.map { it.toOutput() }

            val defaultRecommendation = orderedBalances.first()

            orderedBalances
                .drop(1)
                .filterByMemberSex(input.person.sex)
                .filterByStaffGender(defaultRecommendation)
                .let { filteredBalances ->
                    span.setAttribute("filtered_balances", filteredBalances.map { it.value }.toString())
                    (listOf(defaultRecommendation) + filteredBalances).map { it.toOutput() }
                }

        }

    private suspend fun List<HealthcareTeamBalance>.setDefaultRecommendations(shouldSelectMinDistance: Boolean) =
        span("setDefaultRecommendations") { span ->
            this.takeIf { shouldSelectMinDistance }
                .let { this.selectMinDistances().also { span.setAttribute("selected_min_distances", it.size) } }
                .lessBusyByPercentage()
                ?.let { listOf(it.toOutput()) }
        }

    private fun HealthcareTeamBalance.enrichWithDistance(
        address: Address,
        span: Span
    ) = this.withDistanceToMember(address.lat?.toString(), address.lng?.toString())
        .also { span.setAttribute("balance_${it.value}_distance", it.distanceToMember) }

    private fun List<HealthcareTeamBalance>.lessBusyByPercentage() =
        minByOrNull { it.occupancyPercentage() }

    private fun List<HealthcareTeamBalance>.orderByLessBusyByPercentage() =
        this.sortedBy { it.occupancyPercentage() }

    private fun List<HealthcareTeamBalance>.filterByStaffGender(defaultRecommendation: HealthcareTeamBalance) =
        this
            .filter { it.physicianStaffGender != null }
            .takeIf { it.size > 2 && defaultRecommendation.physicianStaffGender == it[0].physicianStaffGender }
            ?.let { filteredBalances ->
                filteredBalances
                    .firstOrNull { it.physicianStaffGender != defaultRecommendation.physicianStaffGender }
                    ?.let {
                        filteredBalances.toMutableList().apply {
                            remove(it)
                            add(1, it)
                        }
                    } ?: filteredBalances
            } ?: this

    private fun List<HealthcareTeamBalance>.filterByMemberSex(memberSex: Sex?) =
        memberSex.takeIf { it == Sex.FEMALE }
            ?.let {
                this.filter { it.sexPercentage(it.femaleCount) < FEMALE_MAX_PERCENTAGE }.ifEmpty { this }
            } ?: this.filter { it.sexPercentage(it.maleCount) < MALE_MAX_PERCENTAGE }.ifEmpty { this }

    private suspend fun List<HealthcareTeamBalance>.orderBalances() =
        span("orderBalances") { span ->
            this.sortedWith(
                compareBy({ it.distanceToMember }, { it.occupancyPercentage() })
            ).filter { it.occupancyPercentage() < PERCENT_FULL }
        }

    private fun shouldUseSexBalance() =
        FeatureService.get(
            namespace = FeatureNamespace.SORTING_HAT,
            key = "should_balance_according_sex",
            defaultValue = false
        )

    private companion object {
        const val MALE_MAX_PERCENTAGE = 40.0
        const val FEMALE_MAX_PERCENTAGE = 60.0
        const val PERCENT_FULL = 100
        const val MINIMUM_OF_TEAMS_TO_BALANCE_ACCORDING_SEX = 3
    }
}

fun Span.setInput(
    input: HealthcareTeamInput,
    teamIds: List<String>
) {
    this.setAttribute("person_id", input.person.id)
    this.setAttribute("risk", input.risk)
    this.setAttribute("source", input.source)
    this.setAttribute("balances_size", input.balances.size)
    this.setAttribute("routing_rules_size", input.routingRules.size)
    this.setAttribute("person_team_association", input.personTeamAssociation)
    this.setAttribute("input_team_ids_size", teamIds)
    this.setAttribute("input_team_ids", teamIds.toString())
    this.setAttribute("person_sex", input.person.sex)
}

fun Span.setBalance(
    balance: HealthcareTeamBalance
) {
    this.setAttribute("healthcare_team_id", balance.value)
    this.setAttribute("healthcare_team_type", balance.healthcareTeamType)
    this.setAttribute("count", balance.count)
    this.setAttribute("max_member_association", balance.maxMemberAssociation)
    this.setAttribute("latitude", balance.latitude ?: "null")
    this.setAttribute("latitude", balance.longitude ?: "null")
    this.setAttribute("distance_by_member", balance.distanceToMember)
    this.setAttribute("physician_staff_gender", balance.physicianStaffGender)
    this.setAttribute("female_count", balance.femaleCount)
    this.setAttribute("male_count", balance.maleCount)
}

fun List<HealthcareTeamBalance>.selectMinDistances() = this
    .fold(emptyList<HealthcareTeamBalance>()) { acc, balance ->
        when {
            acc.isEmpty() -> acc.plus(balance)
            acc.all { it.distanceToMember > balance.distanceToMember } -> listOf(balance)
            acc.all { it.distanceToMember == balance.distanceToMember } -> acc.plus(balance)
            else -> acc
        }
    }
