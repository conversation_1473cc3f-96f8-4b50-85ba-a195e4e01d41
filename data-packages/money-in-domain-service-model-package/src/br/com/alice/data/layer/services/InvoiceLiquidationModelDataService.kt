package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.client.UpdaterList
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Predicate.Companion.ContainsAnyPredicateUsage
import br.com.alice.common.service.data.dsl.Predicate.Companion.ContainsPredicateUsage
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.InvoiceLiquidationModel
import br.com.alice.data.layer.models.InvoiceLiquidationStatus
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface InvoiceLiquidationModelDataService : Service,
    Finder<InvoiceLiquidationModelDataService.FieldOptions, InvoiceLiquidationModelDataService.OrderingOptions, InvoiceLiquidationModel>,
    Getter<InvoiceLiquidationModel>,
    Updater<InvoiceLiquidationModel>,
    Adder<InvoiceLiquidationModel>,
    UpdaterList<InvoiceLiquidationModel> {

    override val namespace: String
        get() = "money_in"

    override val serviceName: String
        get() = "invoice_liquidation"

    class Id : Field.UUIDField(InvoiceLiquidationModel::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class ExternalId : Field.TextField(InvoiceLiquidationModel::externalId) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class Status : Field.TextField(InvoiceLiquidationModel::status) {
        fun eq(value: InvoiceLiquidationStatus) = Predicate.eq(this, value)

        fun diff(value: InvoiceLiquidationStatus) = Predicate.diff(this, value)
        fun inList(value: List<InvoiceLiquidationStatus>) = Predicate.inList(this, value)
    }

    class MemberInvoiceGroupIds : Field.JsonbField(InvoiceLiquidationModel::memberInvoiceGroupIds) {
        @OptIn(ContainsPredicateUsage::class)
        fun contains(value: UUID) = Predicate.contains(this, value)

        @OptIn(ContainsAnyPredicateUsage::class)
        fun containsAny(value: List<UUID>) = Predicate.containsAny(this, value)
    }

    class BillingAccountablePartyId : Field.UUIDField(InvoiceLiquidationModel::billingAccountablePartyId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class CompanyId : Field.UUIDField(InvoiceLiquidationModel::companyId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class CompanyIds : Field.UUIDField(InvoiceLiquidationModel::companyIds) {
        @OptIn(Predicate.Companion.ContainsPredicateUsage::class)
        fun contains(value: UUID) = Predicate.contains(this, value)
    }

    class SubContractIds : Field.UUIDField(InvoiceLiquidationModel::subcontractIds) {
        @OptIn(Predicate.Companion.ContainsPredicateUsage::class)
        fun contains(value: UUID) = Predicate.contains(this, value)
    }

    class DueDate : Field.DateField(InvoiceLiquidationModel::dueDate) {
        fun eq(value: java.time.LocalDate) = Predicate.eq(this, value)
        fun greaterEq(value: java.time.LocalDate) = Predicate.greaterEq(this, value)
        fun lessEq(value: java.time.LocalDate) = Predicate.lessEq(this, value)
    }

    class FieldOptions {
        val id = Id()
        val status = Status()
        val externalId = ExternalId()
        val memberInvoiceGroupIds = MemberInvoiceGroupIds()
        val billingAccountablePartyId = BillingAccountablePartyId()
        val companyId = CompanyId()
        val dueDate = DueDate()
        val companyIds = CompanyIds()
        val subContractIds = SubContractIds()

    }

    class OrderingOptions {
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun findByQuery(query: Query): Result<List<InvoiceLiquidationModel>, Throwable>

    override suspend fun add(model: InvoiceLiquidationModel): Result<InvoiceLiquidationModel, Throwable>

    override suspend fun get(id: UUID): Result<InvoiceLiquidationModel, Throwable>

    override suspend fun update(model: InvoiceLiquidationModel): Result<InvoiceLiquidationModel, Throwable>

    override suspend fun updateList(
        models: List<InvoiceLiquidationModel>,
        returnOnFailure: Boolean
    ): Result<List<InvoiceLiquidationModel>, Throwable>

    suspend fun findBySubContractId(subContractId: UUID) = find {
        where {
            this.subContractIds.contains(subContractId)
        }
    }

    suspend fun findByBillingAccountablePartyId(billingAccountablePartyId: UUID) = find {
        where {
            this.billingAccountablePartyId.eq(billingAccountablePartyId)
        }
    }
}
